{"app": {"signingConfigs": [], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS"}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "pdkfull", "srcPath": "./pdkfull", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "support", "srcPath": "./support", "targets": [{"name": "default", "applyToProducts": ["default"]}]}, {"name": "aiAppBase", "srcPath": "./aiAppBase"}, {"name": "aiAppBaseConfig", "srcPath": "./aiAppBaseConfig"}, {"name": "aiBaseSdk", "srcPath": "./aiBaseSdk"}, {"name": "aiAppBaseNetwork", "srcPath": "./aiAppBaseNetwork"}, {"name": "aiTuneBase", "srcPath": "./aiTuneBase"}, {"name": "wakeup", "srcPath": "./wakeup"}]}