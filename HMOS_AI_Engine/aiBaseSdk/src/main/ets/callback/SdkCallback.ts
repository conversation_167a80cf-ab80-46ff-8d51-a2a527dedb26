/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * SDK回调
 */
export interface SdkCallback {
    /**
     * 结果回调，当不传入可选参数sdkCallbackType时，默认回传字符串格式的报文。
     *
     * 传入的sdkCallbackType需与callbackResult的类型相对应：
     * 回传PACKET报文类型时，callbackResult应为string类型；
     * 回传FILE_DESCRIPTOR文件句柄类型时，callbackResult应为number类型。
     *
     * @param retCode 返回码
     * @param callbackResult 返回数据载体
     * @param sdkCallbackType  返回结果的类型
     */
    onResult(retCode: number, callbackResult: string | number, sdkCallbackType?: SdkCallbackType): void;

    /**
     * 在create-release生命周期内监听回调，支持多次回调。目前仅在dispatchMessage场景下有onNotify
     *
     * @param eventResult 事件回调
     */
    onNotify(eventResult: string);
}

/**
 * 回调结果的类型，目前只支持回传报文和文件句柄。
 */
export enum SdkCallbackType {
    //1.报文
    PACKET = 1,
    //2.文件句柄
    FILE_DESCRIPTOR = 2
    //3.音频
    //4.图像
}
