/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

import hilog from '@ohos.hilog';

/**
 * 日志工具类
 */
export default class LogUtil {
    private static readonly DOMAIN: number = 0x0F00;
    private static readonly MAX_HILOG_LENGTH: number = 800;
    private static readonly IS_RELEASE: boolean = true;
    private static readonly IS_DEBUG: boolean = hilog.isLoggable(LogUtil.DOMAIN, 'AIDispatcherEngineOH', hilog.LogLevel.DEBUG);
    private static readonly COLON: string = ':';
    private static readonly TAG: string = 'AIDispatcherEngineOH';

    private constructor() {
    }

    /**
     * Debug级别日志
     *
     * @param logTag 日志tag
     * @param message 日志信息
     */
    public static debug(logTag: string, message: string): void {
        if (this.IS_RELEASE && this.IS_DEBUG) {
            let log: string = logTag + LogUtil.COLON + message;
            let index: number = 0
            for (index = 0; index < Math.floor(log.length / this.MAX_HILOG_LENGTH); index++) {
                hilog.debug(this.DOMAIN, LogUtil.TAG, log.slice(this.MAX_HILOG_LENGTH * index,
                    this.MAX_HILOG_LENGTH * index + this.MAX_HILOG_LENGTH));
            }
            if (log.length !== this.MAX_HILOG_LENGTH * index) {
                hilog.debug(this.DOMAIN, LogUtil.TAG, log.slice(this.MAX_HILOG_LENGTH * index));
            }
        } else {
            console.debug(LogUtil.TAG + LogUtil.COLON + logTag + LogUtil.COLON + message);
        }
    }

    /**
     * Info级别日志
     *
     * @param logTag 日志tag
     * @param message 日志信息
     */
    public static info(logTag: string, message: string): void {
        if (this.IS_RELEASE) {
            hilog.info(this.DOMAIN, LogUtil.TAG, logTag + LogUtil.COLON + message);
        } else {
            console.info(LogUtil.TAG + LogUtil.COLON + logTag + LogUtil.COLON + message);
        }
    }

    /**
     * Warn级别日志
     *
     * @param logTag 日志tag
     * @param message 日志信息
     */
    public static warn(logTag: string, message: string): void {
        if (this.IS_RELEASE) {
            hilog.warn(this.DOMAIN, LogUtil.TAG, logTag + LogUtil.COLON + message);
        } else {
            console.warn(LogUtil.TAG + LogUtil.COLON + logTag + LogUtil.COLON + message);
        }
    }

    /**
     * Error级别日志
     *
     * @param logTag 日志tag
     * @param message 日志信息
     */
    public static error(logTag: string, message: string): void {
        if (this.IS_RELEASE) {
            hilog.error(this.DOMAIN, LogUtil.TAG, logTag + LogUtil.COLON + message);
        } else {
            console.error(LogUtil.TAG + LogUtil.COLON + logTag + LogUtil.COLON + message);
        }
    }
}
