/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import BaseSdkConstants from '../common/BaseSdkConstants';
import LogUtil from './LogUtil';

/**
 * json工具类
 */
export class JsonUtil {
    private static readonly TAG = 'JsonUtil';

    private constructor() {
    }

    /**
     * 判断字符串是否是JSON字符串
     *
     * @param str 输入字符串
     * @returns 是否是JSON字符串
     */
    public static isJSONString(str: string): boolean {
        if (typeof str === 'string') {
            try {
                let obj = JSON.parse(str);
                return obj && typeof obj === 'object';
            } catch (ex) {
                LogUtil.debug(this.TAG, `isJSONString::exception: ${ex}`);
            }
        }
        return false;
    }

    /**
     * 将目标JSON字符串转化为指定类型的对象
     *
     * @param target 目标对象
     * @param source 源JSON字符串
     * @returns 目标对象
     */
    public static parseStrToClass<T>(target: T, source: string): T {
        LogUtil.info(this.TAG, `parseStrToClass`);
        if (!source) {
            LogUtil.error(this.TAG, `parseStrToClass::source is empty`);
            return null;
        }
        if (!this.isJSONString(source)) {
            LogUtil.error(this.TAG, `parseStrToClass::source is not JSON string`);
            return null;
        }
        try {
            Object.assign(target, JSON.parse(source));
        } catch (ex) {
            LogUtil.debug(this.TAG, `parseStrToClass exception: ${JSON.stringify(ex)}`);
        }
        return target;
    }

    /**
     * 将对象转换成Json string
     *
     * @param obj 待转换的目标对象
     * @returns Json 字符串
     */
    public static parseObjToStr(obj: object): string {
        LogUtil.info(this.TAG, `parseObjToStr`);
        let jsonStr: string = BaseSdkConstants.EMPTY_STRING;
        if (obj == null) {
            LogUtil.error(this.TAG, `parseObjToStr::object is null`);
            return jsonStr;
        }
        try {
            jsonStr = JSON.stringify(obj);
        } catch (ex) {
            LogUtil.debug(this.TAG, `parseObjToStr exception: ${JSON.stringify(ex)}`);
        }
        return jsonStr;
    }
}