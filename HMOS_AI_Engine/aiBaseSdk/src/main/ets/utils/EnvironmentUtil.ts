/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import ServiceExtensionAbility from '@ohos.app.ability.ServiceExtensionAbility';
import UIAbility from '@ohos.app.ability.UIAbility';

/**
 * 环境工具类
 */
export default class EnvironmentUtil {
    private static context: ServiceExtensionAbility['context'] | UIAbility['context'];

    private constructor() {
    }

    /**
     * 获取context
     */
    public static getContext(): any {
        return this.context;
    }

    /**
     * 设置context
     */
    public static setContext(context: any): void {
        this.context = context;
    }
}