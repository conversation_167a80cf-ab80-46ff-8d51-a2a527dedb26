/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import FailedPayload from '../beans/FailedPayload';
import { DispatchParams, Session, Content, ContentData, Header} from '../beans/DispatchParams';
import BaseSdkConstants from '../common/BaseSdkConstants';
import { JsonUtil } from './JsonUtil';
import LogUtil from './LogUtil';
import Payload from '../beans/Payload';
import AiDispatchResultCode from '../common/AiDispatchResultCode';
import AiDispatchResultMessage from '../common/AiDispatchResultMessage';

/**
 * CallBackResultUtil，用于生成回调中的CallbackResult
 */
export default class CallBackResultUtil {
    private static readonly TAG: string = "CallBackResultUtil";

    private constructor() {
    }

    /**
     * 创建绑定服务的CallbackResult，用于create接口
     *
     * @return 统一报文格式的CallbackResult
     */
    public static createCallbackResult(): DispatchParams {
        let session: Session = new Session();
        session.serviceName = BaseSdkConstants.EMPTY_STRING;
        session.senderName = BaseSdkConstants.EMPTY_STRING;
        session.traceId = BaseSdkConstants.EMPTY_STRING;
        session.messageName = BaseSdkConstants.CREATE;
        session.messageVersion = BaseSdkConstants.CREATE_MESSAGE_VERSION;
        let content: Content = new Content();
        let callbackResult: DispatchParams = new DispatchParams();
        callbackResult.session = session;
        callbackResult.content = content;
        return callbackResult;
    }

    /**
     * 生成create接口中参数错误或JSON转化失败对应的callbackResult
     *
     * @param unifiedMessage callbackResult对象
     * @param serviceName 服务名
     * @param code 错误码
     * @param msg 错误信息
     * @return callbackResult
     */
    public static createFailedCallbackResult(dispatchParams: DispatchParams, serviceName: string, code: string, msg: string): string {
        if (!dispatchParams) {
            LogUtil.warn(CallBackResultUtil.TAG, `unifiedMessage is null`);
            dispatchParams = CallBackResultUtil.createCallbackResult();
        }

        // 根据入参构成FailedPayload
        let failedPayload: FailedPayload = new FailedPayload();
        failedPayload.retErrCode = code;
        failedPayload.errMsg = msg;
        failedPayload.serviceName = serviceName;

        // 构造Header，create接口的回调header是固定的
        let header: Header = new Header;
        header.name = BaseSdkConstants.CREATE_FAILED;
        header.namespace = BaseSdkConstants.EVENT;

        // 构造content
        let contentDataList: Array<ContentData> = new Array();
        let contentData: ContentData = new ContentData();
        contentData.header = header;
        contentData.payload = failedPayload;
        contentDataList.push(contentData);
        let content: Content = dispatchParams.content;
        content.contentData = contentDataList;
        dispatchParams.content = content;
        return JsonUtil.parseObjToStr(dispatchParams);
    }

    /**
     * 生成绑定服务成功时对应的callbackResult
     *
     * @param unifiedMessage callbackResult对象
     * @param serviceNames 服务名(支持多个)
     * @return callbackResult
     */
    public static bindSuccessCallbackResult(dispatchParams: DispatchParams, serviceNames: Set<string>): string {
        if (!dispatchParams) {
            LogUtil.warn(CallBackResultUtil.TAG, `unifiedMessage is null`);
            dispatchParams = CallBackResultUtil.createCallbackResult();
        }
        let header: Header = new Header();
        header.name = BaseSdkConstants.CREATE_SUCCESS;
        header.namespace = BaseSdkConstants.EVENT;
        let contentDataList: Array<ContentData> = new Array();
        for (let name of serviceNames) {
            let payload: Payload = new Payload();
            payload.serviceName = name;
            let contentData: ContentData = new ContentData();
            contentData.header = header;
            contentData.payload = payload;
            contentDataList.push(contentData);
        }
        let content: Content = dispatchParams.content;
        content.contentData = contentDataList;
        dispatchParams.content = content;
        return JsonUtil.parseObjToStr(dispatchParams);
    }

    /**
     * 生成绑定服务失败时对应的callbackResult
     *
     * @param unifiedMessage callbackResult对象
     * @param serviceNames 服务名(支持多个)
     * @return callbackResult
     */
    public static bindFailedCallbackResult(dispatchParams: DispatchParams, serviceNames: Set<string>): string {
        if (!dispatchParams) {
            LogUtil.warn(CallBackResultUtil.TAG, `unifiedMessage is null`);
            dispatchParams = CallBackResultUtil.createCallbackResult();
        }
        if (!serviceNames || serviceNames.size === 0) {
            LogUtil.info(CallBackResultUtil.TAG, `failed serviceNames is empty`);
            return CallBackResultUtil.createFailedCallbackResult(dispatchParams, BaseSdkConstants.EMPTY_STRING,
                AiDispatchResultCode.FAILED_TO_CONNECT_SPECIFIED_SERVICE_ERROR_CODE.toString(),
                AiDispatchResultMessage.FAILED_TO_CONNECT_SPECIFIED_SERVICE_ERROR);
        }
        let header: Header = new Header();
        header.name = BaseSdkConstants.CREATE_FAILED;
        header.namespace = BaseSdkConstants.EVENT;
        let contentDataList: Array<ContentData> = new Array();
        for (let name of serviceNames) {
            let failedPayload: FailedPayload = new FailedPayload();
            failedPayload.retErrCode = AiDispatchResultCode.FAILED_TO_CONNECT_SPECIFIED_SERVICE_ERROR_CODE.toString();
            failedPayload.errMsg = AiDispatchResultMessage.FAILED_TO_CONNECT_SPECIFIED_SERVICE_ERROR;
            failedPayload.serviceName = name;
            let contentData: ContentData = new ContentData();
            contentData.header = header;
            contentData.payload = failedPayload;
            contentDataList.push(contentData);
        }
        let content: Content = dispatchParams.content;
        content.contentData = contentDataList;
        dispatchParams.content = content;
        return JsonUtil.parseObjToStr(dispatchParams);
    }

    /**
     * 用于生成dispatchMessage接口调用失败时，回调中的CallBackResult
     *
     * @param dispatchMessageParam 业务消息分发的入参
     * @param errCode 错误码
     * @param errMsg 错误信息
     * @param serviceName 服务名
     * @return CallBackResult
     */
    public static getErrorCallBackResult(dispatchMessageParam: DispatchParams, errCode: number,
        errMsg: string, serviceName: string): string {
        LogUtil.info(CallBackResultUtil.TAG, "start building dispatchMessage error information");
        let callBackResult: DispatchParams = dispatchMessageParam;
        if (!callBackResult) {
            callBackResult = new DispatchParams();
        }
        if (!callBackResult.content) {
            callBackResult.content = new Content();
        }
        if (!callBackResult.content.contentData) {
            callBackResult.content.contentData = new Array<ContentData>();
        }

        // 封裝ContentData
        let header: Header = new Header();
        header.name = BaseSdkConstants.EVENT;
        header.namespace = BaseSdkConstants.DISPATCH_FAILED;
        let failedPayload: FailedPayload = new FailedPayload();
        failedPayload.retErrCode = errCode.toString();
        failedPayload.errMsg = errMsg;
        failedPayload.serviceName = serviceName;
        let contentData: ContentData = new ContentData();
        contentData.payload = failedPayload;
        contentData.header = header;
        if (callBackResult.content.contentData.length === 0) {
            callBackResult.content.contentData.push(contentData);
        } else {
            let listContent: Array<ContentData> = callBackResult.content.contentData;
            for (let data of listContent) {
                if (!data) {
                    data = new ContentData();
                }
                if (!data.header) {
                    data.header = header;
                }
                data.payload = failedPayload;
            }
            callBackResult.content.contentData = listContent;
        }

        // 封装Session
        if (!callBackResult.session) {
            let session: Session = new Session();
            session.serviceName = serviceName;
            session.senderName = BaseSdkConstants.EMPTY_STRING;
            session.traceId = BaseSdkConstants.EMPTY_STRING;
            session.messageName = BaseSdkConstants.EMPTY_STRING;
            session.messageVersion = BaseSdkConstants.CREATE_MESSAGE_VERSION;
            callBackResult.session = session;
        }
        return JsonUtil.parseObjToStr(callBackResult);
    }
}