/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import { IServiceCallback } from './IServiceCallback';

/**
 * IBaseSdkService用于IDL通信
 */
export interface IBaseSdkService extends rpc.IRemoteBroker {
    /*
     * 消息处理
     *
     * @param dispatchParams 参数载体
     * @param serviceCallback 统一SDK服务回调
     */
    processMessage(dispatchParams: string, serviceCallback: IServiceCallback): void;
}