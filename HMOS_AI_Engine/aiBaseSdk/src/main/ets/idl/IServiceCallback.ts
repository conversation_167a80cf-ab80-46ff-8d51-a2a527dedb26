/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import { SdkCallbackType } from '../callback/SdkCallback';

/**
 * IServiceCallback用于IDL通信
 */
export interface IServiceCallback extends rpc.IRemoteBroker {
    /*
    * 成功结果回调
    *
    * @param retCode 返回码
    * @param callbackResult 返回数据载体
    */
    onResult(retCode: number, callbackResult: string | number, sdkCallbackType?: SdkCallbackType): void;

    /*
    * 在create-release生命周期内监听回调，支持多次回调。目前仅在dispatchMessage场景下有onNotify
    *
    * @param eventResult 事件回调
    */
    onNotify(eventResult: string): void;
}