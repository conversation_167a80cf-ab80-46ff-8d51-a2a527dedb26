/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import { IBaseSdkService } from './IBaseSdkService';
import { IServiceCallback } from './IServiceCallback';
import { BusinessError } from '@ohos.base';
import LogUtil from '../utils/LogUtil';

/**
 * BaseSdkServiceProxy用于IDL通信
 */
export class BaseSdkServiceProxy implements IBaseSdkService {
    private static readonly DESCRIPTOR: string = "idl.IBaseSdkService";

    private static readonly COMMAND_MESSAGE_PROCESS: number = 0;

    private remote: rpc.IRemoteObject;

    private static readonly SLICE_NUMBER: number = 10000;

    private static readonly WRITE_STRING_CAPACITY: number = 480 * 1024;

    private static readonly WRITE_STRING_THRESHOLD: number = 5;

    private static readonly WRITE_STRING_MAX_SIZE: number = 12;

    private readonly TAG: string = 'BaseSdkServiceProxy';

    /*
     * 构造器
     *
     * @param remote IRemoteObject接口
     */
    public constructor(remote: rpc.IRemoteObject) {
        this.remote = remote;
    }

    /*
     * 转换IRemoteObject
     *
     * @return IRemoteObject接口
     */
    public asObject(): rpc.IRemoteObject {
        return this.remote;
    }

    /*
     * 消息处理
     *
     * @param dispatchParams 参数载体
     * @param serviceCallback 统一SDK服务回调
     */
    public processMessage(dispatchParams: string, serviceCallback: IServiceCallback): void {
        LogUtil.info(this.TAG,  `=== ${this.remote} === ${JSON.stringify(this.remote)}`);
        let data = rpc.MessageSequence.create();
        let option = new rpc.MessageOption();
        let reply = rpc.MessageSequence.create();

        data.writeInterfaceToken(BaseSdkServiceProxy.DESCRIPTOR);
        let strLength: number = dispatchParams.length;
        let numChunks: number = Math.ceil(strLength / BaseSdkServiceProxy.SLICE_NUMBER);
        // 1、writeString单次写入要小于40kb，大于200kb要设置容量大小，
        // 2、writeRawData容量大，但是要求传入字节数组，加上转换时间，整体时延更大(60kb proxy->stub约250ms)
        // 3、考虑到直接计算字节数可能会出现2中的情况，同样影响性能，所以采用以长度而不是字节分割字符串的方式
        // 4、10000个字符按每个字符占用最多4个字节计算，最多占用39KB，因此直接使用10000作为分割点，每10000字符调用一次writeString
        // 5、writeString极限为500kb，最多分割12次，因此将写入控制在12次(含12次)以内
        if (numChunks > BaseSdkServiceProxy.WRITE_STRING_MAX_SIZE) {
            LogUtil.error(this.TAG, `dispatchParams size is larger than 500KB, failed to writeString`);
            return;
        }
        if (numChunks >= BaseSdkServiceProxy.WRITE_STRING_THRESHOLD) {
            LogUtil.warn(this.TAG, `dispatchParams size is larger than 200KB, need to setCapacity`);
            data.setCapacity(BaseSdkServiceProxy.WRITE_STRING_CAPACITY);
        }
        data.writeInt(numChunks);
        let index: number = 0;
        while (index < strLength) {
            data.writeString(dispatchParams.slice(index, index + BaseSdkServiceProxy.SLICE_NUMBER));
            index += BaseSdkServiceProxy.SLICE_NUMBER;
        }
        try{
            data.writeRemoteObject(serviceCallback.asObject());
        } catch(error) {
            let e: BusinessError = error as BusinessError;
            LogUtil.error(this.TAG, `write remote object fail, errorCode: ${e.code}, errorMessage: ${e.message}`);
        }

        try {
            this.remote.sendMessageRequest(BaseSdkServiceProxy.COMMAND_MESSAGE_PROCESS, data, reply, option).then(res => {
                LogUtil.info(this.TAG, `sendMessageRequest res: ${JSON.stringify(res)}`);
            });
            reply.readException();
        } catch(error) {
            let e: BusinessError = error as BusinessError;
            LogUtil.error(this.TAG, `sendMessageRequest failed, errorCode: ${e.code}, errorMessage: ${e.message}`);
        } finally {
            data.reclaim();
            reply.reclaim();
        }
    }
}