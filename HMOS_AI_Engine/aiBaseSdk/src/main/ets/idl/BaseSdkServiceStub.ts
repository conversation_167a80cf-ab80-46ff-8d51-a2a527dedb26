/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import { BaseSdkServiceProxy } from './BaseSdkServiceProxy';
import { IBaseSdkService } from './IBaseSdkService';
import { IServiceCallback } from './IServiceCallback';
import { ServiceCallbackStub } from './ServiceCallbackStub';
import hilog from '@ohos.hilog';
import LogUtil from '../utils/LogUtil';
import { BusinessError } from '@ohos.base';
/**
 * BaseSdkServiceStub用于IDL通信
 */
export abstract class BaseSdkServiceStub extends rpc.RemoteObject implements IBaseSdkService {
    private static readonly DESCRIPTOR: string = "idl.IBaseSdkService";

    private static readonly COMMAND_MESSAGE_PROCESS: number = 0;

    private static readonly EMPTY_STRING: string = '';

    //readInt为报文分割次数，目前最大支持传输报文约为640KB，分割次数最大为32，取35为上限
    private static readonly MAX_READ_INT: number = 35;

    /*
     * 构造器
     *
     * @param descriptor 描述符
     */
    public constructor(descriptor: string) {
        super(descriptor);
    }

    /*
     * 转换IRemoteObject
     *
     * @return IRemoteObject接口
     */
    public asObject(): rpc.IRemoteObject {
        return this;
    }

    /*
     * 转换Interface
     *
     * @param object IRemoteObject对象
     * @return IBaseSdkService接口
     */
    public static asInterface(object: rpc.IRemoteObject): IBaseSdkService {
        if (object == null) {
            return null;
        }

        let result: IBaseSdkService = null;
        let broker: rpc.IRemoteBroker = object.queryLocalInterface(BaseSdkServiceStub.DESCRIPTOR);
        if (broker != null) {
            result = <IBaseSdkService> broker;
        } else {
            result = new BaseSdkServiceProxy(object);
        }
        return result;
    }

    /*
     * 远程消息请求
     *
     * @param code 请求码
     * @param data 请求数据
     * @param reply 返回数据
     * @param option 可选项
     * @return boolean | Promise<boolean> 返回结果
     */
    public onRemoteMessageRequest(code: number, data: rpc.MessageSequence, reply: rpc.MessageSequence,
        option: rpc.MessageOption): boolean | Promise<boolean> {
        let token: string = data.readInterfaceToken();
        if (BaseSdkServiceStub.DESCRIPTOR !== token) {
            return false;
        }
        switch (code) {
            case BaseSdkServiceStub.COMMAND_MESSAGE_PROCESS: {
                let count: number = data.readInt();
                if(count > BaseSdkServiceStub.MAX_READ_INT) {
                    LogUtil.error('BaseSdkServiceStub', 'read int is too large, invalid data');
                    return false;
                }
                let arr: Array<string> = new Array();
                while (count !== 0) {
                    arr.push(data.readString());
                    count--;
                }
                let dispatchParams: string = arr.join(BaseSdkServiceStub.EMPTY_STRING);
                try{
                    let proxy = data.readRemoteObject();
                    let serviceCallback: IServiceCallback = ServiceCallbackStub.asInterface(proxy);
                    LogUtil.info('BaseSdkServiceStub', 'readRemoteObject is ' + proxy);
                    this.processMessage(dispatchParams, serviceCallback);
                    LogUtil.info('BaseSdkServiceStub', 'processMessage success');
                } catch(error){
                    let e: BusinessError = error as BusinessError;
                    LogUtil.error('BaseSdkServiceStub', `Ipc read remote object fail, errCode is ${e.code}, errMessage is ${e.message}`)
                }
                reply.writeNoException();
                return true;
            }
            default:
                return super.onRemoteMessageRequest(code, data, reply, option);
        }
    }

    /*
     * 消息处理
     *
     * @param dispatchParams 参数载体
     * @param serviceCallback 统一SDK服务回调
     */
    abstract processMessage(dispatchParams: string, serviceCallback: IServiceCallback): void;
}