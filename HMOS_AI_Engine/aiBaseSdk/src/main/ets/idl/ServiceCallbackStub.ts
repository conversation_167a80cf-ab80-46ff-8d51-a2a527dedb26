/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import { IServiceCallback } from './IServiceCallback';
import { ServiceCallbackProxy } from './ServiceCallbackProxy';
import { SdkCallbackType } from '../callback/SdkCallback';
import LogUtil from '../utils/LogUtil';

/**
 * ServiceCallbackStub用于IDL通信
 */
export abstract class ServiceCallbackStub extends rpc.RemoteObject implements IServiceCallback {
    private static readonly DESCRIPTOR: string = "idl.IServiceCallback";

    private static readonly COMMAND_ON_RESULT: number = 0;

    private static readonly COMMAND_ON_NOTIFY: number = 1;

    private static readonly EMPTY_STRING: string = '';

    /*
     * 构造器
     *
     * @param descriptor 描述符
     */
    public constructor(descriptor: string) {
        super(descriptor);
    }

    /*
     * 转换IRemoteObject
     *
     * @return IRemoteObject接口
     */
    public asObject(): rpc.IRemoteObject {
        return this;
    }

    /*
     * 转换Interface
     *
     * @param object IRemoteObject对象
     * @return IBaseSdkService接口
     */
    public static asInterface(object: rpc.IRemoteObject): IServiceCallback {
        if (object == null) {
            return null;
        }

        let result: IServiceCallback = null;
        let broker = object.queryLocalInterface(ServiceCallbackStub.DESCRIPTOR);
        if (broker != null) {
            result = <IServiceCallback> broker;
        } else {
            result = new ServiceCallbackProxy(object);
        }
        return result;
    }

    /*
     * 远程消息请求
     *
     * @param code 请求码
     * @param data 请求数据
     * @param reply 返回数据
     * @param option 可选项
     * @return boolean | Promise<boolean> 返回结果
     */
    public onRemoteMessageRequest(code: number, data: rpc.MessageSequence, reply: rpc.MessageSequence,
        option: rpc.MessageOption): boolean | Promise<boolean> {
        let token: string = data.readInterfaceToken();
        LogUtil.info("ServiceCallbackStub", `ServiceCallbackStub onRemoteMessageRequest code: ${code}`);
        if (ServiceCallbackStub.DESCRIPTOR !== token) {
            LogUtil.error("ServiceCallbackStub", `the token is incompatible`);
            return false;
        }
        switch (code) {
            case ServiceCallbackStub.COMMAND_ON_RESULT: {
                let retCode: number = data.readInt();
                if (data.containFileDescriptors()) {
                    let callbackResult = data.readFileDescriptor();
                    LogUtil.info("ServiceCallbackStub", `ServiceCallbackStub onRemoteMessageRequest retcode: ${retCode}, callbackResult: ${callbackResult}`);
                    this.onResult(retCode, callbackResult, SdkCallbackType.FILE_DESCRIPTOR);
                } else {
                    let callbackResult = this.getRemoteString(data);
                    LogUtil.info("ServiceCallbackStub", `ServiceCallbackStub onRemoteMessageRequest retCode:${retCode}, callbackResult: ${callbackResult}`);
                    this.onResult(retCode, callbackResult, SdkCallbackType.PACKET);
                }
                reply.writeNoException();
                return true;
            }
            case ServiceCallbackStub.COMMAND_ON_NOTIFY: {
                let eventResult = this.getRemoteString(data);
                this.onNotify(eventResult);
                reply.writeNoException();
                return true;
            }
            default:
                return super.onRemoteMessageRequest(code, data, reply, option);
        }
    }

    // 分段并拼接string
    private getRemoteString(data: rpc.MessageSequence): string {
        let count: number = data.readInt();
        let arr: Array<string> = new Array();
        while (count !== 0) {
            arr.push(data.readString());
            count--;
        }
        return arr.join(ServiceCallbackStub.EMPTY_STRING);
    }

    /*
     * 成功结果回调
     *
     * @param retCode 返回码
     * @param callbackResult 返回数据载体
     */
    abstract onResult(retCode: number, callbackResult: string | number, sdkCallbackType?: SdkCallbackType): void;

    /*
     * 在create-release生命周期内监听回调，支持多次回调。目前仅在dispatchMessage场景下有onNotify
     *
     * @param eventResult 事件回调
     */
    abstract onNotify(eventResult: string): void;
}