/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import { IServiceCallback } from './IServiceCallback';
import { BusinessError } from '@ohos.base';
import buffer from '@ohos.buffer';
import LogUtil from '../utils/LogUtil';
import { SdkCallbackType } from '../callback/SdkCallback';

/**
 * ServiceCallbackProxy用于IDL通信
 */
export class ServiceCallbackProxy implements IServiceCallback {
    private static readonly DESCRIPTOR: string = "idl.IServiceCallback";

    private static readonly COMMAND_ON_RESULT: number =  0;

    private static readonly COMMAND_ON_NOTIFY: number = 1;

    private remote: rpc.IRemoteObject;

    private static readonly SLICE_NUMBER: number = 10000;

    private static readonly WRITE_STRING_CAPACITY: number = 640 * 1024;

    private static readonly WRITE_STRING_THRESHOLD: number = 5;

    private static readonly WRITE_STRING_THRESHOLD_REAL: number = 99 *1024;

    private static readonly WRITE_STRING_MAX_SIZE: number = 12;

    private static readonly WRITE_STRING_MAX_SIZE_REAL: number = 640 * 1024;

    private readonly TAG: string = `ServiceCallbackProxy`;

    /*
     * 构造器
     *
     * @param remote IRemoteObject接口
     */
    public constructor(remote: rpc.IRemoteObject) {
        this.remote = remote;
    }

    /*
     * 转换IRemoteObject
     *
     * @return IRemoteObject接口
     */
    public asObject(): rpc.IRemoteObject {
        return this.remote;
    }

    /*
     * 成功结果回调
     *
     * @param retCode 返回码
     * @param callbackResult 返回数据载体
     */
    public onResult(retCode: number, callbackResult: string | number, sdkCallbackType?: SdkCallbackType): void {
        let data = rpc.MessageSequence.create();
        let option = new rpc.MessageOption();
        let reply = rpc.MessageSequence.create();
        data.writeInterfaceToken(ServiceCallbackProxy.DESCRIPTOR);
        data.writeInt(retCode);
        if ((!sdkCallbackType || sdkCallbackType === SdkCallbackType.PACKET) && (typeof (callbackResult) === "string")) {
            let isSuccess: boolean = this.writeStringInSegments(data, callbackResult);
            if (!isSuccess) {
                LogUtil.error(this.TAG, 'failed to writeStringInSegments');
                return;
            }
        } else if ((sdkCallbackType === SdkCallbackType.FILE_DESCRIPTOR) && (typeof (callbackResult) === "number")){
            data.writeFileDescriptor(callbackResult);
            LogUtil.info(this.TAG, `write FileDescriptor success, the file descriptor is ${callbackResult}`);
        } else {
            LogUtil.error(this.TAG, 'the callbackResult type and sdkCallbackType is incompatible, the default callbackResult ' +
                'type should be string');
            return;
        }
        try {
            this.remote.sendMessageRequest(ServiceCallbackProxy.COMMAND_ON_RESULT, data, reply, option).then(res => {
                LogUtil.info(this.TAG, `sendMessageRequest res: ${JSON.stringify(res)}`);
            });
            reply.readException();
        } catch(error) {
            let e: BusinessError = error as BusinessError;
            LogUtil.error(this.TAG, `sendMessageRequest failed, errorCode: ${e.code}, errorMessage: ${e.message}`);
        } finally {
            data.reclaim();
            reply.reclaim();
        }
    }

    /*
     * 在create-release生命周期内监听回调，支持多次回调。目前仅在dispatchMessage场景下有onNotify
     *
     * @param eventResult 事件回调
     */
    public onNotify(eventResult: string): void {
        let data = rpc.MessageSequence.create();
        let option = new rpc.MessageOption();
        let reply = rpc.MessageSequence.create();

        data.writeInterfaceToken(ServiceCallbackProxy.DESCRIPTOR);
        // 分段写入String
        let isSuccess: boolean = this.writeStringInSegments(data, eventResult);
        if (!isSuccess) {
            return;
        }
        try {
            this.remote.sendMessageRequest(ServiceCallbackProxy.COMMAND_ON_NOTIFY, data, reply, option);
            reply.readException();
        } finally {
            data.reclaim();
            reply.reclaim();
        }
    }

    private writeStringInSegments(data: rpc.MessageSequence, result: string): boolean {
        let strLength: number = result.length;
        let numChunks: number = Math.ceil(strLength / ServiceCallbackProxy.SLICE_NUMBER);
        let strSize: number = buffer.byteLength(result,'utf8');

        if ( (2*strSize) > ServiceCallbackProxy.WRITE_STRING_MAX_SIZE_REAL) {
            LogUtil.error(this.TAG, `callbackresult size is larger than limit`);
            return false;
        }

        if (strSize > ServiceCallbackProxy.WRITE_STRING_THRESHOLD_REAL) {
            LogUtil.warn(this.TAG, `callbackresult size is larger than threshold, need to setcapacity, the data capacity is ${data.getCapacity()}`);
            data.setCapacity(ServiceCallbackProxy.WRITE_STRING_CAPACITY);
        }
        data.writeInt(numChunks);
        let index: number = 0;
        while (index < strLength) {
            data.writeString(result.slice(index, index + ServiceCallbackProxy.SLICE_NUMBER));
            index += ServiceCallbackProxy.SLICE_NUMBER;
        }
        LogUtil.info(this.TAG, `writeString success`);
        return true;
    }
}