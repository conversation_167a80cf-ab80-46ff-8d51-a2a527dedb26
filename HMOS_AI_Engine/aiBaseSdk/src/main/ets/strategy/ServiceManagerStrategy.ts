/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import BaseSdkConstants from '../common/BaseSdkConstants';
import AiDataServiceManager from '../rpc/AiDataServiceManager';
import AiDispatchServiceManager from '../rpc/AiDispatchServiceManager';
import { BaseSdkServiceManager } from '../rpc/BaseSdkServiceManager';
import DataManagerService from '../rpc/DataManagerService';
import UpService from '../rpc/UpServiceExtend';
import EnvironmentUtil from '../utils/EnvironmentUtil';
import LogUtil from '../utils/LogUtil';

/**
 * 根据服务名获取对应的管理类
 */
export default class ServiceManagerStrategy {
    private static readonly TAG: string = "ServiceManagerStrategy";

    /**
     * 获取ServerManager
     */
    public static getServerManager(serverName: string): BaseSdkServiceManager {
        if (!serverName) {
            LogUtil.warn(this.TAG, "serverName is null");
            return null;
        }
        switch (serverName) {
            case BaseSdkConstants.AI_DISPATCH:
                return AiDispatchServiceManager.getInstance(EnvironmentUtil.getContext());
            case BaseSdkConstants.DATA_SERVICE:
                return AiDataServiceManager.getInstance(EnvironmentUtil.getContext());
            case BaseSdkConstants.DATA_MANAGER_SERVICE:
                return DataManagerService.getInstance(EnvironmentUtil.getContext());
            case BaseSdkConstants.UP_SERVICE:
                return UpService.getInstance(EnvironmentUtil.getContext());
            default:
                LogUtil.warn(this.TAG, "serverName is not support, serverName is " + serverName);
                return null;
        }
    }
}