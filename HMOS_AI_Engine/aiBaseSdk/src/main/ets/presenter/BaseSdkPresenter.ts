/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import common from '@ohos.app.ability.common';
import LogUtil from '../utils/LogUtil';
import EnvironmentUtil from '../utils/EnvironmentUtil';
import { JsonUtil } from '../utils/JsonUtil';
import ServiceManagerStrategy from '../strategy/ServiceManagerStrategy';
import AiDispatchServiceManager from '../rpc/AiDispatchServiceManager'
import { DispatchParams } from '../beans/DispatchParams';
import AiDispatchResultCode from '../common/AiDispatchResultCode';
import AiDispatchResultMessage from '../common/AiDispatchResultMessage';
import { SdkCallback } from '../callback/SdkCallback';
import CallBackResultUtil from '../utils/CallBackResultUtil';
import BaseSdkConstants from '../common/BaseSdkConstants';
import hilog from '@ohos.hilog';

/**
 * 统一sdk对外接口
 *
 * 包括绑定服务、业务消息分发和解绑服务三个接口
 *
 * <AUTHOR>
 * @since 2023-12-18
 */
export class BaseSdkPresenter {
    private static readonly TAG: string = 'BaseSdkPresenter';
    private static instance: BaseSdkPresenter;
    private static readonly serviceNameSet: Set<string> = new Set<string>([BaseSdkConstants.AI_DISPATCH, BaseSdkConstants.DATA_SERVICE,
        BaseSdkConstants.DATA_MANAGER_SERVICE, BaseSdkConstants.UP_SERVICE]);
    private static readonly PARAM_MAX_SIZE: number = 120000;

    /**
     * 获取BaseSdkPresenter实例
     *
     * @return BaseSdkPresenter实例
     */
    public static getInstance(): BaseSdkPresenter {
        this.instance = this.instance ?? new BaseSdkPresenter();
        return this.instance;
    }

    private constructor() {
        LogUtil.debug(BaseSdkPresenter.TAG, 'constructor');
    }

    /**
     * 绑定服务
     *
     * @param context 上下文
     * @param connectParams 绑定服务对应的服务名
     * @param sdkCallback 服务连接回调
     */
    public async create(context: common.UIAbilityContext|common.ServiceExtensionContext, connectParams: string, sdkCallback: SdkCallback): Promise<void> {
        LogUtil.info(BaseSdkPresenter.TAG, `enter bind service`);
        if (!sdkCallback) {
            LogUtil.error(BaseSdkPresenter.TAG, `sdkCallback is null`);
            return;
        }
        let callbackResult: DispatchParams = CallBackResultUtil.createCallbackResult();
        hilog.info(globalThis.domain, `BaseSdkPresenter`, `callbackResult: ${JSON.stringify(callbackResult)}`);
        if (!context || !connectParams) {
            LogUtil.error(BaseSdkPresenter.TAG, `context or connectParams is invalid`);
            let errCallbackMsg: string =
            CallBackResultUtil.createFailedCallbackResult(callbackResult, BaseSdkConstants.EMPTY_STRING,
                AiDispatchResultCode.PARAMETER_ERROR_CODE.toString(), AiDispatchResultMessage.PARAMETER_ERROR);
            sdkCallback.onResult(AiDispatchResultCode.PARAMETER_ERROR_CODE, errCallbackMsg);
            return;
        }
        EnvironmentUtil.setContext(context);
        let serviceNameList: Array<string> = JsonUtil.parseStrToClass(new Array(), connectParams);
        if (!serviceNameList || !serviceNameList.length) {
            LogUtil.error(BaseSdkPresenter.TAG, `serviceNameList is invalid`);
            let errCallbackMsg: string =
            CallBackResultUtil.createFailedCallbackResult(callbackResult, BaseSdkConstants.EMPTY_STRING,
                AiDispatchResultCode.JSON_PARSE_ERROR_CODE.toString(), AiDispatchResultMessage.JSON_PARSE_ERROR);
            sdkCallback.onResult(AiDispatchResultCode.JSON_PARSE_ERROR_CODE, errCallbackMsg);
            return;
        }

        // 过滤重复的服务，根据服务名绑定对应的服务
        let serviceNames: Array<string> = serviceNameList.filter(function (elem, index, self): boolean {
            if (self === null) {
                LogUtil.error(BaseSdkPresenter.TAG, `serviceNameList is empty`);
                return false;
            }
            return index === self.indexOf(elem);
        });

        let successService: Set<string> = new Set();
        let failedService: Set<string> = new Set();
        for (let serviceName of serviceNames) {
            LogUtil.debug(BaseSdkPresenter.TAG, `create serviceName is: ` + serviceName);
            if (!ServiceManagerStrategy.getServerManager(serviceName)) {
                LogUtil.error(BaseSdkPresenter.TAG, `failed to get the service, serviceName: ` + serviceName);
                failedService.add(serviceName);
                continue;
            }
            let binder = await ServiceManagerStrategy.getServerManager(serviceName).getBinder().catch(error => {
                LogUtil.error(BaseSdkPresenter.TAG, `bind service failed, error is : ${JsonUtil.parseObjToStr(error)}`);
            });
            if (!binder) {
                LogUtil.error(BaseSdkPresenter.TAG, `can not connect service, serviceName: ` + serviceName);
                failedService.add(serviceName);
                continue;
            }
            successService.add(serviceName);
        }
        hilog.info(globalThis.domain, "BaseSdkPresenter", `successService: ${JSON.stringify(successService)},
                                                           failedService: ${JSON.stringify(failedService)}`);
        if (failedService.size === 0 && successService.size === serviceNames.length) {
            let successCallback: string = CallBackResultUtil.bindSuccessCallbackResult(callbackResult, successService);
            sdkCallback.onResult(AiDispatchResultCode.RESULT_SUCCESS_CODE, successCallback);
        } else {
            let failedCallback: string = CallBackResultUtil.bindFailedCallbackResult(callbackResult, failedService);
            sdkCallback.onResult(AiDispatchResultCode.FAILED_TO_CONNECT_SPECIFIED_SERVICE_ERROR_CODE, failedCallback);
        }
    }

    /**
     * 业务消息分发
     *
     * @param serviceName 分发的目的服务
     * @param dispatchParams 分发的参数
     * @param sdkCallback 回调
     */
    public dispatchMessage(serviceName: string, dispatchParams: string, sdkCallback: SdkCallback) : void {
        LogUtil.info(BaseSdkPresenter.TAG, "dispatchMessage is called");
        if (!sdkCallback) {
            LogUtil.error(BaseSdkPresenter.TAG, "dispatchMessage sdkCallback is null, return");
            return;
        }
        if (!dispatchParams) {
            LogUtil.error(BaseSdkPresenter.TAG, "dispatchMessage param dispatchParams is null, return");
            sdkCallback.onResult(AiDispatchResultCode.PARAMETER_ERROR_CODE, CallBackResultUtil.getErrorCallBackResult(null,
                AiDispatchResultCode.PARAMETER_ERROR_CODE, AiDispatchResultMessage.PARAMETER_ERROR, serviceName));
            return;
        }
        let dispatchMsgParam: DispatchParams =
            JsonUtil.parseStrToClass<DispatchParams>(new DispatchParams(), dispatchParams);
        if (!dispatchMsgParam) {
            LogUtil.error(BaseSdkPresenter.TAG, "dispatchMessage param dispatchParams JSON parse fail, return");
            sdkCallback.onResult(AiDispatchResultCode.JSON_PARSE_ERROR_CODE, CallBackResultUtil.getErrorCallBackResult(
                null, AiDispatchResultCode.JSON_PARSE_ERROR_CODE, AiDispatchResultMessage.JSON_PARSE_ERROR, serviceName));
            return;
        }
        if (dispatchParams.length > BaseSdkPresenter.PARAM_MAX_SIZE) {
            LogUtil.error(BaseSdkPresenter.TAG, 'dispatchParams size is more 120000, return');
            sdkCallback.onResult(AiDispatchResultCode.PARAMETER_ERROR_CODE, CallBackResultUtil.getErrorCallBackResult(
                dispatchMsgParam, AiDispatchResultCode.PARAMETER_ERROR_CODE,
                AiDispatchResultMessage.PARAMETER_ERROR, serviceName));
            return;
        }
        if (!serviceName || !BaseSdkPresenter.serviceNameSet.has(serviceName)) {
            LogUtil.error(BaseSdkPresenter.TAG, "dispatchMessage param serviceName is not legal, return");
            sdkCallback.onResult(AiDispatchResultCode.PARAMETER_ERROR_CODE, CallBackResultUtil.getErrorCallBackResult(
                dispatchMsgParam, AiDispatchResultCode.PARAMETER_ERROR_CODE,
                AiDispatchResultMessage.PARAMETER_ERROR, serviceName));
            return;
        }
        if (!ServiceManagerStrategy.getServerManager(serviceName)) {
            LogUtil.error(BaseSdkPresenter.TAG, "serverName: " + serviceName + " service is null");
            sdkCallback.onResult(AiDispatchResultCode.NOT_CONNECT_SERVER_ERROR_CODE, CallBackResultUtil.getErrorCallBackResult(
                dispatchMsgParam, AiDispatchResultCode.NOT_CONNECT_SERVER_ERROR_CODE,
                AiDispatchResultMessage.NOT_CONNECT_SERVER_ERROR, serviceName));
            return;
        }
        ServiceManagerStrategy.getServerManager(serviceName).dispatchMessage(serviceName, dispatchParams, sdkCallback);
    }

    /**
     * 解绑服务
     *
     * @param connectParams 待解绑的服务名参数
     */
    public release(connectParams: string): Promise<void> {
        LogUtil.info(BaseSdkPresenter.TAG, "release service");
        if (!connectParams || connectParams === BaseSdkConstants.EMPTY_RELEASE_STRING) {
            AiDispatchServiceManager.getInstance(EnvironmentUtil.getContext()).unbindService();
            return;
        }
        let serviceNameList: Array<string> = JsonUtil.parseStrToClass(new Array(), connectParams);
        if (!serviceNameList || !serviceNameList.length) {
            LogUtil.error(BaseSdkPresenter.TAG, "json transfer failed when release service");
            return;
        }

        // 过滤重复的服务，根据服务名解绑对应的服务
        let serviceNames: Array<string> = serviceNameList.filter(function (elem, index, self) {
            return index === self.indexOf(elem);
        });
        for (let serviceName of serviceNames) {
            LogUtil.debug(BaseSdkPresenter.TAG, `release serviceName is: ` + serviceName);
            if (!ServiceManagerStrategy.getServerManager(serviceName)) {
                LogUtil.error(BaseSdkPresenter.TAG, "serviceName is illegal");
                continue;
            }
            ServiceManagerStrategy.getServerManager(serviceName).unbindService();
        }
    }
}