/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * 回调报文的结构体
 */
export class DispatchParams {
    /**
     * 会话定义
     */
    session: Session;

    /**
     * 数据内容定义
     */
    content: Content;
}

/**
 * 请求分发报文中的公共参数
 *
 * <AUTHOR>
 * @since 2023-07-17
 */
export class Session {
    /**
     * 消息名称
     */
    messageName: string;

    /**
     * 调用方名称
     */
    senderName: string;

    /**
     * 接收消息服务组件名
     */
    serviceName: string;

    /**
     * 跟踪ID
     */
    traceId: string;

    /**
     * 各业务指定messageName对应的消息ver
     */
    messageVersion: string;

    /**
     * 业务方业务名称
     */
    businessName: string;

    /**
     * 是否同意隐私协议
     */
    isAgreePrivacy: string;
}

/**
 * 数据内容定义
 * <AUTHOR>
 * @since 2023-07-17
 */
export class Content {
    /**
     * 回调报文content的结构体
     */
    contentData: Array<ContentData>;
}

/**
 * 请求分发报文中内容数据中的业务数据
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
export class ContentData {
    /**
     * 数据内容信息头
     */
    header: Header;

    /**
     * 数据内容信息体
     */
    payload: object;
}

/**
 * 数据内容信息头
 * <AUTHOR>
 * @since 2023-07-17
 */
export class Header {
    /**
     * SDK内事件命名空间
     */
    namespace: string;

    /**
     * SDK内内容消息具体名称
     */
    name: string;
};