/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import ServiceExtensionAbility from '@ohos.app.ability.ServiceExtensionAbility';
import UIAbility from '@ohos.app.ability.UIAbility';
import { BinderInfo } from '../beans/BinderInfo';
import LogUtil from '../utils/LogUtil';
import { BaseSdkServiceProxy } from '../idl/BaseSdkServiceProxy';
import { SdkCallback } from '../callback/SdkCallback';
import { ServiceCallbackStubImpl } from './ServiceCallbackStubImpl';
import AiDispatchResultCode from '../common/AiDispatchResultCode';
import { DispatchParams } from '../beans/DispatchParams';
import { JsonUtil } from '../utils/JsonUtil';
import AiDispatchResultMessage from '../common/AiDispatchResultMessage';
import CallBackResultUtil from '../utils/CallBackResultUtil';
import hilog from '@ohos.hilog';

/**
 * BaseServiceManager，用于service通信
 */
export abstract class BaseSdkServiceManager {
    private binderInfo: BinderInfo;
    private TAG;

    /**
     * BaseServiceManager类构造器
     */
    constructor() {
        this.TAG = this.getTag();
    }

    /**
     * 获取子类的标签用于日志打印
     *
     * @returns { string } 子类的标签
     */
    protected abstract getTag(): string;

    /**
     * 获取上下文
     *
     * @returns { ServiceExtensionAbility['context'] | UIAbility['context'] } 子类上下文
     */
    protected abstract getContext(): ServiceExtensionAbility['context'] | UIAbility['context'];

    /**
     * 获取包名
     *
     * @return 返回包名
     */
    protected abstract getBundleName(): string;

    /**
     * 获取服务名称
     *
     * @return 返回服务名称
     */
    protected abstract getAbilityName(): string;

    /**
     * 获取binder对象
     *
     * @returns { Promise<any> } binder对象
     */
    public async getBinder(): Promise<any> {
        if (!this.binderInfo) {
            this.binderInfo = new BinderInfo()
        }
        if (this.binderInfo.proxy) {
            return this.binderInfo.proxy;
        }
        return this.connectService(this.getBundleName(), this.getAbilityName());
    }

    private connectService(bundleName: string, abilityName: string): Promise<any> {
        LogUtil.info(this.TAG, `connectService`);
        const intent = {
            'bundleName': bundleName,
            'abilityName': abilityName,
        };
        return new Promise(async (resolve, reject) => {
            let connection: number = this.getContext().connectServiceExtensionAbility(intent, {
                onConnect: async (elementName, proxy) => {
                    if (proxy) {
                        LogUtil.debug(this.TAG, `onConnect: ${JsonUtil.parseObjToStr(elementName)}, connection: ${connection}`);
                        this.binderInfo.proxy = proxy;
                    }
                    resolve(proxy);
                },
                onDisconnect: async elementName => {
                    LogUtil.info(this.TAG, `onDisconnect: ${JsonUtil.parseObjToStr(elementName)}`);
                    this.binderInfo.proxy = undefined;
                    this.binderInfo.connection = undefined;
                },
                onFailed: async code => {
                    LogUtil.error(this.TAG, `onFailed called: ${code}`);
                    reject(new Error('bind service failed'));
                }
            });
            this.binderInfo.connection = connection;
        });
    }

    /**
     * binder通信发送数据
     *
     * @param { string } serviceName - 目的服务
     * @param { string } dispatchParams - 分发的参数
     * @param { SdkCallback } callback - 回调
     */
    public dispatchMessage(serviceName: string, dispatchParams: string, callback: SdkCallback): void {
        LogUtil.info(this.TAG, `dispatchMessage is called`);
        LogUtil.info(this.TAG, `===this.binderInfo.proxy: ${JSON.stringify(this.binderInfo?.proxy)} this.binderInfo.connection: ${this.binderInfo?.connection}`);
        if (!this.binderInfo?.proxy) {
            LogUtil.error(this.TAG, `not connect, ` + 'bundleName: ' +
                this.getBundleName() + 'abilityName: ' + this.getAbilityName());
            let unifiedMessage: DispatchParams = JsonUtil.parseStrToClass(new DispatchParams(), dispatchParams);
            callback.onResult(AiDispatchResultCode.NOT_CONNECT_SERVER_ERROR_CODE,
                CallBackResultUtil.getErrorCallBackResult(unifiedMessage, AiDispatchResultCode.NOT_CONNECT_SERVER_ERROR_CODE,
                    AiDispatchResultMessage.NOT_CONNECT_SERVER_ERROR, serviceName));
            return;
        }
        if (this.binderInfo.proxy) {
            console.log(`===proxy is not null`);
        }
        let serviceProxy: BaseSdkServiceProxy = new BaseSdkServiceProxy(this.binderInfo.proxy);
        try {
            serviceProxy.processMessage(dispatchParams, new ServiceCallbackStubImpl(callback));
        } catch (e) {
            LogUtil.debug(this.TAG, `sendMessageRequest error, error code is ${JsonUtil.parseObjToStr(e)}`);
        }
    }

    /**
     * 解绑服务
     *
     * @param { string } bundleName - 包名
     * @param { string } abilityName - ability名
     * @returns { Promise<void> } 解绑结果
     */
    public async unbindService(): Promise<void> {
        LogUtil.info(this.TAG, 'unbindService');
        if (isNaN(this.binderInfo?.connection)) {
            LogUtil.warn(this.TAG, 'invalid connection, no need to unbind.');
            return;
        }
        let connection = this.binderInfo.connection;
        LogUtil.info(this.TAG, `unbindService connection: ${connection}`);
        await this.getContext().disconnectServiceExtensionAbility(connection).catch(error => {
            LogUtil.debug(this.TAG, `disconnectServiceExtensionAbility error: ${JsonUtil.parseObjToStr(error)}`);
        });
    }
}