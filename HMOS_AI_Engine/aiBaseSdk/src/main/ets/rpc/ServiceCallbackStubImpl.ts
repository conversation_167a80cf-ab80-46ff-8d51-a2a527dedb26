/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { ServiceCallbackStub } from '../idl/ServiceCallbackStub';
import { SdkCallback, SdkCallbackType } from '../callback/SdkCallback';

/**
 * 回调的实现类
 */
export class ServiceCallbackStubImpl extends ServiceCallbackStub {

    private callback: SdkCallback;

    public constructor (callback: SdkCallback){
        super("ServiceCallbackStubImpl");
        this.callback = callback;
    }

    /**
     * @override
     */
    onResult(retCode: number, callbackResult: string | number, sdkCallbackType?: SdkCallbackType): void {
        if (SdkCallbackType) {
            this.callback.onResult(retCode, callbackResult, sdkCallbackType);
        } else {
            this.callback.onResult(retCode, callbackResult);
        }
    }

    /**
     * @override
     */
    onNotify(eventResult: string): void {
        this.callback.onNotify(eventResult);
    }
}