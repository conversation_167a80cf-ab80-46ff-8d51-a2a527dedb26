/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import ServiceExtensionAbility from '@ohos.app.ability.ServiceExtensionAbility';
import { BaseSdkServiceManager } from './BaseSdkServiceManager';
import BaseSdkConstants from '../common/BaseSdkConstants';

/**
 * 用于处理recSearch与同包名其他hap进行binder通信接收到的数据
 */
export default class AiDispatchServiceManager extends BaseSdkServiceManager {
    private static readonly TAG: string = 'AiDispatchServiceManager';
    private static instance: AiDispatchServiceManager;
    private static context: ServiceExtensionAbility['context'];

    /**
     * @Override
     */
    protected getBundleName(): string {
        return BaseSdkConstants.AI_DISPATCH_PACKAGE_NAME;
    }

    /**
     * @Override
     */
    protected getAbilityName(): string {
        return BaseSdkConstants.AI_DISPATCH_SERVICE_NAME;
    }

    /**
     * 获取AbilityDispatchServerServiceManager实例
     *
     * @param { ServiceExtensionAbility['context'] } context - service类型的上下文
     * @returns { AiDispatchServiceManager } AiDispatchServiceManager实例
     */
    public static getInstance(context: ServiceExtensionAbility['context']): AiDispatchServiceManager {
        this.context = context
        this.instance = this.instance ?? new AiDispatchServiceManager();
        return this.instance;
    }

    /**
     * @Override
     */
    protected getTag(): string {
        return AiDispatchServiceManager.TAG;
    }

    /**
     * @Override
     */
    protected getContext(): ServiceExtensionAbility['context'] {
        return AiDispatchServiceManager.context;
    }
}