/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import ServiceExtensionAbility from '@ohos.app.ability.ServiceExtensionAbility';
import { BaseSdkServiceManager } from './BaseSdkServiceManager';
import BaseSdkConstants from '../common/BaseSdkConstants';

/**
 * 用于处理和数据服务进行binder通信接收到的数据
 */
export default class DataManagerService extends BaseSdkServiceManager {
  private static readonly TAG: string = 'AiUpService';
  private static instance: DataManagerService;
  private static context: ServiceExtensionAbility['context'];

  /**
   * @Override
   */
  protected getBundleName(): string {
    return BaseSdkConstants.DATA_MANAGER_SERVICE_PACKAGE_NAME;
  }

  /**
   * @Override
   */
  protected getAbilityName(): string {
    return BaseSdkConstants.DATA_MANAGER_SERVICE_NAME;
  }

  /**
   * 获取AiDataServiceManagerManager实例
   *
   * @param { ServiceExtensionAbility['context'] } context - service类型的上下文
   * @returns { AiDataServiceManager } AiDataServiceManager实例
   */
  public static getInstance(context: ServiceExtensionAbility['context']): DataManagerService {
    this.context = context
    this.instance = this.instance ?? new DataManagerService();
    return this.instance;
  }

  /**
   * @Override
   */
  protected getTag(): string {
    return DataManagerService.TAG;
  }

  /**
   * @Override
   */
  protected getContext(): ServiceExtensionAbility['context'] {
    return DataManagerService.context;
  }
}