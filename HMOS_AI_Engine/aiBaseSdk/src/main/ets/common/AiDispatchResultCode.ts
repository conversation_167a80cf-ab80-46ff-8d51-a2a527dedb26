/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * AiDispatch code集合
 */
export default class AiDispatchResultCode {
    /**
     * 获取结果成功
     */
    public static readonly RESULT_SUCCESS_CODE: number = 1;

    /**
     * json转化异常
     */
    public static readonly JSON_PARSE_ERROR_CODE: number = -1;

    /**
     * 参数错误
     */
    public static readonly PARAMETER_ERROR_CODE: number = -2;

    /**
     * 无法连接指定服务
     */
    public static readonly FAILED_TO_CONNECT_SPECIFIED_SERVICE_ERROR_CODE: number = -3;

    /**
     * 未连接服务端
     */
    public static readonly NOT_CONNECT_SERVER_ERROR_CODE: number = -4;

    /**
     * 业务错误
     */
    public static readonly SERVICE_ERROR_CODE: number = -100;

    private AiDispatchResultCode(): void {
    }
}
