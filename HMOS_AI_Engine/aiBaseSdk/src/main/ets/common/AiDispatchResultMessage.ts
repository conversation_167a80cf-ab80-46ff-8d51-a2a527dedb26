/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * AiDispatch message集合
 */
export default class AiDispatchResultMessage {
    /**
     * JSON解析错误
     */
    public static readonly JSON_PARSE_ERROR: string = "JSON parse error";

    /**
     * 参数错误
     */
    public static readonly PARAMETER_ERROR: string = "Parameter error";

    /**
     * 无法连接指定服务
     */
    public static readonly FAILED_TO_CONNECT_SPECIFIED_SERVICE_ERROR: string = "Can not connect service";

    /**
     * 未连接服务端
     */
    public static readonly NOT_CONNECT_SERVER_ERROR: string = "Not connected to the server";

    private AiDispatchResultMessage(): void {
    }
}
