/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * 常量类
 */
export default class BaseSdkConstants {
    /**
     * 空字串
     */
    public static readonly EMPTY_STRING: string = '';

    /**
     * 解绑空字串
     */
    public static readonly EMPTY_RELEASE_STRING: string = '[]';

    /**
     * baseSDK对外接口create
     */
    public static readonly CREATE: string = 'create';

    /**
     * SDK内内容消息具体名称，这里定义create接口成功的名称
     */
    public static readonly CREATE_SUCCESS: string = 'CreateSuccess';

    /**
     * SDK内内容消息具体名称，这里定义create接口失败的名称
     */
    public static readonly CREATE_FAILED: string = 'CreateFailed';

    /**
     * SDK内内容消息具体名称，这里定义dispatchMessage接口失败的名称
     */
    public static readonly DISPATCH_FAILED: string = 'DispatchFailed';

    /**
     * SDK内事件命名空间
     */
    public static readonly EVENT: string = 'Event';

    /**
     * SDK内create消息名的消息版本，从1.0.0开始
     */
    public static readonly CREATE_MESSAGE_VERSION: string = '1.0.0';

    /**
     * 智慧分发服务
     */
    public static readonly AI_DISPATCH: string = "AiDispatch";

    /**
     * 数据服务
     */
    public static readonly DATA_SERVICE: string = "AiDataService";

    /**
     * 数据管理服务
     */
    public static readonly DATA_MANAGER_SERVICE: string = "DataManagerService";

    /**
     * 上传服务
     */
    public static readonly UP_SERVICE: string = "UpService";

    /**
     * AIDispatchService的包名
     */
    public static readonly AI_DISPATCH_PACKAGE_NAME: string = 'com.huawei.hmos.aidispatchservice';

    /**
     * AIDispatchService的服务名
     */
    public static readonly AI_DISPATCH_SERVICE_NAME: string = 'AiDispatchRemoteAbility';

    /**
     * AiDataService的包名
     */
    public static readonly AI_DATA_PACKAGE_NAME: string = 'com.huawei.hmos.aidataservice';

    /**
     * AiDataService的服务名
     */
    public static readonly AI_DATA_SERVICE_NAME: string = 'AiDataServiceAbility';

    /**
     * DataManagerService 的包名
     */
    public static readonly DATA_MANAGER_SERVICE_PACKAGE_NAME: string = 'com.huawei.hmos.aidataservice';

    /**
     * DataManagerService 的服务名
     */
    public static readonly DATA_MANAGER_SERVICE_NAME: string = 'DataManagerServiceAbility';

    /**
     * UpService 的包名
     */
    public static readonly UP_SERVICE_PACKAGE_NAME: string = 'com.huawei.hmos.aidataservice';

    /**
     * UpService 的服务名
     */
    public static readonly UP_SERVICE_NAME: string = 'UpServiceExtendAbility';
}