/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { describe, it, expect } from '@ohos/hypium'
import AbilityLabelManager from '@hms-ai/pdkfull/src/main/ets/abilityservice/labeling/AbilityLabelManager';
import AbilityLabelCapability from '../../../main/ets/labeling/AbilityLabelCapability';
import PluginInfoManager from '@hms-ai/pdkfull/src/main/ets/abilityservice/information/PluginInfoManager';
import LabelInfo from '@hms-ai/pdkfull/src/main/ets/abilityservice/labeling/LabelInfo';
import rpc from '@ohos.rpc';

const TAG: string = "AbilityLabelCapabilityTest";

/**
 * AbilityLabelCapability in the labeling
 */
export default function AbilityLabelCapabilityTest(): void {

    /**
     * Entry for starting the AbilityLabelCapabilityTest
     */
    describe('AbilityLabelCapabilityTest', (): void => {

        /**
         * 接口onRemoteRequestHandle正常传参情况
         */
        it('onRemoteRequestHandleResultTrue', 0, (): void => {
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultTrue start');
            let date: rpc.MessageSequence = rpc.MessageSequence.create();
            date.writeInterfaceToken(AbilityLabelManager.DESCRIPTOR);
            date.writeString("onRemoteRequestHandle");

            let reply: rpc.MessageSequence = rpc.MessageSequence.create();
            let abilityLabelCapability: AbilityLabelCapability = new AbilityLabelCapability();
            let flag: boolean | Promise<boolean> = abilityLabelCapability.onRemoteRequestHandle(date, reply, null);
            HiAiLog.info(TAG, 'onRemoteRequestHandle ResultTrue result is' + flag);
            expect(flag).assertTrue()
            HiAiLog.info(TAG, 'onRemoteRequestHandle ResultTrue resultTrue success');
        })

        /**
         * 接口onRemoteRequestHandle传参 date ,reply 异常情况
         */
        it('onRemoteRequestHandleResultFalse', 0, (): void => {
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result1 start');
            let date: rpc.MessageSequence = rpc.MessageSequence.create();
            date.writeInterfaceToken("InterfaceToken");
            date.writeString("getModuleInfoList");

            //date write参数都异常情况
            let reply: rpc.MessageSequence = rpc.MessageSequence.create();
            let abilityLabelCapability: AbilityLabelCapability = new AbilityLabelCapability();
            let flag: boolean | Promise<boolean> = abilityLabelCapability.onRemoteRequestHandle(date, reply, null);
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result1 is' + flag);
            expect(flag).assertFalse();

            //date 单独writeString参数异常
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result2 start');
            date.writeInterfaceToken(PluginInfoManager.DESCRIPTOR);
            date.writeString("writeString");
            let flag2: boolean | Promise<boolean> = abilityLabelCapability.onRemoteRequestHandle(date, reply, null);
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result2 is' + flag2);
            expect(flag2).assertFalse();

            //reply 为空情况
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result3 start');
            date.writeString("onRemoteRequestHandle");
            let flag3: boolean | Promise<boolean> = abilityLabelCapability.onRemoteRequestHandle(date, null, null);
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result3 is' + flag2);
            expect(flag3).assertFalse();
            HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse success');
        })


        /**
         * 接口getChipType 获取ChipType成功
         */
        it('getChipType', 0, async (): Promise<void> => {
            HiAiLog.info(TAG, 'getChipType start');
            let abilityLabelCapability: AbilityLabelCapability = new AbilityLabelCapability();
            let result: LabelInfo = await abilityLabelCapability.getChipType();
            let labelValue: string = result.labelValue;
            HiAiLog.info(TAG, 'getChipType labelValue is' + labelValue);
            if (labelValue != null && labelValue != undefined) {
                expect(true).assertTrue();
            } else {
                expect(false).assertTrue();
            }
            HiAiLog.info(TAG, 'getChipType success');
        })

        /**
         * 接口getProductModel 获取productModel成功
         */
        it('getProductModel', 0, async (): Promise<void> => {
            HiAiLog.info(TAG, 'getProductModel start');
            let abilityLabelCapability: AbilityLabelCapability = new AbilityLabelCapability();
            let result: LabelInfo = await abilityLabelCapability.getProductModel();
            let labelValue: string = result.labelValue;
            HiAiLog.info(TAG, 'getProductModel labelValue is' + result);
            if (labelValue != null && labelValue != undefined) {
                expect(true).assertTrue();
            } else {
                expect(false).assertTrue();
            }
            HiAiLog.info(TAG, 'getProductModel success');
        })
        /**
         * 接口getProductModel 获取RegionLabel成功
         */
        it('getRegionLabel', 0, async (): Promise<void> => {
            HiAiLog.info(TAG, 'getRegionLabel start');
            let abilityLabelCapability: AbilityLabelCapability = new AbilityLabelCapability();
            let result: LabelInfo = await abilityLabelCapability.getRegionLabel();
            let labelValue: string = result.labelValue;
            HiAiLog.info(TAG, 'getRegionLabel labelValue is' + result);
            if (labelValue != null && labelValue != undefined) {
                expect(true).assertTrue();
            } else {
                expect(false).assertTrue();
            }
            HiAiLog.info(TAG, 'getRegionLabel success');
        })

        /**
         * getAllLabel 参数正常成功情况
         */
        it('getAllLabel', 0, (): void => {
            HiAiLog.info(TAG, 'getAllLabel start');
            let testRemoteObject: TestRemoteObject = new TestRemoteObject("testObject");
            let requestData: rpc.MessageSequence = rpc.MessageSequence.create();
            try {
                requestData.writeRemoteObject(testRemoteObject);
                let abilityLabelCapability: AbilityLabelCapability = new AbilityLabelCapability();
                abilityLabelCapability.getAllLabel(requestData, null, new AbilityLabelCapability());
            } catch (error) {
                expect().assertThrowError(error)
            }
            expect(true).assertTrue();
            HiAiLog.info(TAG, 'getAllLabel success');
        })

        /**
         * getAllLabel 参数requestData abilityLabelCapability 异常失败情况
         */
        it('getAllLabelFalse', 0, (): void => {
            HiAiLog.info(TAG, 'getAllLabelFalse start');

            let requestData: rpc.MessageSequence = rpc.MessageSequence.create();
            let abilityLabelCapability: AbilityLabelCapability = new AbilityLabelCapability();
            let testRemoteObject: TestRemoteObject = new TestRemoteObject("testObject");

            //传参requestData不写入RemoteObject情况
            try {
                HiAiLog.info(TAG, 'getAllLabel no write RemoteObject  start');
                abilityLabelCapability.getAllLabel(requestData, null, abilityLabelCapability);
            } catch (error) {
                expect(true).assertTrue()
            }

            //传参capability为空情况
            try {
                HiAiLog.info(TAG, 'getAllLabel capability null start');
                requestData.writeRemoteObject(testRemoteObject);
                abilityLabelCapability.getAllLabel(requestData, null, null);
            } catch (error) {
                expect(true).assertTrue()
            }

            //传参requestData为空情况
            try {
                HiAiLog.info(TAG, 'getAllLabel requestData null start');
                abilityLabelCapability.getAllLabel(null, null, abilityLabelCapability);
            } catch (error) {
                expect(true).assertTrue()
            }

            //传参requestData，capability 都为空情况
            try {
                HiAiLog.info(TAG, 'getAllLabel all null start');
                abilityLabelCapability.getAllLabel(null, null, null);
            } catch (error) {
                expect(true).assertTrue()
            }
            HiAiLog.info(TAG, 'getAllLabelFalse success');
        })

        /**
         * Inherit RemoteObject and transfer parameters in test cases
         */
        class TestRemoteObject extends rpc.RemoteObject {
            constructor(descriptor: string) {
                super(descriptor);
            }
        }
    })
}