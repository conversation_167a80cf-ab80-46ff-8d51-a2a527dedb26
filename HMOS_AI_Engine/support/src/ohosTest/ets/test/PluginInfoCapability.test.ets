/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import PluginInfoCapability from '../../../main/ets/framework/ability/PluginInfoCapability';
import PluginInfoManager from '@hms-ai/pdkfull/src/main/ets/abilityservice/information/PluginInfoManager';
import rpc from '@ohos.rpc';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { describe, it, expect } from '@ohos/hypium'

const TAG: string = "PluginInfoCapabilityTest";

/**
 * PluginInfoCapability in the framework
 */
export default function PluginInfoCapabilityTest(): void {

  /**
   * Entry for starting the PluginInfoCapabilityTest
   */
  describe('PluginInfoCapabilityTest', (): void => {

    /**
     * 接口onRemoteRequestHandle正常传参情况
     */
    it('onRemoteRequestHandleResultTrue', 0, (): void => {
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultTrue start');
      let date: rpc.MessageSequence = rpc.MessageSequence.create();
      date.writeInterfaceToken(PluginInfoManager.DESCRIPTOR);
      date.writeString("onRemoteRequestHandle");

      let reply: rpc.MessageSequence = rpc.MessageSequence.create();
      let pluginInfoCapability: PluginInfoCapability = new PluginInfoCapability();
      let flag: boolean | Promise<boolean> = pluginInfoCapability.onRemoteRequestHandle(date, reply, null);
      HiAiLog.info(TAG, 'onRemoteRequestHandle ResultTrue result is' + flag);
      expect(flag).assertTrue()
      HiAiLog.info(TAG, 'onRemoteRequestHandle ResultTrue resultTrue success');
    })

    /**
     * 接口onRemoteRequestHandle传参 date ,reply 异常情况
     */
    it('onRemoteRequestHandleResultFalse', 0, (): void => {
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result1 start');

      //date write参数都异常情况
      let date: rpc.MessageSequence = rpc.MessageSequence.create();
      date.writeInterfaceToken("InterfaceToken");
      date.writeString("getModuleInfoList");
      let reply: rpc.MessageSequence = rpc.MessageSequence.create();
      let pluginInfoCapability: PluginInfoCapability = new PluginInfoCapability();
      let flag: boolean | Promise<boolean> = pluginInfoCapability.onRemoteRequestHandle(date, reply, null);
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result1 is' + flag);
      expect(flag).assertFalse();

      //date 单独writeString参数异常
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result2 start');
      date.writeInterfaceToken(PluginInfoManager.DESCRIPTOR);
      date.writeString("writeString");
      let flag2: boolean | Promise<boolean> = pluginInfoCapability.onRemoteRequestHandle(date, reply, null);
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result2 is' + flag2);
      expect(flag2).assertFalse();

      //reply 为空情况
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result3 start');
      date.writeString("onRemoteRequestHandle");
      let flag3: boolean | Promise<boolean> = pluginInfoCapability.onRemoteRequestHandle(date, null, null);
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse result3 is' + flag2);
      expect(flag3).assertFalse();
      HiAiLog.info(TAG, 'onRemoteRequestHandle resultFalse success');
    })

    /**
     * getModuleInfoList  正常传参数情况
     */
    it('getModuleInfoList', 0, (): void => {
      HiAiLog.info(TAG, 'getModuleInfoList start');
      let testRemoteObject: TestRemoteObject = new TestRemoteObject("testObject");
      let date: rpc.MessageSequence = rpc.MessageSequence.create();
      date.writeRemoteObject(testRemoteObject)

      let reply: rpc.MessageSequence = rpc.MessageSequence.create();
      let pluginInfoCapability: PluginInfoCapability = new PluginInfoCapability();
      let flag1: boolean = pluginInfoCapability.getModuleInfoList(date, reply);
      HiAiLog.info(TAG, 'getModuleInfoList result1 is' + flag1);
      expect(flag1).assertTrue()
      HiAiLog.info(TAG, 'getModuleInfoList success');
    })

    /**
     * getModuleInfoList  参数date,reply为空情况
     */
    it('getModuleInfoListFalse', 0, (): void => {
      HiAiLog.info(TAG, 'getModuleInfoListFalse start');
      let testRemoteObject: TestRemoteObject = new TestRemoteObject("testObject");
      let date: rpc.MessageSequence = rpc.MessageSequence.create();
      date.writeRemoteObject(testRemoteObject)
      let reply: rpc.MessageSequence = rpc.MessageSequence.create();
      let pluginInfoCapability: PluginInfoCapability = new PluginInfoCapability();

      //date 为空情况
      try {
        HiAiLog.info(TAG, 'getModuleInfoListFalse date null start');
        pluginInfoCapability.getModuleInfoList(null, reply);
      } catch (error) {
        expect(true).assertTrue();
      }

      //reply 为空情况
      try {
        HiAiLog.info(TAG, 'getModuleInfoListFalse reply null start');
        pluginInfoCapability.getModuleInfoList(date, null);
      } catch (error) {
        expect(true).assertTrue();
      }

      //date reply 都为空情况
      try {
        HiAiLog.info(TAG, 'getModuleInfoListFalse all null start');
        pluginInfoCapability.getModuleInfoList(null, null);
      } catch (error) {
        expect(true).assertTrue();
      }
      HiAiLog.info(TAG, 'getModuleInfoListFalse success');
    })

    /**
     * Inherit RemoteObject and transfer parameters in test cases
     */
    class TestRemoteObject extends rpc.RemoteObject {
      constructor(descriptor: string) {
        super(descriptor);
      }
    }
  })
}