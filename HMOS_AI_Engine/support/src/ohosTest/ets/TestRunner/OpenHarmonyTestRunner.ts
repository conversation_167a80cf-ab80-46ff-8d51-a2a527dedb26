/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import TestRunner from '@ohos.application.testRunner'
import AbilityDelegatorRegistry from '@ohos.app.ability.abilityDelegatorRegistry'
import { BusinessError } from '@hms-ai/pdkfull/src/main/ets/interfaces/ICallback';

/**
 * Convert the input parameter to the string method
 */
function translateParamsToString (parameters: {[key: string]: string}): string {
    const keySet = new Set( [
        '-s class', '-s notClass', '-s suite', '-s it',
        '-s level', '-s testType', '-s size', '-s timeout',
        '-s dryRun'
    ])
    let targetParams: string = '';
    for (const key of Object.keys(parameters)) {
        if (keySet.has(key)) {
            targetParams = `${targetParams} ${key} ${parameters[key]}`
        }
    }
    return targetParams.trim()
}

/**
 * 创建能力回调
 */
async function onAbilityCreateCallback (): Promise<void> {
    HiAiLog.info ('testTag', '%{public}s' + 'onAbilityCreateCallback');
}

/**
 * 添加能力过程中回调
 */
async function addAbilityMonitorCallback (err: BusinessError): Promise<void> {
    HiAiLog.info ('testTag', 'addAbilityMonitorCallback : %{public}s' + JSON.stringify (err) ?? '');
}

/**
 * 继承testruner，测试框架中初始化的类
 */
export default class OpenHarmonyTestRunner implements TestRunner {
    constructor() {
    }

    /**
     * 准备阶段方法
     */
    onPrepare (): void {
        HiAiLog.info ('testTag', '%{public}s' + 'OpenHarmonyTestRunner OnPrepare ');
    }

    /**
     * 执行过程中方法
     */
    async onRun (): Promise<void> {
        HiAiLog.info ('testTag', '%{public}s' + 'OpenHarmonyTestRunner onRun run');
        let abilityDelegatorArguments: AbilityDelegatorRegistry.AbilityDelegatorArgs = AbilityDelegatorRegistry.getArguments ()
        let abilityDelegator: AbilityDelegatorRegistry.AbilityDelegator = AbilityDelegatorRegistry.getAbilityDelegator ()
        let testAbilityName: string = abilityDelegatorArguments.bundleName + '.TestAbility'
        let lMonitor: AbilityDelegatorRegistry.AbilityMonitor = {
            abilityName: testAbilityName,
            onAbilityCreate: onAbilityCreateCallback,
        };
        abilityDelegator.addAbilityMonitor (lMonitor, addAbilityMonitorCallback)
        let cmd: string = 'aa start -d 0 -a TestAbility' + ' -b ' + abilityDelegatorArguments.bundleName
        cmd += ' ' + translateParamsToString (abilityDelegatorArguments.parameters)
        let debug: string = abilityDelegatorArguments.parameters['-D']
        if (debug == 'true') {
            cmd += ' -D'
        }
        HiAiLog.info ('testTag', 'cmd : %{public}s' + cmd);
        abilityDelegator.executeShellCommand (cmd,
            (err: BusinessError, d: AbilityDelegatorRegistry.ShellCmdResult) => {
                HiAiLog.info ('testTag', 'executeShellCommand : err : %{public}s' + JSON.stringify (err) ?? '');
                HiAiLog.info ('testTag', 'executeShellCommand : data : %{public}s' + d.stdResult ?? '');
                HiAiLog.info ('testTag', 'executeShellCommand : data : %{public}s' + d.exitCode ?? '');
            })
        HiAiLog.info ('testTag', '%{public}s' + 'OpenHarmonyTestRunner onRun end');
    }
}