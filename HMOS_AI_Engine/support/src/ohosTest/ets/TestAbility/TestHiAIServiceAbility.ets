/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import AbilityDelegatorRegistry from '@ohos.app.ability.abilityDelegatorRegistry'
import { Hypium } from '@ohos/hypium'
import testsuite from '../test/List.test'
import Window from '@ohos.window'
import { GlobalContextUpdateKey } from '@hms-ai/pdkfull/src/main/ets/utils/GlobalContext';
import Ability from '@ohos.app.ability.Ability'
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';

/**
 * 测试ability入口
 */
export default class TestHiAIServiceAbility extends Ability {
    /**
     * 创建能力方法
     */
    onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
        HiAiLog.info('testTag', 'TestAbility onCreate');
        HiAiLog.info('testTag', 'want param:' + JSON.stringify(want) ?? '');
        HiAiLog.info('testTag', 'launchParam:' + JSON.stringify(launchParam) ?? '');
        let abilityDelegator: AbilityDelegatorRegistry.AbilityDelegator = AbilityDelegatorRegistry.getAbilityDelegator()
        let abilityDelegatorArguments: AbilityDelegatorRegistry.AbilityDelegatorArgs = AbilityDelegatorRegistry.getArguments()
        HiAiLog.info('testTag', '%{public}s' + 'start run testcase!!!');
        Hypium.hypiumTest(abilityDelegator, abilityDelegatorArguments, testsuite)
    }

    /**
     * 销毁方法
     */
    onDestroy(): void {
        HiAiLog.info('testTag', '%{public}s' + 'TestAbility onDestroy');
    }

    /**
     * 创建窗口
     */
    onWindowStageCreate(windowStage: Window.WindowStage): void {
        HiAiLog.info('testTag', '%{public}s' + 'TestAbility onWindowStageCreate');
        windowStage.loadContent('TestAbility/pages/index', (err, data) => {
            if (err.code) {
                HiAiLog.error('testTag', 'Failed to load the content. Cause: %{public}s' + JSON.stringify(err) ?? '');
                return;
            }
            HiAiLog.info('testTag', 'Succeeded in loading the content. Data: %{public}s' +
            JSON.stringify(data) ?? '');
        });

        GlobalContextUpdateKey('abilityContext', this.context);
    }

    /**
     * 窗口销毁
     */
    onWindowStageDestroy(): void {
        HiAiLog.info('testTag', '%{public}s' + 'TestAbility onWindowStageDestroy');
    }

    /**
     * 前台运行方法
     */
    onForeground(): void {
        HiAiLog.info('testTag', '%{public}s' + 'TestAbility onForeground');
    }

    /**
     * 后台运行方法
     */
    onBackground(): void {
        HiAiLog.info('testTag', '%{public}s' + 'TestAbility onBackground');
    }
}