/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';

@Entry
@Component
struct Index {
    aboutToAppear(): void {
        HiAiLog.info('testTag', '%{public}s' + 'TestAbility index aboutToAppear');
    }

    @State message: string = 'Hello World'

    build() {
        Row() {
            Column() {
                Text(this.message)
                    .fontSize(50)
                    .fontWeight(FontWeight.Bold)
                Button() {
                    Text('next page')
                        .fontSize(20)
                        .fontWeight(FontWeight.Bold)
                }
                .type(ButtonType.Capsule)
                .margin({
                    top: 20
                })
                .backgroundColor('#0D9FFB')
                .width('35%')
                .height('5%')
                .onClick(() => {
                })
            }
            .width('100%')
        }
        .height('100%')
    }
}