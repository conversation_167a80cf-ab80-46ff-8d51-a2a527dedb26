{"module": {"name": "support_test", "type": "feature", "srcEntrance": "./ets/Application/TestAbilityStage.ts", "description": "$string:entry_test_desc", "mainElement": "TestHiAIServiceAbility", "process": "com.huawei.ohos.hiai:test", "deviceTypes": ["default", "tablet", "2in1"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:test_pages", "abilities": [{"name": "TestAbility", "srcEntrance": "./ets/TestAbility/TestHiAIServiceAbility.ets", "description": "$string:TestAbility_desc", "icon": "$media:icon", "label": "$string:TestAbility_label", "visible": true, "startWindowIcon": "$media:icon", "startWindowBackground": "$color:white", "skills": [{"actions": ["action.system.home"], "entities": ["entity.system.home"]}]}]}}