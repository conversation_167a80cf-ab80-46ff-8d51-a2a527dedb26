{"string": [{"name": "entry_desc", "value": "description"}, {"name": "MainAbility_desc", "value": "description"}, {"name": "MainAbility_label", "value": "HiAiDemo"}, {"name": "INTERNET_permission_reason", "value": "Used to access network"}, {"name": "Bundle_Info_permission_reason", "value": "Used to query the bundle info of hiai"}, {"name": "START_ABILIIES_FROM_BACKGROUND_permission_reason", "value": "Used to access its own background Ability."}, {"name": "ENTERPRISE_GET_DEVICE_INFO_permission_reason", "value": "need use ENTERPRISE_GET_DEVICE_INFO to obtain device info."}, {"name": "GET_NETWORK_INFO_permission_reason", "value": "need use GET_NETWORK_INFO to obtain network info."}, {"name": "GET_SYSTEM_FLOAT_WINDOW_permission_reason", "value": "need use SYSTEM_FLOAT_WINDOW when downloading model."}, {"name": "GET_PLUGIN_UPDATE_permission_reason", "value": "need use PLUGIN_UPDATE when downloading model."}, {"name": "GET_RECEIVE_UPDATE_MESSAGE_permission_reason", "value": "need use RECEIVE_UPDATE_MESSAGE when downloading model."}, {"name": "GET_BUNDLE_INFO_permission_reason", "value": "need use BUNDLE_INFO when profiling."}, {"name": "START_SYSTEM_DIALOG_permission_reason", "value": "need use START_SYSTEM_DIALOG when downloading model."}, {"name": "UIExtAbility_label", "value": "UIExtAbility"}, {"name": "ModelDownload_Language_Model", "value": "Download language package?", "attr": {"priority": "LT"}}, {"name": "ModelDownload_component", "value": "Download required resources?", "attr": {"priority": "LT"}}, {"name": "ModelDownload_curent_operation", "value": "A resource package (%s) is required to use this service. Downloading this over a mobile network may result in extra charges.", "attr": {"priority": "translate"}}, {"name": "Title_ModelDownload_downloading", "value": "Download in progress", "attr": {"priority": "translate"}}, {"name": "ModelDownload_download", "value": "Download", "attr": {"priority": "translate"}}, {"name": "ModelDownload_cancel", "value": "Cancel", "attr": {"priority": "translate"}}, {"name": "ModelDownload_downloading", "value": "Downloading %1$s (%2$s)…", "attr": {"priority": "LT"}}, {"name": "ModelDownload_downloaded", "value": "Download complete", "attr": {"priority": "LT"}}, {"name": "Network_exception", "value": "Network error. Please try again.", "attr": {"priority": "translate"}}, {"name": "ModelDownload_cancel_button", "value": "Cancel download", "attr": {"priority": "translate"}}, {"name": "Service_ModelDownload", "value": "Service is being downloaded", "attr": {"priority": "translate"}}]}