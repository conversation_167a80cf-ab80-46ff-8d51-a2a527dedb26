{"string": [{"name": "ModelDownload_Language_Model", "value": "[TS_959730]_Download language package?"}, {"name": "Bundle_Info_permission_reason", "value": "[TS_959715]_Used to query the bundle info of hiai"}, {"name": "GET_NETWORK_INFO_permission_reason", "value": "[TS_959724]_need use GET_NETWORK_INFO to obtain network info."}, {"name": "Title_ModelDownload_downloading", "value": "[TS_959729]_Download in progress"}, {"name": "ModelDownload_downloading", "value": "[TS_959718]_Downloading %1$s (%2$s)…"}, {"name": "ModelDownload_cancel", "value": "[TS_959725]_Cancel"}, {"name": "START_ABILIIES_FROM_BACKGROUND_permission_reason", "value": "[TS_959714]_Used to access its own background Ability."}, {"name": "ModelDownload_curent_operation", "value": "[TS_959720]_A resource package (%s) is required to use this service. Downloading this over a mobile network may result in extra charges."}, {"name": "ModelDownload_download", "value": "[TS_959722]_Download"}, {"name": "INTERNET_permission_reason", "value": "[TS_959726]_Used to access network"}, {"name": "ENTERPRISE_GET_DEVICE_INFO_permission_reason", "value": "[TS_959719]_need use ENTERPRISE_GET_DEVICE_INFO to obtain device info."}, {"name": "ModelDownload_component", "value": "[TS_959721]_Download required resources?"}, {"name": "entry_desc", "value": "[TS_959717]_description"}, {"name": "MainAbility_desc", "value": "[TS_959713]_description"}, {"name": "Network_exception", "value": "[TS_959727]_Network error. Please try again."}, {"name": "MainAbility_label", "value": "[TS_959716]_HiAiDemo"}, {"name": "ModelDownload_downloaded", "value": "[TS_959728]_Download complete"}, {"name": "GET_SYSTEM_FLOAT_WINDOW_permission_reason", "value": "[TS_963930]_need use SYSTEM_FLOAT_WINDOW when downloading model."}, {"name": "GET_BUNDLE_INFO_permission_reason", "value": "[TS_1049238]_need use BUNDLE_INFO when profiling."}, {"name": "GET_RECEIVE_UPDATE_MESSAGE_permission_reason", "value": "[TS_1025136]_need use RECEIVE_UPDATE_MESSAGE when downloading model."}, {"name": "GET_PLUGIN_UPDATE_permission_reason", "value": "[TS_1025137]_need use PLUGIN_UPDATE when downloading model."}, {"name": "Service_ModelDownload", "value": "[TS_1000907]_Service is being downloaded"}, {"name": "ModelDownload_cancel_button", "value": "[TS_994048]_Cancel download"}, {"name": "UIExtAbility_label", "value": "[TS_1062923]_UIExtAbility"}, {"name": "START_SYSTEM_DIALOG_permission_reason", "value": "[TS_1062922]_need use START_SYSTEM_DIALOG when downloading model."}]}