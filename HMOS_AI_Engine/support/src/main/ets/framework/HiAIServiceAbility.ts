/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import IAIFrameworkStub from './IAIFrameworkStub';
import ExtensionAbility from '@ohos.app.ability.ServiceExtensionAbility';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import GlobalThisAbilityHelper from '@hms-ai/pdkfull/src/main/ets/abilityservice/GlobalThisAbilityHelper';
import Want from '@ohos.app.ability.Want';
import { GlobalContextUpdateKey } from '@hms-ai/pdkfull/src/main/ets/utils/GlobalContext';
import { common } from '@kit.AbilityKit';
import CeliaPrivacyVerification
  from '../modelManager/privacyVerification/celiaPrivacyVerification/CeliaPrivacyVerification'
import ModelDownloadOta from '../modelManager/modelDownloadOta/ModelDownloadOta';
import ModelInfoManager from '../modelManager/modelDownloadOta/modelDownloadInfo/ModelInfoManager';
import ModelTaskManager from '../modelManager/modelDownloadOta/ModelTaskManager';
import { JSON } from '@kit.ArkTS';

const TAG: string = 'HiAIServiceAbility';

export default class HiAIServiceAbility extends ExtensionAbility {
  private iAIFrameworkStubObj: IAIFrameworkStub;
  public static globalContext: common.ServiceExtensionContext | null = null;

  constructor() {
    super()
    this.iAIFrameworkStubObj = new IAIFrameworkStub("IAIFrameworkStub construct", this.context)
  }

  onCreate(want: Want): void {
    HiAiLog.info(TAG, `onCreate`);
    HiAIServiceAbility.globalContext = this.context;
    this.iAIFrameworkStubObj = new IAIFrameworkStub("IAIFrameworkStub construct", this.context);
    GlobalThisAbilityHelper.set<rpc.RemoteObject>(TAG, this.iAIFrameworkStubObj);
    CeliaPrivacyVerification.getInstance().listenWlanStatus();
    GlobalContextUpdateKey('HiAIServiceAbility', this);
  }

  async onDestroy(): Promise<void> {
    HiAiLog.info(TAG, 'Ability onDestroy');
    this.iAIFrameworkStubObj.onRelease();
  }

  onConnect(): IAIFrameworkStub {
    HiAiLog.info(TAG, `is connect`);
    return this.iAIFrameworkStubObj;
  }

  async onDisconnect(): Promise<void> {
    HiAiLog.info(TAG, 'is disconnect');
    let modelNames: string[] = ModelInfoManager.getAllModelNames();
    let modelIdArray: Array<string> = [];
    for (let i = 0; i < modelNames.length; i++) {
      let modelId: string = ModelInfoManager.getModelId(modelNames[i]);
      if (modelId !== null) {
        await modelIdArray.push(modelId);
      }
    }
    if (modelIdArray.length !== 0) {
      try {
        HiAiLog.info(TAG, `Try to pauseModelDownload ${JSON.stringify(modelIdArray)}`)
        await ModelDownloadOta.getInstance().pauseModelDownload(modelIdArray);
      } catch (e) {
        HiAiLog.error(TAG, `Failed to pauseModelDownload ${e.message}, ${e.code}`)
      }
    }
  }

}