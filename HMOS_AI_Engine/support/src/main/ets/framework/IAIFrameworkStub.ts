/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import common from '@ohos.app.ability.common';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import CapabilityFactory from './CapabilityFactory';
import { Capability, CommonMethod } from '@hms-ai/pdkfull/src/main/ets/interfaces/FrameworkCapability';
import IHiAICapability from './IHiAICapability';

const TAG: string = 'IAIFrameworkStub';

/**
 * The plugin business service class provides specific business service entries.
 * <AUTHOR>
 */
export default class IAIFrameworkStub extends rpc.RemoteObject {
    private context: common.ServiceExtensionContext | null = null;

    /**
     * @override
     */
    constructor(des: string, context: common.ServiceExtensionContext) {
        super(des);
        if (typeof des === 'string') {
            this.context = context;
        }
    }

    /**
     * Remote RPC request
     * @param capabilityId { number } the Request Code
     * @param data { rpc.MessageSequence } The data transmitted by the client.
     * @param reply {rpc.MessageSequence } Container for information returned to the client.
     * @param options Configuration parcel
     */
    onRemoteMessageRequest(code: number, data: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): boolean {
        HiAiLog.info(TAG, `message code : ${code}`);
        if (Object.values(Capability).includes(code)) {
            let capability: IHiAICapability = CapabilityFactory.createCapability(code) as IHiAICapability;
            let temp: boolean | Promise<boolean> = capability.onRemoteRequestHandle(data, reply, options);
            HiAiLog.info(TAG, `${JSON.stringify(temp)}`);
            return true;
        } else if (Object.values(CommonMethod).includes(code)) {
            this.commonMethodHandle(code);
            return true;
        } else {
            HiAiLog.info(TAG, `request code error`);
            return false;
        }
    }

    /**
     * Interface of initializing plugin which is used to load the so, model and shared resources of related services
     */
    init(): void {
        HiAiLog.info(TAG, `pluginservicestub init start`);
    }

    /**
     * Interface for releasing service-related resources
     */
    onRelease(): void {
        HiAiLog.info(TAG, `onRelease resource`);
        if (this.context) {
            this.context.resourceManager.release();
        }
    }

    private commonMethodHandle(methodCode: number): void {
        switch (methodCode) {
            case CommonMethod.INITIALIZATION: {
                this.init();
                break;
            }
            case CommonMethod.RELEASE: {
                this.onRelease();
                break;
            }
            default: {
                HiAiLog.info(TAG, `common code code error`);
                break;
            }
        }
    }
}