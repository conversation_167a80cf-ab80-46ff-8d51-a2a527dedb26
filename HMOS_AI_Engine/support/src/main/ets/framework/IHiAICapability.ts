/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';

/**
 * This interface defines the necessary methods for the basic capabilities of HIAI.
 * This method is used to handle client requests.
 * <AUTHOR>
 */
export default interface IHiAICapability {
    /**
     * Implement this method for remote request processing and return the corresponding processing results.
     * @param { rpc.MessageSequence } data The data transmitted by the client.
     * @param { rpc.MessageSequence } reply Container for information returned to the client.
     * @param { rpc.MessageSequence } options Settings requested by the client.
     * @return Returns a simple boolean which is {@code true} if the operation succeeds; {{@code false} otherwise}
     * when the function call is synchronous.
     */
    onRemoteRequestHandle: (data: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption) => boolean | Promise<boolean>;
}