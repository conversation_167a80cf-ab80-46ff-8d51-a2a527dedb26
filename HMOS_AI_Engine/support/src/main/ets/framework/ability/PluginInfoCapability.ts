/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import Constants from '@hms-ai/pdkfull/src/main/ets/utils/Constants';
import { RunningErrorCode } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import rpc from '@ohos.rpc';
import taskpool from '@ohos.taskpool';
import { mGetModuleInfoListTaskFunc } from './GetModuleInfoListTaskFunc';
import IHiAICapability from '../IHiAICapability';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import PluginInfoManager from '@hms-ai/pdkfull/src/main/ets/abilityservice/information/PluginInfoManager';
import PluginInfoCallbackStub from '@hms-ai/pdkfull/src/main/ets/abilityservice/information/PluginInfoCallbackStub';
import InfoCallback from '@hms-ai/pdkfull/src/main/ets/interfaces/IInfoCallback'

const TAG: string = "PluginInfoCapability";

/**
 * This class is used to obtain information about plugins. It is convenient for each plug-in to communicate with
 * each other. Get the necessary information about running.
 * @class
 * <AUTHOR>
 */
export default class PluginInfoCapability implements IHiAICapability {
    /**
     * @override
     */
    onRemoteRequestHandle(data: rpc.MessageSequence, reply: rpc.MessageSequence | null, options: rpc.MessageOption | null): boolean | Promise<boolean> {
        let token: string = data.readInterfaceToken();
        if (PluginInfoManager.DESCRIPTOR !== token) {
            HiAiLog.error(TAG, "token is error");
            return false;
        }
        let requestMethod: string = data.readString();
        let methods: string[] = Object.getOwnPropertyNames(Object.getPrototypeOf(this));
        HiAiLog.info(TAG, `===---methods: ${JSON.stringify(methods)}`);
        if (methods.includes(requestMethod)) {
            HiAiLog.info(TAG, 'The method is correct!');
            if (requestMethod === 'getModuleInfoList' && reply) {
                this.getModuleInfoList(data, reply);
            }
            HiAiLog.info(TAG, `===---requestMethod: ${requestMethod}`)
            return true;
        } else {
            HiAiLog.error(TAG, 'Wrong method name!');
            return false
        }
    }

    /**
     * Obtain the corresponding information according to the module name.
     * @param data {@link rpc.MessageSequence} object holding the data to send.
     * @param reply {@link rpc.MessageSequence} object that receives the response.
     * @return { boolean } return true means the acquisition is successful, otherwise it means failure.
     */
    getModuleInfoList(data: rpc.MessageSequence | null, reply: rpc.MessageSequence | null): boolean {
        if (data) {
            let pluginInfoCallback: rpc.IRemoteObject = data.readRemoteObject();
            let pluginInfoCallbackProxy: InfoCallback = PluginInfoCallbackStub.asInterface(pluginInfoCallback)
            let mGetModuleInfoListTask: taskpool.Task = new taskpool.Task(mGetModuleInfoListTaskFunc);
            let mGetModuleInfoListTaskRes: Promise<string> = taskpool.execute(mGetModuleInfoListTask) as Promise<string>;
            mGetModuleInfoListTaskRes.then((data: string): void => {
                if (data != null) {
                    HiAiLog.info(TAG, 'getModuleInfo success!' + data);
                    pluginInfoCallbackProxy.onResult(RunningErrorCode.SUCCESS, data);
                } else {
                    HiAiLog.info(TAG, 'getModuleInfo fail!');
                    pluginInfoCallbackProxy.onResult(RunningErrorCode.FAILED, Constants.EMPTY_STRING);
                }
            })
        }
        if (reply) {
            reply.writeNoException();
        }
        return true;
    }
}