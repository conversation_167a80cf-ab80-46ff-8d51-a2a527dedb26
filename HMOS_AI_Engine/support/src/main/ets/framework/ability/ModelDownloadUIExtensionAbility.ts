/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import UIExtensionAbility from '@ohos.app.ability.UIExtensionAbility'
import UIExtensionContentSession from '@ohos.app.ability.UIExtensionContentSession';
import Want from '@ohos.app.ability.Want';
import { common } from '@kit.AbilityKit';
import NoToastConstants from '../../modelManager/utils/constants/NoToastConstants';
import ModelDownloadCapability from '../../modelManager/ModelDownloadCapability';
import { ModelDownloadResultCode, ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import ModelInfoManager from '../../modelManager/modelDownloadOta/modelDownloadInfo/ModelInfoManager';

const TAG: string = 'ModelDownloadUIExtentionAbility';

export default class ModelDownloadUIExtentionAbility extends UIExtensionAbility {
  public static globalContext: common.UIExtensionContext | null = null;
  public static hasTriggeredCallback: boolean = false;

  onCreate(): void {
    HiAiLog.info(TAG, `onCreate`);
    ModelDownloadUIExtentionAbility.globalContext = this.context;
    ModelDownloadUIExtentionAbility.hasTriggeredCallback = false;
  }

  onForeground(): void {
    HiAiLog.info(TAG, `onForeground`);
  }

  onBackground(): void {
    HiAiLog.info(TAG, `onBackground`);
  }

  onDestroy(): void {
    HiAiLog.info(TAG, `onDestroy`);
    // 重置标志位
    ModelDownloadUIExtentionAbility.hasTriggeredCallback = false;
  }

  onSessionCreate(want: Want, session: UIExtensionContentSession): void {
    HiAiLog.info(TAG, `onSessionCreate, session: ${JSON.stringify(session)}`);
    let storage: LocalStorage = new LocalStorage();
    storage.setOrCreate('session', session);

    session.loadContent('modelManager/modelDownloadOta/modelDownloadPages/pages/ModelDownloadPage', storage);
    session.setWindowBackgroundColor('#00000000')

    HiAiLog.info(TAG, 'onSessionCreate');
  }

  onSessionDestroy(session: UIExtensionContentSession): void | Promise<void> {
    HiAiLog.info(TAG, `onSessionDestroy`);
  }
};