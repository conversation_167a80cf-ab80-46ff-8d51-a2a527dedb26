/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import BundleResourceManager from '@hms-ai/pdkfull/src/main/ets/platform/oswrapper/BundleResourceManager';
import <PERSON>sonUtil from '@hms-ai/pdkfull/src/main/ets/utils/JsonUtil';
import ModuleInfo from '@hms-ai/pdkfull/src/main/ets/abilityservice/information/ModuleInfo';
import { BusinessError } from '@hms-ai/pdkfull/src/main/ets/interfaces/ICallback';

/**
 * hap ModlueInfo task
 * return moduleInfo string
 */
const mGetModuleInfoListTaskFunc = async function (): Promise<string> {
    "use concurrent";
    let tag: string = "TaskPool_myTaskFunc";
    return await new Promise((resolve, reject) => {
        BundleResourceManager.getInstance().getModuleInfoList((error: BusinessError, data: Array<ModuleInfo> | null) => {
            if (data != null) {
                HiAiLog.info(tag, 'getModuleInfo success!');
                let dataContent: string = JsonUtil.classToString(data);
                HiAiLog.info(tag, "dataContent is :: " + dataContent);
                resolve(dataContent);
                HiAiLog.info(tag, 'getModuleInfo end !!');
            } else {
                HiAiLog.info(tag, 'getModuleInfo fail!');
                reject("");
            }
        });
    });
}

export { mGetModuleInfoListTaskFunc }