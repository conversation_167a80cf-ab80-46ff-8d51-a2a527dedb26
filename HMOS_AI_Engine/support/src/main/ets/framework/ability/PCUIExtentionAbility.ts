/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import UIExtensionAbility from '@ohos.app.ability.UIExtensionAbility'
import UIExtensionContentSession from '@ohos.app.ability.UIExtensionContentSession';
import Want from '@ohos.app.ability.Want';
import { common } from '@kit.AbilityKit';

const TAG: string = 'UIExtAbility';

export default class PCUIExtentionAbility extends UIExtensionAbility {
  public static globalContext: common.UIExtensionContext | null = null;

  onCreate(): void {
    HiAiLog.info(TAG, `onCreate`);
    PCUIExtentionAbility.globalContext = this.context;
  }

  onForeground(): void {
    HiAiLog.info(TAG, `onForeground`);
  }

  onBackground(): void {
    HiAiLog.info(TAG, `onBackground`);
  }

  onDestroy(): void {
    HiAiLog.info(TAG, `onDestroy`);
  }

  onSessionCreate(want: Want, session: UIExtensionContentSession): void {
    HiAiLog.info(TAG, `onSessionCreate, session: ${JSON.stringify(session)}`);
    let storage: LocalStorage = new LocalStorage();
    storage.setOrCreate('session', session);

    session.loadContent('modelManager/modelDownloadOta/modelDownloadPages/pages/PCModelDownloadPage', storage);
    session.setWindowBackgroundColor('#00000000')

    HiAiLog.info(TAG, 'onSessionCreate');
  }

  onSessionDestroy(session: UIExtensionContentSession): void | Promise<void> {
    HiAiLog.info(TAG, `onSessionDestroy`);
  }
};