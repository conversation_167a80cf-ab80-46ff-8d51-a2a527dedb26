/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import HashMap from '@ohos.util.HashMap';
import IHiAICapability from './IHiAICapability';
import AbilityLabelCapability from '../labeling/AbilityLabelCapability';
import ObjectUtil from '@hms-ai/pdkfull/src/main/ets/utils/ObjectUtil';
import { Capability } from '@hms-ai/pdkfull/src/main/ets/interfaces/FrameworkCapability';
import PluginInfoCapability from './ability/PluginInfoCapability';
import ModelDownloadCapability from '../modelManager/ModelDownloadCapability';

/**
 * This factory class is used to create the basic capability object of the framework according to the capability ID.
 * These objects are used to handle client related requests. For a list of IDs see { Capability }
 * @see Capability
 * <AUTHOR>
 */
export default class CapabilityFactory {
    private static capabilities = new HashMap<number, IHiAICapability>();

    /**
     * According to the capability ID, create a capability object corresponding to the capability
     * to handle related client requests.
     * @param { number } capabilityId the capability ID.
     * @return { IHiAICapability } the capability object
     */
    static createCapability(capabilityId: number): IHiAICapability | undefined {
        if (!ObjectUtil.isNullObject(CapabilityFactory.capabilities.get(capabilityId))) {
            return CapabilityFactory.capabilities.get(capabilityId);
        }
        switch (capabilityId) {
            case Capability.LABELING_CAPABILITY: {
                let labelCapability: AbilityLabelCapability = new AbilityLabelCapability();
                CapabilityFactory.capabilities.set(capabilityId, labelCapability);
                return labelCapability;
            }
            case Capability.PLUGIN_INFO_MANAGER_CAPABILITY: {
                let pluginInfoCapability: PluginInfoCapability = new PluginInfoCapability();
                CapabilityFactory.capabilities.set(capabilityId, pluginInfoCapability);
                return pluginInfoCapability;
            }
            case Capability.MODEL_DOWNLOAD_CAPABILITY: {
                let modelDownloadCapability: ModelDownloadCapability = new ModelDownloadCapability();
                CapabilityFactory.capabilities.set(capabilityId, modelDownloadCapability);
                return modelDownloadCapability;
            }
            default:
                return undefined;
        }
    }
}