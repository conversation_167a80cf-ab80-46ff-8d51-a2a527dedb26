/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import AbilityStage from "@ohos.app.ability.AbilityStage";
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import GlobalThisAbilityHelper from '@hms-ai/pdkfull/src/main/ets/abilityservice/GlobalThisAbilityHelper';
import BundleResourceManager from '@hms-ai/pdkfull/src/main/ets/platform/oswrapper/BundleResourceManager'
import ModuleInfo from '@hms-ai/pdkfull/src/main/ets/abilityservice/information/ModuleInfo';
import { BusinessError } from '@hms-ai/pdkfull/src/main/ets/interfaces/ICallback';
import { GlobalContextUpdateKey } from '@hms-ai/pdkfull/src/main/ets/utils/GlobalContext';

const TAG: string = 'HiAiServiceStage';

/**
 * The stage model of hiaiservice
 */
export default class HiAiServiceStage extends AbilityStage {
    onCreate(): void {
        HiAiLog.info(TAG, `AbilityStage onCreate`);
        // the path of resource file
        GlobalContextUpdateKey('GlobalContextUpdateKey', this.context.bundleCodeDir);
        // Record the sandbox path
        GlobalContextUpdateKey('filesPath', this.context.filesDir);
        GlobalContextUpdateKey('StageContext', this.context);
        this.init();
    }

    private init(): void {
        // init ability information
        BundleResourceManager.getInstance().getModuleInfoList((error: BusinessError, data: Array<ModuleInfo> | null): void => {
            let moduleNameList = new Array() as Array<string>;
            if (data) {
                data.forEach((moduleInfo: ModuleInfo) => {
                    moduleNameList.push(moduleInfo.name);
                })
                GlobalThisAbilityHelper.initModuleKey(moduleNameList);
            }
        });
    }
}