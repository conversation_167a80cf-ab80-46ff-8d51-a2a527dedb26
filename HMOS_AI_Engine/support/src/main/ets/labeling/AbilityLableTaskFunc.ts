/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import SystemParameterHelper from '@hms-ai/pdkfull/src/main/ets/platform/oswrapper/SystemParameterHelper';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';

/**
 * get systemChipType task
 */
const mGetChipTypeTaskFunc = function(): string {
    "use concurrent"
    const TAG: string = "TaskPool_mGetChipTypeTaskFunc";
    let chipLabel: string = SystemParameterHelper.getChipType()
    HiAiLog.info(TAG, `chip label is ${chipLabel}`);
    return chipLabel;
}

/**
 * get system product task
 */
const mGetProductModelTaskFunc = function(): string {
    "use concurrent"
    const TAG: string = "TaskPool_mGetProductModelTaskFunc";
    let productModel: string = SystemParameterHelper.getDeviceModel()
    HiAiLog.info(TAG, `product label is ${productModel}`);
    return productModel;
}

export { mGetChipTypeTaskFunc, mGetProductModelTaskFunc }