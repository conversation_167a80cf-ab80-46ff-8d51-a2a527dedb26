/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import taskpool from '@ohos.taskpool';
import { mGetChipTypeTaskFunc, mGetProductModelTaskFunc } from './AbilityLableTaskFunc';
import IHiAICapability from '../framework/IHiAICapability';
import LabelInfo from '@hms-ai/pdkfull/src/main/ets/abilityservice/labeling/LabelInfo';
import { LabelType } from '@hms-ai/pdkfull/src/main/ets/abilityservice/labeling/LabelType';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import LabelInfoCallbackStub from '@hms-ai/pdkfull/src/main/ets/abilityservice/labeling/LabelInfoCallbackStub';
import { RunningErrorCode } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import JsonUtil from '@hms-ai/pdkfull/src/main/ets/utils/JsonUtil';
import AbilityLabelManager from '@hms-ai/pdkfull/src/main/ets/abilityservice/labeling/AbilityLabelManager';
import { GlobalContextGetKey } from '@hms-ai/pdkfull/src/main/ets/utils/GlobalContext';
import InfoCallback from '@hms-ai/pdkfull/src/main/ets/interfaces/IInfoCallback'

const TAG: string = "AbilityLabelCapability";

/**
 * This class is used to get device-related labels.
 * @class
 * <AUTHOR>
 */
export default class AbilityLabelCapability implements IHiAICapability {
    /**
     * @override
     */
    onRemoteRequestHandle(data: rpc.MessageSequence, reply: rpc.MessageSequence | null, options: rpc.MessageOption | null): boolean | Promise<boolean> {
        let token: string = data.readInterfaceToken();
        if (AbilityLabelManager.DESCRIPTOR !== token) {
            HiAiLog.error(TAG, "token is error");
            return false;
        }
        let requestMethod: string = data.readString();
        let methods: string[] = Object.getOwnPropertyNames(Object.getPrototypeOf(this));
        HiAiLog.info(TAG, `===---methods: ${JSON.stringify(methods)}`);
        if (methods.includes(requestMethod)) {
            HiAiLog.info(TAG, 'The method is correct!');
            if (requestMethod === 'getAllLabel') {
                this.getAllLabel(data, reply, this)
            }
            HiAiLog.info(TAG, `===---requestMethod =: ${requestMethod}`)
            if (reply) {
                reply.writeNoException();
            }
            return true;
        } else {
            HiAiLog.error(TAG, 'Wrong method name!');
            if (reply) {
                reply.writeNoException();
            }
            return false;
        }
    }

    /**
     * Get all label of HiAI
     * @return { Promise<Array<LabelInfo>> } The retrieved label results
     */
    async getAllLabel(data: rpc.MessageSequence | null, reply: rpc.MessageSequence | null, capability: AbilityLabelCapability | null): Promise<void> {
        if (data) {
            let labelInfoCallback: rpc.IRemoteObject = data.readRemoteObject();
            let labelInfoCallbackProxy: InfoCallback = LabelInfoCallbackStub.asInterface(labelInfoCallback)
            let labels: Array<LabelInfo> = new Array<LabelInfo>();
            if (capability) {
                let chipLabel: LabelInfo = await capability.getChipType();
                labels.push(chipLabel);
                let regionLabel: LabelInfo = await capability.getRegionLabel();
                labels.push(regionLabel);
                let productLabel: LabelInfo = await capability.getProductModel();
                labels.push(productLabel);
            }
            let labelContent: string = JsonUtil.classToString(labels);
            labelInfoCallbackProxy.onResult(RunningErrorCode.SUCCESS, labelContent);
        }
    }

    /**
     * Generate chip label information.
     * @return { LabelInfo } the chip label
     */
    async getChipType(): Promise<LabelInfo> {
        let mGetChipTypeTask: taskpool.Task = new taskpool.Task(mGetChipTypeTaskFunc);
        let mGetChipTypeTaskRes: string = await taskpool.execute(mGetChipTypeTask) as string;
        let labelInfo: LabelInfo = new LabelInfo();
        labelInfo.labelName = LabelType.CHIP_LABEL_TYPE;
        labelInfo.labelValue = mGetChipTypeTaskRes;
        return labelInfo;
    }

    /**
     * Generate product information.
     * @return { Promise<LabelInfo> } the product label
     */
    async getProductModel(): Promise<LabelInfo> {
        let mGetProductModelTask: taskpool.Task = new taskpool.Task(mGetProductModelTaskFunc);
        let mGetProductModelTaskRes: string = await taskpool.execute(mGetProductModelTask) as string;
        let labelInfo: LabelInfo = new LabelInfo();
        labelInfo.labelName = LabelType.PRODUCT_LABEL_TYPE;
        labelInfo.labelValue = mGetProductModelTaskRes;
        return labelInfo;
    }

    async getRegionLabel(): Promise<LabelInfo> {
        let defaultRegion: string = "china";
        let labelInfo: LabelInfo = new LabelInfo();
        let globalRegion: Object | undefined = GlobalContextGetKey('hiairegion');
        labelInfo.labelName = LabelType.REGION_LABEL_TYPE;
        labelInfo.labelValue = ((globalRegion === null || globalRegion === undefined) ? defaultRegion : globalRegion) as string;
        return labelInfo;
    }
}