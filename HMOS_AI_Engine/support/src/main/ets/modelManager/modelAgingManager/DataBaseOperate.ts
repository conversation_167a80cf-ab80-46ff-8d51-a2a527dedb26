/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { ModelDownloadResultCode } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import JsonUtil, { StoredModelInfo, UsageStatus, VersionInfo } from '../utils/JsonUtil';
import ModelBaseInfo from '../modelDownloadOta/modelDownloadInfo/ModelBaseInfo'
import QueryResultInfo from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/modelQuery/ModelQueryResultInfo';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import CommonConstants from '../utils/constants/CommonConstants';
import ModelAgingManager from './ModelAgingManager';
import StorageLevelWhiteListConstants from '../utils/constants/StorageLevelWhiteListConstants';
import fs from '@ohos.file.fs';
import HiAIServiceAbility from '../../framework/HiAIServiceAbility';
import { JSON } from '@kit.ArkTS';

const TAG: string = "DataBaseOperate";

// Database operate class
export default class DataBaseOperate {
  private static instance: DataBaseOperate;

  /**
   * Gets the singleton instance of DataBaseOperate.
   *
   * @returns {DataBaseOperate} The singleton instance of DataBaseOperate.
   */
  public static getInstance(): DataBaseOperate {
    if (!DataBaseOperate.instance) {
      DataBaseOperate.instance = new DataBaseOperate();
    }
    return DataBaseOperate.instance;
  }


  /**
   * Adds a downloaded model to the storage list.
   *
   * @param {ModelBaseInfo} modelBaseInfo - The base information of the model.
   * @param {string} storedPath - The path where the model is stored.
   * @returns {Promise<void>} A promise that resolves when the model is added.
   */
  public async addToDownloadedList(modelBaseInfo: ModelBaseInfo, storedPath: string): Promise<void> {
    try {
      const modelDomain = modelBaseInfo.getModelUpdateInfo().pluginInfos[0].packageName;
      const modelName = modelBaseInfo.getModelUpdateInfo().pluginInfos[0].pluginName;
      const modelVersion = modelBaseInfo.getModelVersion();
      const modelType = modelBaseInfo.getModelUpdateInfo().pluginInfos[0].pluginType;
      const now = new Date();

      const newVersionInfo: VersionInfo = {
        version: modelVersion,
        storedTime: now.toLocaleString(),
        modelSize: modelBaseInfo.getModelSize(),
        status: UsageStatus.UNUSED,
        sourcePath: storedPath,
        deleteTime: CommonConstants.DEFAULT_INT
      };

      // Add to EL2 level
      const storedModel = await JsonUtil.getModel(
        modelBaseInfo.getModelUpdateInfo().pluginInfos[0].packageName,
        modelName,
        modelBaseInfo.getModelUpdateInfo().pluginInfos[0].compatibleVersion
      );

      if (storedModel) {
        if (!storedModel.versionInfos.some(vi => vi.version === modelVersion)) {
          storedModel.versionInfos.push(newVersionInfo);
          await JsonUtil.updateModel(storedModel);
        }
      } else {
        const newModel: StoredModelInfo = {
          domain: modelBaseInfo.getModelUpdateInfo().pluginInfos[0].packageName,
          modelName: modelName,
          resCompatibleVersion: modelBaseInfo.getModelUpdateInfo().pluginInfos[0].compatibleVersion,
          modelType: modelType,
          isSubscribed: true,
          lastQueryVersion: CommonConstants.EMPTY_STRING,
          lastQueryTime: CommonConstants.DEFAULT_INT,
          versionInfos: [newVersionInfo]
        };
        await JsonUtil.addModel(newModel);
      }

      // Add to EL1 level if model is in white list
      if (modelDomain === StorageLevelWhiteListConstants.TTS) {
        const el1StoredModel = await JsonUtil.getModel(
          modelBaseInfo.getModelUpdateInfo().pluginInfos[0].packageName,
          modelName,
          modelBaseInfo.getModelUpdateInfo().pluginInfos[0].compatibleVersion,
          true
        );

        if (el1StoredModel) {
          if (!el1StoredModel.versionInfos.some(vi => vi.version === modelVersion)) {
            el1StoredModel.versionInfos.push(newVersionInfo);
            await JsonUtil.updateModel(el1StoredModel, true);
          }
        } else {
          const newEl1Model: StoredModelInfo = {
            domain: modelBaseInfo.getModelUpdateInfo().pluginInfos[0].packageName,
            modelName: modelName,
            resCompatibleVersion: modelBaseInfo.getModelUpdateInfo().pluginInfos[0].compatibleVersion,
            modelType: modelType,
            isSubscribed: true,
            lastQueryVersion: CommonConstants.EMPTY_STRING,
            lastQueryTime: CommonConstants.DEFAULT_INT,
            versionInfos: [newVersionInfo]
          };
          await JsonUtil.addModel(newEl1Model, true);
        }
      }
    } catch (error) {
      HiAiLog.error(TAG, `Failed to add model to downloaded list: ${error}`);
    }
  }

  /**
   * Creates a query result object with the given parameters.
   *
   * @param {ModelDownloadInfo} modelInfo - The model download information.
   * @param {VersionInfo} versionInfo - The version information of the model.
   * @param {number} [errorCode=0] - The error code of the query result.
   * @param {string} [errorMessage=""] - The error message of the query result.
   * @returns {QueryResultInfo} The created query result object.
   */
  private createQueryResult(modelInfo: ModelDownloadInfo, versionInfo: VersionInfo, errorCode: number = 0,
    errorMessage: string = CommonConstants.EMPTY_STRING): QueryResultInfo {
    const queryResultInfo = new QueryResultInfo();
    queryResultInfo.resId = modelInfo?.resId || CommonConstants.EMPTY_STRING;
    queryResultInfo.domain = modelInfo?.domain || CommonConstants.EMPTY_STRING;
    queryResultInfo.resCompatibleVersion = modelInfo?.resCompatibleVersion || CommonConstants.EMPTY_STRING;
    queryResultInfo.resVersion = versionInfo?.version || CommonConstants.EMPTY_STRING;
    queryResultInfo.sourcePath = versionInfo?.sourcePath || CommonConstants.EMPTY_STRING;
    queryResultInfo.errorCode = errorCode;
    queryResultInfo.errorMessage = errorMessage;
    return queryResultInfo;
  }

  /**
   * Checks if a file exists at the given path.
   *
   * @param {string} filePath - The path to check.
   * @returns {boolean} True if the file exists, false otherwise.
   */
  private checkFileExists(filePath: string): boolean {
    try {
      return fs.accessSync(filePath);
    } catch (error) {
      HiAiLog.error(TAG, `File does not exist at path: ${filePath}`);
      return false;
    }
  }

  /**
   * Cleans up database records when model files don't exist.
   *
   * @param {StoredModelInfo} storedModel - The model information from database.
   * @param {boolean} isEl1 - Whether to clean up EL1 level records.
   * @returns {Promise<void>} A promise that resolves when cleanup is complete.
   */
  private async cleanupMissingModelFiles(storedModel: StoredModelInfo, isEl1: boolean = false): Promise<void> {
    try {
      // Filter out versions whose files don't exist
      const validVersions = storedModel.versionInfos.filter(version =>
        version.sourcePath && this.checkFileExists(version.sourcePath)
      );

      if (validVersions.length === 0) {
        // If no valid versions left, delete the entire model record
        await JsonUtil.deleteModel(
          storedModel.domain,
          storedModel.modelName,
          storedModel.resCompatibleVersion,
          undefined,
          isEl1
        );
        HiAiLog.info(TAG, `Deleted model record for ${storedModel.modelName} as all files are missing`);
      } else if (validVersions.length !== storedModel.versionInfos.length) {
        // If some versions are missing, update the model record with only valid versions
        storedModel.versionInfos = validVersions;
        await JsonUtil.updateModel(storedModel, isEl1);
        HiAiLog.info(TAG, `Updated model record for ${storedModel.modelName} to remove missing versions`);
      }
    } catch (error) {
      HiAiLog.error(TAG, `Failed to cleanup missing model files: ${error}`);
    }
  }

  /**
   * Constructs the expected model file path based on model information.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @param {string} version - The specific version of the model.
   * @param {boolean} isEl1 - Whether to use EL1 level path.
   * @returns {string} The constructed model file path.
   */
  private constructModelFilePath(domain: string, modelName: string, resCompatibleVersion: string,
    version: string, isEl1: boolean = false): string {
    const baseDir = isEl1 ?
      '/data/storage/el1/base/haps/support/files' :
      '/data/storage/el2/base/haps/support/files';
    return `${baseDir}/${domain}/${modelName}/${resCompatibleVersion}/${version}.zip`;
  }

  /**
   * Scans local model files and synchronizes them to JSON storage.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @param {boolean} isEl1 - Whether to scan EL1 level files.
   * @returns {Promise<StoredModelInfo | null>} The synchronized model info or null if no files found.
   */
  private async scanAndSyncLocalModelFiles(domain: string, modelName: string,
    resCompatibleVersion: string, isEl1: boolean = false): Promise<StoredModelInfo | null> {
    try {
      const baseDir = isEl1 ?
        '/data/storage/el1/base/haps/support/files' :
        '/data/storage/el2/base/haps/support/files';
      const modelDir = `${baseDir}/${domain}/${modelName}/${resCompatibleVersion}`;

      HiAiLog.info(TAG, `Scanning for model files in directory: ${modelDir}`);

      // Check if model directory exists
      if (!this.checkFileExists(modelDir)) {
        HiAiLog.info(TAG, `Model directory does not exist: ${modelDir}`);
        return null;
      }

      // List all files in the model directory
      const files = await fs.listFile(modelDir);
      const modelFiles = files.filter(file => file.endsWith('.zip'));

      if (modelFiles.length === 0) {
        HiAiLog.info(TAG, `No model files found in directory: ${modelDir}`);
        return null;
      }

      HiAiLog.info(TAG, `Found ${modelFiles.length} model files: ${JSON.stringify(modelFiles)}`);

      // Create version infos for found files
      const versionInfos: VersionInfo[] = [];
      const now = new Date();

      for (const file of modelFiles) {
        const version = file.replace('.zip', '');
        const filePath = `${modelDir}/${file}`;

        // Get file stats for size and creation time
        const stats = await fs.stat(filePath);
        const modelSize = Math.round(stats.size / (1024 * 1024)); // Convert to MB

        const versionInfo: VersionInfo = {
          version: version,
          storedTime: now.toLocaleString(),
          modelSize: modelSize,
          status: UsageStatus.UNUSED,
          sourcePath: filePath,
          deleteTime: CommonConstants.DEFAULT_INT
        };

        versionInfos.push(versionInfo);
      }

      // Create or update model info
      const modelInfo: StoredModelInfo = {
        domain: domain,
        modelName: modelName,
        resCompatibleVersion: resCompatibleVersion,
        modelType: CommonConstants.EMPTY_STRING, // Will be updated when actual model type is known
        isSubscribed: true,
        lastQueryVersion: CommonConstants.EMPTY_STRING,
        lastQueryTime: CommonConstants.DEFAULT_INT,
        versionInfos: versionInfos
      };

      // Check if model already exists to avoid duplicates
      const existingModel = await JsonUtil.getModel(domain, modelName, resCompatibleVersion, isEl1);
      if (existingModel) {
        HiAiLog.info(TAG, `Model already exists in JSON, merging version information`);
        // Merge version infos, avoiding duplicates
        for (const newVersion of versionInfos) {
          const existingVersion = existingModel.versionInfos.find(v => v.version === newVersion.version);
          if (!existingVersion) {
            existingModel.versionInfos.push(newVersion);
          }
        }
        await JsonUtil.updateModel(existingModel, isEl1);
        HiAiLog.info(TAG, `Successfully merged ${versionInfos.length} model versions to existing JSON record`);
        return existingModel;
      } else {
        // Add new model to JSON storage
        await JsonUtil.addModel(modelInfo, isEl1);
        HiAiLog.info(TAG, `Successfully synchronized ${versionInfos.length} model versions to JSON storage`);
        return modelInfo;
      }
    } catch (error) {
      HiAiLog.error(TAG, `Failed to scan and sync local model files: ${error}`);
      return null;
    }
  }

  /**
   * Queries the latest model information and updates its usage status.
   *
   * @param {ModelDownloadInfo} modelInfo - The model information to query.
   * @returns {Promise<QueryResultInfo>} A promise that resolves with the query result.
   */
  public async queryLatestModelInfo(modelInfo: ModelDownloadInfo): Promise<QueryResultInfo> {
    try {
      if (!modelInfo?.domain || !modelInfo?.resId || !modelInfo?.resCompatibleVersion) {
        HiAiLog.error(TAG,
          `Invalid parameters: domain = ${modelInfo?.domain}, resId = ${modelInfo?.resId}, resCompatibleVersion = ${modelInfo?.resCompatibleVersion}`);
        let emptyModelInfo = new ModelDownloadInfo();
        emptyModelInfo.domain = CommonConstants.EMPTY_STRING;
        emptyModelInfo.resId = CommonConstants.EMPTY_STRING;
        emptyModelInfo.resVersion = CommonConstants.EMPTY_STRING;
        emptyModelInfo.resCompatibleVersion = CommonConstants.EMPTY_STRING;
        return this.createQueryResult(modelInfo || emptyModelInfo, {
          version: modelInfo.resVersion,
          storedTime: CommonConstants.EMPTY_STRING,
          modelSize: CommonConstants.DEFAULT_INT,
          status: UsageStatus.UNUSED,
          sourcePath: CommonConstants.EMPTY_STRING,
          deleteTime: CommonConstants.DEFAULT_INT
        },
          ModelDownloadResultCode.PARAMATER_INVALID,
          'Invalid parameters: Required fields are missing'
        );
      }

      // Check if model is in white list
      const isWhiteListModel = modelInfo.domain === StorageLevelWhiteListConstants.TTS;
      
      // Try to get model from EL1 level first if it's in white list
      let storedModel = isWhiteListModel ? 
        await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion, true) : null;

      // If not found in EL1 or not in white list, try EL2 level
      if (!storedModel) {
        storedModel = await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion);
      } else {
        HiAiLog.info(TAG,`Target model is in el1`);
      }

      if (!storedModel) {
        HiAiLog.info(TAG, `Model ${modelInfo.resId} with domain ${modelInfo.domain} not found in JSON, attempting to scan local files`);

        // Try to scan and sync local model files
        const syncedModel = await this.scanAndSyncLocalModelFiles(
          modelInfo.domain,
          modelInfo.resId,
          modelInfo.resCompatibleVersion,
          isWhiteListModel
        );

        if (syncedModel) {
          HiAiLog.info(TAG, `Successfully synchronized local model files for ${modelInfo.resId}`);
          storedModel = syncedModel;
        } else {
          HiAiLog.info(TAG, `No local model files found for ${modelInfo.resId}`);
          return this.createQueryResult(modelInfo, {
            version: modelInfo.resVersion,
            storedTime: CommonConstants.EMPTY_STRING,
            modelSize: CommonConstants.DEFAULT_INT,
            status: UsageStatus.UNUSED,
            sourcePath: CommonConstants.EMPTY_STRING,
            deleteTime: CommonConstants.DEFAULT_INT
          }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
            `Model ${modelInfo.resId} not found`);
        }
      }

      await ModelAgingManager.getInstance()
        .handleModelAging(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion, true);
      await ModelAgingManager.getInstance()
        .checkAndCleanExpiredModels(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion);

      // Get model again after aging check
      storedModel = isWhiteListModel ? 
        await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion, true) :
        await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion);

      if (!storedModel) {
        HiAiLog.info(TAG, `Model ${modelInfo.resId} not found after aging check, attempting to scan local files again`);

        // Try to scan and sync local model files again after aging check
        const syncedModel = await this.scanAndSyncLocalModelFiles(
          modelInfo.domain,
          modelInfo.resId,
          modelInfo.resCompatibleVersion,
          isWhiteListModel
        );

        if (syncedModel) {
          HiAiLog.info(TAG, `Successfully synchronized local model files for ${modelInfo.resId} after aging check`);
          storedModel = syncedModel;
        } else {
          HiAiLog.info(TAG, `No local model files found for ${modelInfo.resId} after aging check`);
          return this.createQueryResult(modelInfo, {
            version: modelInfo.resVersion,
            storedTime: CommonConstants.EMPTY_STRING,
            modelSize: CommonConstants.DEFAULT_INT,
            status: UsageStatus.UNUSED,
            sourcePath: CommonConstants.EMPTY_STRING,
            deleteTime: CommonConstants.DEFAULT_INT
          }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
            `Model ${modelInfo.resId} not found after aging check`);
        }
      }

      // Find the latest version index
      const latestVersionIndex = storedModel.versionInfos.reduce((maxIndex, current, currentIndex, arr) => {
        if (maxIndex === -1) {
          return currentIndex;
        }
        return this.versionToNumber(current.version) > this.versionToNumber(arr[maxIndex].version) ? currentIndex :
          maxIndex;
      }, -1);

      if (latestVersionIndex === -1) {
        HiAiLog.info(TAG, `No versions found for model ${modelInfo.resId}`);
        return this.createQueryResult(modelInfo, {
          version: modelInfo.resVersion,
          storedTime: CommonConstants.EMPTY_STRING,
          modelSize: CommonConstants.DEFAULT_INT,
          status: UsageStatus.UNUSED,
          sourcePath: CommonConstants.EMPTY_STRING,
          deleteTime: CommonConstants.DEFAULT_INT
        }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
          `No versions found for model ${modelInfo.resId}`);
      }

      const latestVersion = storedModel.versionInfos[latestVersionIndex];
      
      // Check if the model file actually exists
      if (!this.checkFileExists(latestVersion.sourcePath)) {
        HiAiLog.error(TAG, `Model file does not exist at path: ${latestVersion.sourcePath}`);
        // Clean up database records for missing files
        await this.cleanupMissingModelFiles(storedModel, isWhiteListModel);
        return this.createQueryResult(modelInfo, {
          version: latestVersion.version,
          storedTime: latestVersion.storedTime,
          modelSize: latestVersion.modelSize,
          status: UsageStatus.UNUSED,
          sourcePath: CommonConstants.EMPTY_STRING,
          deleteTime: latestVersion.deleteTime
        }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
          `Model file does not exist at path: ${latestVersion.sourcePath}`);
      }

      const currentTime = Date.now();
      storedModel.lastQueryVersion = latestVersion.version;
      storedModel.lastQueryTime = currentTime;
      latestVersion.status = UsageStatus.BEING_USED;

      // Update model info in the appropriate level
      await JsonUtil.updateModel(storedModel, isWhiteListModel);

      return this.createQueryResult(modelInfo, latestVersion,
        ModelDownloadResultCode.SUCCESS, 'Query target model success');
    } catch (error) {
      HiAiLog.error(TAG, `Failed to query model info: ${error}`);
      return this.createQueryResult(modelInfo, {
        version: modelInfo.resVersion,
        storedTime: CommonConstants.EMPTY_STRING,
        modelSize: CommonConstants.DEFAULT_INT,
        status: UsageStatus.UNUSED,
        sourcePath: CommonConstants.EMPTY_STRING,
        deleteTime: CommonConstants.DEFAULT_INT
      }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
        `Failed to query model: ${error.message}`);
    }
  }

  /**
   * Queries all versions of a model.
   *
   * @param {ModelDownloadInfo} modelInfo - The model information to query.
   * @returns {Promise<Array<QueryResultInfo>>} A promise that resolves with an array of query results.
   */
  public async queryFullModelInfo(modelInfo: ModelDownloadInfo): Promise<Array<QueryResultInfo>> {
    try {
      // Check if model is in white list
      const isWhiteListModel = modelInfo.domain === StorageLevelWhiteListConstants.TTS;
      
      // Try to get model from EL1 level first if it's in white list
      let storedModel = isWhiteListModel ? 
        await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion, true) : null;

      // If not found in EL1 or not in white list, try EL2 level
      if (!storedModel) {
        storedModel = await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion);
      }

      if (!storedModel) {
        HiAiLog.info(TAG, `Model ${modelInfo.resId} with domain ${modelInfo.domain} not found in JSON, attempting to scan local files`);

        // Try to scan and sync local model files
        const syncedModel = await this.scanAndSyncLocalModelFiles(
          modelInfo.domain,
          modelInfo.resId,
          modelInfo.resCompatibleVersion,
          isWhiteListModel
        );

        if (syncedModel) {
          HiAiLog.info(TAG, `Successfully synchronized local model files for ${modelInfo.resId}`);
          storedModel = syncedModel;
        } else {
          HiAiLog.info(TAG, `No local model files found for ${modelInfo.resId}`);
          return [this.createQueryResult(modelInfo, {
            version: modelInfo.resVersion,
            storedTime: CommonConstants.EMPTY_STRING,
            modelSize: CommonConstants.DEFAULT_INT,
            status: UsageStatus.UNUSED,
            sourcePath: CommonConstants.EMPTY_STRING,
            deleteTime: CommonConstants.DEFAULT_INT
          }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
            `Model ${modelInfo.resId} not found`
          )];
        }
      }

      if (!storedModel.versionInfos || storedModel.versionInfos.length === 0) {
        HiAiLog.info(TAG, `No versions found for model ${modelInfo.resId}`);
        return [this.createQueryResult(modelInfo, {
          version: modelInfo.resVersion,
          storedTime: CommonConstants.EMPTY_STRING,
          modelSize: CommonConstants.DEFAULT_INT,
          status: UsageStatus.UNUSED,
          sourcePath: CommonConstants.EMPTY_STRING,
          deleteTime: CommonConstants.DEFAULT_INT
        }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
          `No versions found for model ${modelInfo.resId}`
        )];
      }

      // Check for missing files and clean up database records
      await this.cleanupMissingModelFiles(storedModel, isWhiteListModel);

      // Get updated model info after cleanup
      storedModel = isWhiteListModel ? 
        await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion, true) :
        await JsonUtil.getModel(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion);

      if (!storedModel || !storedModel.versionInfos || storedModel.versionInfos.length === 0) {
        return [this.createQueryResult(modelInfo, {
          version: modelInfo.resVersion,
          storedTime: CommonConstants.EMPTY_STRING,
          modelSize: CommonConstants.DEFAULT_INT,
          status: UsageStatus.UNUSED,
          sourcePath: CommonConstants.EMPTY_STRING,
          deleteTime: CommonConstants.DEFAULT_INT
        }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
          `No valid versions found for model ${modelInfo.resId} after cleanup`
        )];
      }

      const results: Array<QueryResultInfo> = [];

      for (const versionInfo of storedModel.versionInfos) {
        if (!versionInfo.sourcePath) {
          results.push(this.createQueryResult(modelInfo, {
            version: versionInfo.version,
            storedTime: versionInfo.storedTime,
            modelSize: versionInfo.modelSize,
            status: versionInfo.status,
            sourcePath: CommonConstants.EMPTY_STRING,
            deleteTime: versionInfo.deleteTime
          }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
            `Source path not found for version ${versionInfo.version}`
          ));
        } else if (!this.checkFileExists(versionInfo.sourcePath)) {
          results.push(this.createQueryResult(modelInfo, {
            version: versionInfo.version,
            storedTime: versionInfo.storedTime,
            modelSize: versionInfo.modelSize,
            status: versionInfo.status,
            sourcePath: CommonConstants.EMPTY_STRING,
            deleteTime: versionInfo.deleteTime
          }, ModelDownloadResultCode.QUERY_LOCAL_FAILED,
            `Model file does not exist at path: ${versionInfo.sourcePath}`
          ));
        } else {
          results.push(this.createQueryResult(modelInfo, versionInfo, ModelDownloadResultCode.SUCCESS,
            `Query model version ${versionInfo.version} success`
          ));
        }
      }
      return results;

    } catch (error) {
      HiAiLog.error(TAG, `Failed to query full model info: ${error}`);
      return [this.createQueryResult(
        modelInfo, {
          version: modelInfo.resVersion,
          storedTime: CommonConstants.EMPTY_STRING,
          modelSize: CommonConstants.DEFAULT_INT,
          status: UsageStatus.UNUSED,
          sourcePath: CommonConstants.EMPTY_STRING,
          deleteTime: CommonConstants.DEFAULT_INT
        }, ModelDownloadResultCode.QUERY_LOCAL_FAILED, `Failed to query model: ${error.message}`
      )];
    }
  }

  /**
   * Deletes model data. If modelVersion is provided, deletes specific version; otherwise deletes all versions.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} [resCompatibleVersion] - Optional compatible version to delete.
   * @param {string} [version] - Optional specific version to delete.
   * @returns {Promise<boolean>} A promise that resolves to true if deletion was successful.
   */
  public async deleteModelData(domain: string, modelName: string, resCompatibleVersion?: string,
    version?: string): Promise<boolean> {
    try {
      // Delete from EL2 level
      const el2Result = await JsonUtil.deleteModel(domain, modelName, resCompatibleVersion, version);

      // Delete from EL1 level if model is in white list
      if (domain === StorageLevelWhiteListConstants.TTS) {
        await JsonUtil.deleteModel(domain, modelName, resCompatibleVersion, version, true);
      }

      return el2Result;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to delete model data: ${error}`);
      return false;
    }
  }

  /**
   * Checks if a specific model version is downloaded.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @param {string} version - The specific version to check.
   * @returns {Promise<boolean>} A promise that resolves to true if the version is downloaded.
   */
  public async isVersionDownloaded(domain: string, modelName: string, resCompatibleVersion: string,
    version: string): Promise<boolean> {
    try {
      const storedModel = await JsonUtil.getModel(domain, modelName, resCompatibleVersion);
      if (!storedModel) {
        return false;
      }
      
      const versionInfo = storedModel.versionInfos.find(vi => vi.version === version);
      if (!versionInfo || !versionInfo.sourcePath) {
        return false;
      }
      
      return this.checkFileExists(versionInfo.sourcePath);
    } catch (error) {
      HiAiLog.error(TAG, `Failed to check version: ${error}`);
      return false;
    }
  }

  /**
   * Gets the stored time of a specific model version.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @param {string} version - The specific version to check.
   * @returns {Promise<string | null>} A promise that resolves with the stored time or null if not found.
   */
  public async getVersionStoredTime(domain: string, modelName: string, resCompatibleVersion: string,
    version: string): Promise<string | null> {
    try {
      const storedModel = await JsonUtil.getModel(domain, modelName, resCompatibleVersion);
      if (!storedModel) {
        HiAiLog.info(TAG, `Can't find target stored model when getting target model stored time : ${modelName}}`);
        return null;
      }
      const versionInfo = storedModel.versionInfos.find(vi => vi.version === version);
      return versionInfo ? versionInfo.storedTime : null;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to get stored time: ${error}`);
      return null;
    }
  }

  /**
   * Gets the scheduled deletion time of a aging model version.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @param {string} version - The specific version to check.
   * @returns {Promise<number | null>} A promise that resolves with the deletion time or null if not found.
   */
  public async getVersionDeleteTime(domain: string, modelName: string, resCompatibleVersion: string,
    version: string): Promise<number | null> {
    try {
      const storedModel = await JsonUtil.getModel(domain, modelName, resCompatibleVersion);
      if (!storedModel) {
        HiAiLog.info(TAG, `Can't find target stored model when getting target model delete time : ${modelName}}`);
        return null;
      }
      const versionInfo = storedModel.versionInfos.find(vi => vi.version === version);
      return versionInfo ? versionInfo.deleteTime : null;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to get stored time: ${error}`);
      return null;
    }
  }

  /**
   * Gets the source file path of a specific model version.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @param {string} version - The specific version to check.
   * @returns {string | null} The source path of the model or null if not found.
   */
  public getSourcePath(domain: string, modelName: string, resCompatibleVersion: string,
    version: string): string | null {
    try {
      const storedModel = JsonUtil.getModel(domain, modelName, resCompatibleVersion);
      if (!storedModel) {
        HiAiLog.info(TAG, `Can't find target stored model when getting source path : ${modelName}}`);
        return null;
      }
      let versionInfo = storedModel.versionInfos.find(vi => vi.version === version);
      return versionInfo ? versionInfo.sourcePath : null;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to get source path: ${error}`);
      return null;
    }
  }

  /**
   * Finds the latest version from an array of version information.
   *
   * @param {VersionInfo[]} versions - Array of version information to search through.
   * @returns {VersionInfo | null} The latest version information or null if array is empty.
   */
  private findLatestVersion(versions: VersionInfo[]): VersionInfo | null {
    return versions.reduce((latest, current) => {
      const currentValue = this.versionToNumber(current.version);
      return !latest || currentValue > this.versionToNumber(latest.version)
        ? current
        : latest;
    }, null);
  }

  /**
   * Converts version string to number for comparison.
   * Supports version numbers with any number of segments, each segment up to 999.
   *
   * @param {string} version - The version string to convert.
   * @returns {number} The numeric representation of the version.
   */
  public versionToNumber(version: string): number {
    const parts = version.split('.');
    let result = 0;
    const maxSegments = 9;
    for (let i = 0; i < Math.min(parts.length, maxSegments); i++) {
      const value = parseInt(parts[i]) || 0;
      result = result * 1000 + (value % 1000);
    }
    return result;
  }

  /**
   * Updates the status and deletion time of a specific model version.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @param {string} version - The specific version to update.
   * @param {UsageStatus} status - The new status to set.
   * @param {number} deleteTime - The new deletion time to set.
   * @returns {Promise<boolean>} A promise that resolves to true if update was successful.
   */
  public async updateModelStatusAndDeleteTime(domain: string, modelName: string,
    resCompatibleVersion: string, version: string, status: UsageStatus, deleteTime: number): Promise<boolean> {
    try {
      // Update EL2 level
      const storedModel = await JsonUtil.getModel(domain, modelName, resCompatibleVersion);
      if (!storedModel) {
        HiAiLog.info(TAG, `Can't find target stored model when updating model status and deleteTime: ${modelName}`);
        return false;
      }
      const versionInfo = storedModel.versionInfos.find(vi => vi.version === version);
      if (versionInfo) {
        versionInfo.status = status;
        versionInfo.deleteTime = deleteTime;
        await JsonUtil.updateModel(storedModel);
      }

      // Update EL1 level if model is in white list
      if (domain === StorageLevelWhiteListConstants.TTS) {
        const el1StoredModel = await JsonUtil.getModel(domain, modelName, resCompatibleVersion, true);
        if (el1StoredModel) {
          const el1VersionInfo = el1StoredModel.versionInfos.find(vi => vi.version === version);
          if (el1VersionInfo) {
            el1VersionInfo.status = status;
            el1VersionInfo.deleteTime = deleteTime;
            await JsonUtil.updateModel(el1StoredModel, true);
          }
        }
      }

      return true;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to update model status: ${error}`);
      return false;
    }
  }
}
