/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import ModelBaseInfo from '../modelDownloadOta/modelDownloadInfo/ModelBaseInfo'
import { hash, statfs } from '@kit.CoreFileKit';
import fs from '@ohos.file.fs';
import { zlib } from '@kit.BasicServicesKit';
import { util, xml } from '@kit.ArkTS';
import DataBaseOperate from './DataBaseOperate'
import ModelAgingManager from './ModelAgingManager';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { ModelDownloadResultCode, ReportResultCode } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import ModelInfoManager from '../modelDownloadOta/modelDownloadInfo/ModelInfoManager';
import ModelTaskManager from '../modelDownloadOta/ModelTaskManager';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import JsonUtil from '../utils/JsonUtil';
import CommonConstants from '../utils/constants/CommonConstants';
import { ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode'
import ModelDownloadCapability from '../ModelDownloadCapability';
import { ModelDownloadReportUtil } from '../utils/ModelManagerReportUtil';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import HiAIServiceAbility from '../../framework/HiAIServiceAbility';
import { contextConstant } from '@kit.AbilityKit';
import ModelDownloadUIExtentionAbility from '../../framework/ability/ModelDownloadUIExtensionAbility';

const TAG: string = "ModelManager";

// This class is used to decompress model full zip file and check its integrity
export default class ModelOperate {
  private static instance: ModelOperate;

  /**
   * Gets the singleton instance of ModelOperate.
   *
   * @returns {ModelOperate} The singleton instance of ModelOperate.
   */
  public static getInstance(): ModelOperate {
    if (!ModelOperate.instance) {
      ModelOperate.instance = new ModelOperate();
    }
    return ModelOperate.instance;
  }

  /**
   * Gets the available storage space for a given path.
   *
   * @param {string} path - The path to check for available space.
   * @returns {Promise<number>} A promise that resolves with the available space in bytes.
   */
  private async getAvailableSpace(path: string): Promise<number> {
    return new Promise((resolve, reject) => {
      try {
        statfs.getFreeSize(path, (err, freeSize) => {
          if (err) {
            HiAiLog.error(TAG, `Failed to get available space: ${err.message}, code: ${err.code}`);
            reject(err);
          } else {
            let freeSizeMB =
              Math.round(freeSize / (CommonConstants.CONSTANT_INT_BYTES * CommonConstants.CONSTANT_INT_BYTES));
            HiAiLog.info(TAG, `Available space: ${freeSizeMB} MB`);
            resolve(freeSizeMB);
          }
        });
      } catch (error) {
        HiAiLog.error(TAG, `Exception while getting available space: ${error}`);
        reject(error);
      }
    });
  }

  /**
   * Checks if there is sufficient storage space for the model.
   *
   * @param {ModelBaseInfo} modelBaseInfo - The base information of the model.
   * @param {string} path - The path where the model will be stored.
   * @returns {Promise<boolean>} A promise that resolves to true if there is sufficient space.
   */
  public async checkStorageSpace(modelBaseInfo: ModelBaseInfo, path: string): Promise<boolean> {
    const modelSize = modelBaseInfo.getModelSize();
    HiAiLog.info(TAG, `Model package size is ${modelSize} MB`);
    if (typeof modelSize !== 'number' || modelSize <= 0) {
      HiAiLog.error(TAG, `Invalid model size: ${modelSize}`);
      return false;
    }
    const requiredSpace = modelSize * CommonConstants.SPACE_MULTIPLIER;
    try {
      let availableSpace = await this.getAvailableSpace(path);
      HiAiLog.info(TAG, `check avaiable space is ${availableSpace}`);
      if (availableSpace < requiredSpace) {
        HiAiLog.error(TAG, `Insufficient storage space. Required: ${requiredSpace}, Available: ${availableSpace}`);
        return false;
      }
      return true;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to check storage space: ${error}`);
      return false;
    }
  }

  /**
   * Parses XML file to extract content of a specific tag.
   *
   * @param {string} xmlPath - The path to the XML file.
   * @param {string} targetTag - The tag whose content needs to be extracted.
   * @returns {Promise<string>} A promise that resolves with the content of the target tag.
   */
  private async parseXmlTagContent(xmlPath: string, targetTag: string): Promise<string> {
    try {
      const fileContent = await fs.readText(xmlPath);
      const textEncoder = new util.TextEncoder();
      const xmlBuffer = textEncoder.encodeInto(fileContent);
      const parser = new xml.XmlPullParser(xmlBuffer.buffer as ArrayBuffer, "utf-8");
      let tagContent = "";
      let currentTag = "";

      function parseCallback(eventType: xml.EventType, value: xml.ParseInfo): boolean {
        if (eventType === xml.EventType.START_TAG) {
          currentTag = value.getName();
          HiAiLog.info(TAG, `Start tag: ${currentTag}`);
        } else if (eventType === xml.EventType.TEXT && currentTag === targetTag) {
          tagContent = value.getText().trim();
          HiAiLog.info(TAG, `Found ${targetTag} content: ${tagContent}`);
          return false;
        } else if (eventType === xml.EventType.END_TAG) {
          currentTag = "";
        }
        return true;
      }

      parser.parseXml({
        supportDoctype: true,
        ignoreNameSpace: true,
        tokenValueCallbackFunction: parseCallback
      });

      if (!tagContent) {
        throw new Error(`Failed to find ${targetTag} in XML file`);
      }
      return tagContent;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to parse XML content: ${error}`);
      throw error;
    }
  }

  /**
   * Verifies the integrity of the model package by using SHA256.
   *
   * @param {string} modelPackagePath - The path to the model package.
   * @param {string} versionInfoPath - The path to the version info XML file.
   * @returns {Promise<boolean>} A promise that resolves to true if integrity check passes.
   */
  private async verifyModelIntegrity(modelPackagePath: string, versionInfoPath: string): Promise<boolean> {
    try {
      const correctHash = await this.parseXmlTagContent(versionInfoPath, "sha256");
      // compute current harsh value
      const actualHash = await hash.hash(modelPackagePath, "sha256");
      HiAiLog.info(TAG, `The correct hash is ${correctHash}`);
      HiAiLog.info(TAG, `The actual hash is ${actualHash}`);
      // compare hash value
      if (correctHash.toUpperCase() !== actualHash.toUpperCase()) {
        HiAiLog.error(TAG, 'SHA256 verification failed');
        return false;
      }
      HiAiLog.info(TAG, `SHA256 verification success`);
      return true;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to verify model integrity: ${error}`);
      return false;
    }
  }

  /**
   * Checks if required files exist in the extracted directory.
   *
   * @param {string} directory - The directory to check.
   * @param {{ xmlPattern: string, zipPattern: string }} filePattern - Patterns to match required files.
   * @param {string} errorMessage - Error message to throw if files are missing.
   * @returns {Promise<{ xmlFile: string, zipFile: string }>} A promise that resolves with the found file names.
   */
  private async checkExtractedFiles(
    directory: string,
    filePattern: { xmlPattern: string, zipPattern: string },
    errorMessage: string
  ): Promise<{ xmlFile: string, zipFile: string }> {
    const files = await fs.listFile(directory);
    if (!Array.isArray(files) || files.length === 0) {
      HiAiLog.error(TAG, `directory ${directory} is null or invalid`);
      throw new Error(`directory ${directory} is null or invalid`);
    }
    const xmlFile = files.find(file => file.endsWith(filePattern.xmlPattern));
    const zipFile = files.find(file => file.endsWith(filePattern.zipPattern));
    if (!xmlFile || !zipFile) {
      throw new Error(errorMessage);
    }
    return {
      xmlFile, zipFile
    };
  }

  /**
   * Validates and unzips a model file.
   *
   * @param {ModelBaseInfo} modelBaseInfo - The base information of the model.
   * @param {string} path - The path to the model file.
   * @param {IModelDownloadCallback} [modelCallbackProxy] - Optional callback for download progress.
   * @returns {Promise<void>} A promise that resolves when unzipping is complete.
   */
  public async unZipModel(modelBaseInfo: ModelBaseInfo, path: string,
    modelCallbackProxy?: IModelDownloadCallback): Promise<void> {
    const baseDir = path.substring(0, path.lastIndexOf('/'));
    const tempDir = `${baseDir}/temp`;
    const modelName = modelBaseInfo.getModelUpdateInfo().pluginInfos[0].pluginName;
    const modelDomain =
      ModelInfoManager.getDomain(modelName) || modelBaseInfo.getModelUpdateInfo().pluginInfos[0].packageName;
    const modelCompatibleVersion = ModelInfoManager.getModelCompatibleVersion(modelName) ||
    modelBaseInfo.getModelUpdateInfo().pluginInfos[0].compatibleVersion;
    const modelVersion = ModelInfoManager.getModelVersion(modelName) || modelBaseInfo.getModelVersion();
    const modelType =
      ModelInfoManager.getModelType(modelName) || modelBaseInfo.getModelUpdateInfo().pluginInfos[0].pluginType;
    const versionDir = `${baseDir}/${modelVersion}`;
    try {
      // check whether exist downloaded model
      let existingModel = await JsonUtil.getModel(modelDomain, modelName, modelCompatibleVersion);
      HiAiLog.info(TAG, `Local have existing model result is ${existingModel}`);
      if (existingModel) {
        const existingVersion = existingModel.versionInfos.find(v => v.version === modelVersion);
        if (existingVersion && existingVersion.sourcePath) {
          // Check if the model file actually exists
          try {
            if (!fs.accessSync(existingVersion.sourcePath)) {
              HiAiLog.error(TAG, `Model file does not exist at path: ${existingVersion.sourcePath}`);
              // Clean up database records for missing files
              await DataBaseOperate.getInstance().deleteModelData(modelDomain, modelName, modelCompatibleVersion, modelVersion);
              existingModel = null;
            } else {
              HiAiLog.info(TAG,
                `Model ${modelName} version ${modelVersion} already exists at ${existingVersion.sourcePath}`);
              modelCallbackProxy?.onComplete(modelName, modelVersion, existingVersion.sourcePath);
              ModelDownloadUIExtentionAbility.hasTriggeredCallback = true;
              // delete update package
              await fs.unlink(path);
              existingModel = null;
              return;
            }
          } catch (error) {
            HiAiLog.error(TAG, `Failed to check model file existence: ${error}`);
            // Clean up database records for missing files
            await DataBaseOperate.getInstance().deleteModelData(modelDomain, modelName, modelCompatibleVersion, modelVersion);
            existingModel = null;
          }
        } else {
          existingModel = null;
        }
      }
      if (!await this.checkStorageSpace(modelBaseInfo, path)) {
        modelCallbackProxy?.onError(modelName, ModelDownloadResultCode.OUT_OF_MEMORY,
          ReportResultCodeMessage.SPACE_INSUFFICIENT);
        return;
      }
      // make directory
      await fs.mkdir(tempDir);
      await fs.mkdir(versionDir);
      // unzip full.zip
      await zlib.decompressFile(path, tempDir);
      // delete full.zip
      let extraction;
      try {
        extraction = await this.checkExtractedFiles(tempDir,
          {
            xmlPattern: 'versionInfo.xml', zipPattern: '.zip'
          },
          "The model zip package or versionInfo.xml file is missing"
        );
      } catch (error) {
        let baseInfo: BaseInfo =
          ModelDownloadReportUtil.setBaseLogInfo(modelName, 'Integrity check failed', modelDomain);
        ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.INTEGRITY_CHECK_FAILED,
          ReportResultCodeMessage.CHECK_ZIP_FAILED, ModelDownloadCapability.foreDownloadStartTime.getTime());
        ModelDownloadReportUtil.setReportInfoByBundleName(baseInfo);
        modelCallbackProxy?.onError(modelName, ModelDownloadResultCode.UNZIP_FAILED,
          `${ReportResultCodeMessage.CHECK_ZIP_FAILED}: ${error.message}`);
        await this.cleanUp(tempDir, versionDir, path);
        return;
      }
      // validate the integrity of the model
      const modelPackagePath = `${tempDir}/${extraction.zipFile}`;
      const versionInfoPath = `${tempDir}/${extraction.xmlFile}`;
      // compare hash value,if damaged clean up
      if (!await this.verifyModelIntegrity(modelPackagePath, versionInfoPath)) {
        modelCallbackProxy?.onError(modelName, ModelDownloadResultCode.INTEGRITY_CHECK_FAILED,
          ReportResultCodeMessage.MODEL_HAS_BEEN_DAMAGED);
        let baseInfo: BaseInfo =
          ModelDownloadReportUtil.setBaseLogInfo(modelName, 'Integrity check failed', modelDomain);
        ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.INTEGRITY_CHECK_FAILED,
          ReportResultCodeMessage.CHECK_ZIP_FAILED, ModelDownloadCapability.foreDownloadStartTime.getTime());
        ModelDownloadReportUtil.setReportInfoByBundleName(baseInfo);
        await this.cleanUp(tempDir, versionDir, path);
        return;
      }
      // unzip model package to version directory
      await zlib.decompressFile(modelPackagePath, versionDir);
      await fs.moveFile(versionInfoPath, `${versionDir}/versionInfo.xml`, 0);
      // remove temp directory and model package
      await fs.rmdir(tempDir);
      await fs.unlink(path);
      HiAiLog.info(TAG, `Start to store modelInfo ${modelName}`);
      try {
        await DataBaseOperate.getInstance().addToDownloadedList(modelBaseInfo, versionDir);
      } catch (error) {
        HiAiLog.error(TAG, `Failed to save model information to JSON: ${error}`);
      }

      modelCallbackProxy?.onComplete(modelName, modelVersion, versionDir);

      const modelDownloadInfo = new ModelDownloadInfo();
      modelDownloadInfo.domain = modelDomain;
      modelDownloadInfo.resId = modelName;
      modelDownloadInfo.resCompatibleVersion = modelCompatibleVersion;
      modelDownloadInfo.resVersion = modelVersion;
      HiAiLog.info(TAG,
        `model download subscribe model info is ${JSON.stringify(modelDownloadInfo)},modelType is ${modelType}`);
      ModelTaskManager.getInstance().modelSubscribe(modelType, [modelDownloadInfo]);
      // handel aging
      await ModelAgingManager.getInstance().handleModelAging(modelDomain, modelName, modelCompatibleVersion, false);
      await ModelAgingManager.getInstance()
        .checkAndCleanExpiredModels(modelDomain, modelName, modelCompatibleVersion);
      HiAIServiceAbility.globalContext.area = contextConstant.AreaMode.EL2;
      return;
    } catch (error) {
      HiAiLog.error(TAG, `Failed to unzip model :${error}`);
      modelCallbackProxy?.onError(modelName, ModelDownloadResultCode.UNZIP_FAILED,
        ReportResultCodeMessage.DEPRESS_FAILED);
      let baseInfo: BaseInfo =
        ModelDownloadReportUtil.setBaseLogInfo(modelName, 'Unzip model failed', modelDomain);
      ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.UNZIP_FAILED,
        ReportResultCodeMessage.DEPRESS_FAILED, ModelDownloadCapability.foreDownloadStartTime.getTime());
      ModelDownloadReportUtil.setReportInfoByBundleName(baseInfo);
      await this.cleanUp(tempDir, versionDir, path);
      DataBaseOperate.getInstance().deleteModelData(modelDomain, modelName, modelCompatibleVersion, modelVersion);
      return;
    }
  }

  /**
   * Cleans up model files and directories.
   *
   * @param {string} tempDir - The temporary directory to clean.
   * @param {string} versionDir - The version directory to clean.
   * @param {string} path - The path to the model file to clean.
   * @returns {Promise<void>} A promise that resolves when cleanup is complete.
   */
  public async cleanUp(tempDir: string, versionDir: string, path: string): Promise<void> {
    await fs.rmdir(tempDir);
    await fs.rmdir(versionDir);
    await fs.rmdir(path);
  }
}