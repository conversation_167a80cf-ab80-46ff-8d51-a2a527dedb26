/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import DataBaseOperate from './DataBaseOperate';
import ModelBaseInfo from '../modelDownloadOta/modelDownloadInfo/ModelBaseInfo'
import ModelOperate from './ModelOperate'
import fs from '@ohos.file.fs';
import { ModelDownloadResultCode } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import JsonUtil, { UsageStatus } from '../utils/JsonUtil';
import QueryResultInfo from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/modelQuery/ModelQueryResultInfo';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import { IModelManagerCallback } from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/IModelManagerCallback';
import ResultInfo from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/ModelManagerResultInfo';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { IModelQueryCallback } from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/modelQuery/IModelQueryCallback';
import { JSON } from '@kit.ArkTS';


const TAG: string = "ModelAgingManager";

const THIRTY_DAYS_MS = 30 * 24 * 60 * 60 * 1000;
const SEVEN_DAYS_MS = 7 * 24 * 60 * 60 * 1000;
const ONE_DAY_MS = 24 * 60 * 60 * 1000;

// This class offers basic model manager functions,such as add、query、delete
export default class ModelAgingManager {
  private static instance: ModelAgingManager;

  /**
   * Gets the singleton instance of ModelAgingManager.
   *
   * @returns {ModelAgingManager} The singleton instance of ModelAgingManager.
   */
  public static getInstance(): ModelAgingManager {
    if (!ModelAgingManager.instance) {
      ModelAgingManager.instance = new ModelAgingManager();
    }
    return ModelAgingManager.instance;
  }

  /**
   * Adds a model to local sandbox.
   *
   * @param {ModelBaseInfo} modelBaseInfo - The base information of the model.
   * @param {string} path - The path where the model package is stored.
   * @param {IModelDownloadCallback} [downloadCallback] - Optional callback for download progress.
   * @returns {Promise<void>} A promise that resolves when the model is added.
   */
  public async addLocalModel(modelBaseInfo: ModelBaseInfo, path: string,
    downloadCallback?: IModelDownloadCallback): Promise<void> {
    if (!modelBaseInfo || !path) {
      HiAiLog.info(TAG, `add local model parameter is wrong`);
      downloadCallback?.onError(modelBaseInfo.getModelID(), ModelDownloadResultCode.PARAMATER_INVALID,
        "Add local model parameter is wrong.")
      return;
    }
    JsonUtil.initJsonFile();
    ModelOperate.getInstance().unZipModel(modelBaseInfo, path, downloadCallback);
    return;
  }

  /**
   * Queries the latest model information and handles model aging.
   *
   * @param {Array<ModelDownloadInfo>} queryInfos - Array of model information to query.
   * @param {IModelQueryCallback} queryCallback - Callback to handle query results.
   */
  public queryLatestModel(queryInfos: Array<ModelDownloadInfo>, queryCallback: IModelQueryCallback): void {
    HiAiLog.info(TAG, `Query latest model start`);
    if (!queryInfos?.length) {
      queryCallback.onResult(true, "No models to query", []);
      return;
    }
    (async (): Promise<void> => {
      try {
        const queryResults: Array<QueryResultInfo> = [];
        let hasError = false;
        for (const queryInfo of queryInfos) {
          const result = await DataBaseOperate.getInstance().queryLatestModelInfo(queryInfo);
          queryResults.push(result);
          if (result.errorCode !== 0) {
            hasError = true;
          }
          HiAiLog.info(TAG, `queryLatestModel queryResults is ${JSON.stringify(queryResults)}`);
        }
        queryCallback.onResult(
          !hasError,
          hasError ? "Some models failed to query" : "All models queried successfully",
          queryResults
        );
      } catch (error) {
        HiAiLog.error(TAG, `Failed to query models: ${error}`);
        queryCallback.onResult(
          false,
          `Query operation failed: ${error.message}`,
          []
        );
      }
    })();
  }

  /**
   * Queries all versions of a model.
   *
   * @param {ModelDownloadInfo} queryInfo - The model information to query.
   * @returns {Promise<QueryResultInfo[]>} A promise that resolves with an array of query results.
   */
  public async queryFullModel(queryInfo: ModelDownloadInfo): Promise<QueryResultInfo[]> {
    if (!queryInfo?.domain || !queryInfo?.resId || !queryInfo?.resCompatibleVersion) {
      return [];
    }
    try {
      return await DataBaseOperate.getInstance().queryFullModelInfo(queryInfo);
    } catch (error) {
      HiAiLog.error(TAG, `Failed to query full model info: ${error}`);
      return [];
    }
  }

  /**
   * Creates a failure result object for model operations.
   *
   * @param {ModelDownloadInfo} modelInfo - The model information.
   * @param {string} errorMessage - The error message to include in the result.
   * @returns {ResultInfo} The created failure result object.
   */
  private createFailureResult(modelInfo: ModelDownloadInfo, resultCode: ModelDownloadResultCode,
    errorMessage: string): ResultInfo {
    const resultInfo = new ResultInfo();
    resultInfo.resId = modelInfo.resId;
    resultInfo.domain = modelInfo.domain;
    resultInfo.resCompatibleVersion = modelInfo.resCompatibleVersion;
    resultInfo.resVersion = modelInfo.resVersion;
    resultInfo.errorCode = resultCode;
    resultInfo.errorMessage = errorMessage;
    return resultInfo;
  }

  /**
   * Deletes a single model.
   *
   * @param {ModelDownloadInfo} modelInfo - The model information to delete.
   * @returns {Promise<boolean>} A promise that resolves to true if deletion was successful.
   */
  private async deleteSingleModel(modelInfo: ModelDownloadInfo): Promise<boolean> {
    if (!modelInfo) {
      HiAiLog.info(TAG, `Delete modelInfo parameter is null `);
    }
    const dataBaseOperate = DataBaseOperate.getInstance();
    const sourcePath = dataBaseOperate.getSourcePath(
      modelInfo.domain,
      modelInfo.resId,
      modelInfo.resCompatibleVersion,
      modelInfo.resVersion
    );
    if (sourcePath) {
      try {
        await fs.rmdir(sourcePath);
      } catch (error) {
        HiAiLog.error(TAG, `Failed to delete model file: ${error}`);
        return false;
      }
    }
    const success = await dataBaseOperate.deleteModelData(
      modelInfo.domain,
      modelInfo.resId,
      modelInfo.resCompatibleVersion,
      modelInfo.resVersion
    );
    if (!success) {
      HiAiLog.error(TAG, `Failed to delete model data from database`);
      return false;
    }
    HiAiLog.info(TAG, `Successfully deleted model ${modelInfo.resId}`);
    return true;
  }

  /**
   * Deletes specified models.
   *
   * @param {Array<ModelDownloadInfo>} modelInfos - Array of model information to delete.
   * @param {IModelManagerCallback} deleteCallback - Callback to handle deletion results.
   * @returns {Promise<void>} A promise that resolves when deletion is complete.
   */
  public async deleteModel(modelInfos: Array<ModelDownloadInfo>, deleteCallback: IModelManagerCallback): Promise<void> {
    HiAiLog.info(TAG, `deleteModel start`);
    if (!modelInfos?.length) {
      deleteCallback.onResult(true, "No models to delete", null);
      return;
    }
    let resultList: Array<ResultInfo> = [];
    for (const modelInfo of modelInfos) {
      if (!modelInfo?.domain || !modelInfo?.resId || !modelInfo?.resCompatibleVersion) {
        resultList.push(this.createFailureResult(
          modelInfo, ModelDownloadResultCode.PARAMATER_INVALID, 'Invalid model parameters: Required fields are missing'
        ));
        continue;
      }
      let sourcePath = DataBaseOperate.getInstance()
        .getSourcePath(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion, modelInfo.resVersion);
      try {
        if (!sourcePath) {
          resultList.push(this.createFailureResult(modelInfo, ModelDownloadResultCode.DELETE_LOCAL_FAILED,
            'Model path not found'));
          continue;
        }
        await fs.rmdir(sourcePath);
        const dbResult = DataBaseOperate.getInstance()
          .deleteModelData(modelInfo.domain, modelInfo.resId, modelInfo.resCompatibleVersion, modelInfo.resVersion);
        if (!dbResult) {
          resultList.push(this.createFailureResult(modelInfo, ModelDownloadResultCode.DELETE_LOCAL_FAILED,
            'Failed to delete model from database'));
        }
      } catch (error) {
        resultList.push(this.createFailureResult(modelInfo, ModelDownloadResultCode.DELETE_LOCAL_FAILED,
          `Failed to delete model files: ${error.message}`));
      }
    }
    if (resultList.length === 0) {
      deleteCallback.onResult(true, 'All models deleted successfully', null);
    } else {
      deleteCallback.onResult(false, 'Some models failed to delete', resultList);
    }
  }


  /**
   * Checks and cleans expired model versions.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} compatibleVersion - The compatible version of the model.
   * @returns {Promise<void>} A promise that resolves when cleanup is complete.
   */
  public async checkAndCleanExpiredModels(domain: string, modelName: string, compatibleVersion: string): Promise<void> {
    try {
      const currentTime = Date.now();
      const storedModel = JsonUtil.getModel(domain, modelName, compatibleVersion);
      if (!storedModel) {
        HiAiLog.info(TAG, `Model ${modelName} not found, skipping cleanup`);
        return;
      }
      if (storedModel.lastQueryTime && (currentTime - storedModel.lastQueryTime) < ONE_DAY_MS) {
        HiAiLog.info(TAG, `Last cleanup was less than 24 hours ago for model ${modelName}, skipping`);
        return;
      }
      const expiredVersions = storedModel.versionInfos
        .filter(version =>
        version.deleteTime &&
          version.deleteTime <= currentTime &&
          version.status !== UsageStatus.BEING_USED
        )
        .map(version => {
          const modelInfo = new ModelDownloadInfo();
          modelInfo.domain = domain;
          modelInfo.resId = modelName;
          modelInfo.resCompatibleVersion = compatibleVersion;
          modelInfo.resVersion = version.version;
          return modelInfo;
        });
      if (expiredVersions.length === 0) {
        HiAiLog.info(TAG, `No expired versions found for model ${modelName}`);
        return;
      }
      await Promise.all(
        expiredVersions.map(async modelInfo => {
          try {
            await this.deleteSingleModel(modelInfo);
          } catch (error) {
            HiAiLog.error(TAG, `Failed to delete model ${modelInfo.resId}: ${error}`);
          }
        })
      );
    } catch (error) {
      HiAiLog.error(TAG, `Failed to check and clean expired models: ${error}`);
    }
  }

  /**
   * Cleans up old compatible versions of a model.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} currentCompatibleVersion - The current compatible version to keep.
   * @returns {Promise<void>} A promise that resolves when cleanup is complete.
   */
  private async cleanOldCompatibleVersions(domain: string, modelName: string,
    currentCompatibleVersion: string): Promise<void> {
    const baseDir = `/data/storage/el2/base/haps/support/files/${domain}/${modelName}`;
    HiAiLog.info(TAG, `Start clean old compatible versions`);
    try {
      const compatibleDirs = await fs.listFile(baseDir);
      for (const dir of compatibleDirs) {
        if (dir !== currentCompatibleVersion) {
          const oldVersionPath = `${baseDir}/${dir}`;
          try {
            await fs.rmdir(oldVersionPath);
            await DataBaseOperate.getInstance().deleteModelData(domain, modelName);
            HiAiLog.info(TAG, `Successfully deleted old compatible version`);
          } catch (error) {
            HiAiLog.error(TAG, `Failed to delete old compatible version ${dir}: ${error}`);
          }
        }
      }
    } catch (error) {
      HiAiLog.error(TAG, `Failed to list compatible version directories: ${error}`);
    }
  }

  /**
   * Handles model aging by managing old versions and updating model status.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} compatibleVersion - The compatible version of the model.
   * @returns {Promise<void>} A promise that resolves when aging process is complete.
   */
  public async handleModelAging(domain: string, modelName: string, compatibleVersion: string,
    isQuery: boolean): Promise<void> {
    try {
      this.cleanOldCompatibleVersions(domain, modelName, compatibleVersion);
      HiAiLog.info(TAG, `Start find aging model`);
      const storedModel = await JsonUtil.getModel(domain, modelName, compatibleVersion);
      HiAiLog.info(TAG, `Target aging model is ${JSON.stringify(storedModel)}`);
      if (!storedModel || !storedModel.versionInfos || storedModel.versionInfos.length === 0) {
        HiAiLog.info(TAG, `Can't find target aging model or no versions available: ${modelName}`);
        return;
      }
      const lastQueryVersion = storedModel.lastQueryVersion;
      const lastQueryTime = storedModel.lastQueryTime;
      const currentTime = Date.now();

      if (lastQueryTime && (currentTime - lastQueryTime) < ONE_DAY_MS) {
        HiAiLog.info(TAG, `Last cleanup was less than 24 hours ago for model ${modelName}, skipping`);
        return;
      }

      const sortedVersions = storedModel.versionInfos.sort((a, b) =>
      DataBaseOperate.getInstance().versionToNumber(b.version) -
      DataBaseOperate.getInstance().versionToNumber(a.version));

      if (sortedVersions.length === 0) {
        HiAiLog.info(TAG, `No versions available after sorting for model: ${modelName}`);
        return;
      }

      const lastQueryVersionInfo = sortedVersions.find(v => v.version === lastQueryVersion);
      const latestVersion = sortedVersions[0].version;

      let isOverThree: boolean = false;
      if (sortedVersions.length > 2) {
        isOverThree = true;
        HiAiLog.info(TAG, `Found ${sortedVersions.length} versions, cleaning up old versions`);
        let versionsToDelete = sortedVersions.filter(version =>
        version.status !== UsageStatus.BEING_USED &&
          version.version !== latestVersion
        );
        HiAiLog.info(TAG, `Model num >3 versionsToDelete ${JSON.stringify(versionsToDelete)}`);
        for (const versionInfo of versionsToDelete) {
          const modelInfo = new ModelDownloadInfo();
          modelInfo.domain = domain;
          modelInfo.resId = modelName;
          modelInfo.resCompatibleVersion = compatibleVersion;
          modelInfo.resVersion = versionInfo.version;
          HiAiLog.info(TAG, `versionsToDelete modelInfo is ${modelInfo.resVersion}`);
          try {
            await this.deleteSingleModel(modelInfo);
            HiAiLog.info(TAG, `Successfully deleted old version ${versionInfo.version}`);
          } catch (error) {
            HiAiLog.error(TAG, `Failed to delete version ${versionInfo.version}: ${error}`);
          }
        }

        let versionToUpdate =
          sortedVersions.filter(version => version.status === UsageStatus.BEING_USED);

        if (versionToUpdate.length > 0) {
          let versionInUse = versionToUpdate[0];
          HiAiLog.info(TAG, `versionInUse model is ${versionInUse.version}`);
          await DataBaseOperate.getInstance().updateModelStatusAndDeleteTime(
            domain, modelName, compatibleVersion, versionInUse.version, UsageStatus.OUTDATED,
            currentTime + THIRTY_DAYS_MS);
        }
      }

      HiAiLog.info(TAG, `lastQueryVersionInfo is ${lastQueryVersionInfo?.version},latestVerison is ${latestVersion}`);
      if (lastQueryVersionInfo && lastQueryVersionInfo.version !== latestVersion && !isOverThree && isQuery) {
        let deleteTime = lastQueryVersionInfo.deleteTime === 0 ? currentTime + SEVEN_DAYS_MS : currentTime +
          (lastQueryVersionInfo.deleteTime > currentTime + SEVEN_DAYS_MS ? SEVEN_DAYS_MS :
            lastQueryVersionInfo.deleteTime - currentTime);
        HiAiLog.info(TAG, `The last query version delete time is ${deleteTime}`);
        await DataBaseOperate.getInstance().updateModelStatusAndDeleteTime(
          domain, modelName, compatibleVersion, lastQueryVersionInfo.version, UsageStatus.OUTDATED, deleteTime);
      }

      if (latestVersion && sortedVersions.length === 2 && sortedVersions[1].status === UsageStatus.UNUSED) {
        await DataBaseOperate.getInstance().updateModelStatusAndDeleteTime(
          domain, modelName, compatibleVersion, sortedVersions[1].version, UsageStatus.OUTDATED,
          currentTime + SEVEN_DAYS_MS);
      }
    } catch (e) {
      HiAiLog.error(TAG, `Failed to handle model aging: ${e}`);
    }
  }
}
