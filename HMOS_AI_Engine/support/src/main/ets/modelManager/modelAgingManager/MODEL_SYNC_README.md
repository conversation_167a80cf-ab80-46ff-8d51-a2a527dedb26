# 模型文件与JSON数据同步功能

## 问题描述

在查询本地最新模型时会出现模型文件和数据不一致的情况：

1. **JSON文件损毁**：存储模型下载数据的JSON文件损毁，但本地有模型文件
2. **JSON内容为空**：JSON文件没有损毁但里面的内容为空，本地模型文件存在

## 解决方案

在`DataBaseOperate.ts`中添加了自动检查和同步功能：

### 新增方法

#### 1. `constructModelFilePath()`
- **功能**：根据模型信息构建预期的模型文件路径
- **参数**：
  - `domain`: 模型域
  - `modelName`: 模型名称
  - `resCompatibleVersion`: 兼容版本
  - `version`: 具体版本
  - `isEl1`: 是否使用EL1级别路径
- **返回**：构建的模型文件路径

#### 2. `scanAndSyncLocalModelFiles()`
- **功能**：扫描本地模型文件并同步到JSON存储
- **参数**：
  - `domain`: 模型域
  - `modelName`: 模型名称  
  - `resCompatibleVersion`: 兼容版本
  - `isEl1`: 是否扫描EL1级别文件
- **返回**：同步后的模型信息或null
- **工作流程**：
  1. 检查模型目录是否存在
  2. 列出目录中的所有.zip文件
  3. 为每个文件创建版本信息
  4. 将模型信息添加到JSON存储

### 修改的方法

#### 1. `queryLatestModelInfo()`
- **修改位置**：在JSON中找不到模型时
- **新增逻辑**：
  1. 调用`scanAndSyncLocalModelFiles()`扫描本地文件
  2. 如果找到文件，同步到JSON并继续查询流程
  3. 如果没有找到文件，返回查询失败

#### 2. `queryFullModelInfo()`
- **修改位置**：在JSON中找不到模型时
- **新增逻辑**：与`queryLatestModelInfo()`相同

## 使用场景

### 场景1：JSON文件损毁
```
1. 用户查询模型 -> JSON中找不到记录
2. 系统自动扫描本地文件目录
3. 发现模型文件存在 -> 重建JSON记录
4. 返回查询成功结果
```

### 场景2：JSON内容为空
```
1. 用户查询模型 -> JSON为空或找不到记录
2. 系统自动扫描本地文件目录
3. 发现模型文件存在 -> 添加JSON记录
4. 返回查询成功结果
```

### 场景3：文件和JSON都不存在
```
1. 用户查询模型 -> JSON中找不到记录
2. 系统自动扫描本地文件目录
3. 没有发现模型文件 -> 返回查询失败
```

## 文件路径结构

### EL2级别（默认）
```
/data/storage/el2/base/haps/support/files/{domain}/{modelName}/{compatibleVersion}/{version}.zip
```

### EL1级别（TTS模型）
```
/data/storage/el1/base/haps/support/files/{domain}/{modelName}/{compatibleVersion}/{version}.zip
```

## 日志输出

系统会输出详细的日志信息：
- 扫描目录路径
- 发现的模型文件数量和列表
- 同步操作结果
- 错误信息（如果有）

## 测试

可以使用`DataBaseOperateTest.ts`进行功能测试：

```typescript
// 运行所有测试
await DataBaseOperateTest.runAllTests();

// 单独测试模型文件同步
await DataBaseOperateTest.testModelFileSync();

// 单独测试完整模型查询
await DataBaseOperateTest.testFullModelQuery();
```

## 注意事项

1. **性能影响**：文件扫描操作只在JSON中找不到模型时执行，不会影响正常查询性能
2. **文件权限**：确保应用有读取模型文件目录的权限
3. **并发安全**：多个查询同时进行时，可能会重复扫描同一目录
4. **存储级别**：自动识别TTS模型使用EL1级别，其他模型使用EL2级别

## 兼容性

- 向后兼容：不影响现有的正常查询流程
- 自动恢复：在检测到不一致时自动修复
- 透明操作：对上层调用者透明，无需修改现有代码
