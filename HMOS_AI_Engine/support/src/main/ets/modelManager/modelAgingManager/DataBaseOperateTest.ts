/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import DataBaseOperate from './DataBaseOperate';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import { JSON } from '@kit.ArkTS';

const TAG: string = "DataBaseOperateTest";

/**
 * Test class for DataBaseOperate functionality
 */
export default class DataBaseOperateTest {
  
  /**
   * Tests the model file synchronization functionality
   */
  public static async testModelFileSync(): Promise<void> {
    try {
      HiAiLog.info(TAG, "Starting model file synchronization test");
      
      // Create a test model download info
      const testModelInfo = new ModelDownloadInfo();
      testModelInfo.domain = "com.huawei.hiai.test";
      testModelInfo.resId = "testModel";
      testModelInfo.resCompatibleVersion = "1.0.0";
      testModelInfo.resVersion = "1.0.1";
      
      HiAiLog.info(TAG, `Testing with model info: ${JSON.stringify({
        domain: testModelInfo.domain,
        resId: testModelInfo.resId,
        resCompatibleVersion: testModelInfo.resCompatibleVersion,
        resVersion: testModelInfo.resVersion
      })}`);
      
      // Test the query functionality
      const result = await DataBaseOperate.getInstance().queryLatestModelInfo(testModelInfo);
      
      HiAiLog.info(TAG, `Query result: ${JSON.stringify({
        errorCode: result.errorCode,
        errorMessage: result.errorMessage,
        sourcePath: result.sourcePath,
        resVersion: result.resVersion
      })}`);
      
      if (result.errorCode === 0) {
        HiAiLog.info(TAG, "Model file synchronization test PASSED - Model found or synchronized");
      } else {
        HiAiLog.info(TAG, "Model file synchronization test completed - No model files found (expected for test)");
      }
      
    } catch (error) {
      HiAiLog.error(TAG, `Model file synchronization test FAILED: ${error}`);
    }
  }
  
  /**
   * Tests the full model query functionality
   */
  public static async testFullModelQuery(): Promise<void> {
    try {
      HiAiLog.info(TAG, "Starting full model query test");
      
      // Create a test model download info
      const testModelInfo = new ModelDownloadInfo();
      testModelInfo.domain = "com.huawei.hiai.test";
      testModelInfo.resId = "testModel";
      testModelInfo.resCompatibleVersion = "1.0.0";
      testModelInfo.resVersion = "1.0.1";
      
      // Test the full query functionality
      const results = await DataBaseOperate.getInstance().queryFullModelInfo(testModelInfo);
      
      HiAiLog.info(TAG, `Full query results count: ${results.length}`);
      
      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        HiAiLog.info(TAG, `Result ${i}: ${JSON.stringify({
          errorCode: result.errorCode,
          errorMessage: result.errorMessage,
          sourcePath: result.sourcePath,
          resVersion: result.resVersion
        })}`);
      }
      
      HiAiLog.info(TAG, "Full model query test completed");
      
    } catch (error) {
      HiAiLog.error(TAG, `Full model query test FAILED: ${error}`);
    }
  }
  
  /**
   * Runs all tests
   */
  public static async runAllTests(): Promise<void> {
    HiAiLog.info(TAG, "=== Starting DataBaseOperate Tests ===");
    
    await this.testModelFileSync();
    await this.testFullModelQuery();
    
    HiAiLog.info(TAG, "=== DataBaseOperate Tests Completed ===");
  }
}
