/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { ScreenChangeUtil } from './utils/ScreenChangeUtil';
import { NetworkChangeUtil } from './utils/NetworkChangeUtil';
import DownloadWindowManager from './modelDownloadOta/modelDownloadPages/DownloadWindowManager';
import ModelTaskManager from './modelDownloadOta/ModelTaskManager';
import ModelBaseInfo from './modelDownloadOta/modelDownloadInfo/ModelBaseInfo';
import IHiAICapability from '../framework/IHiAICapability';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { ModelDomain, ModelDownloadType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import DownloadConfig from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConfig';
import ModelDownloadCallbackStub from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadCallbackStub';
import { rpc } from '@kit.IPCKit';
import WindowConstants from './utils/constants/WindowConstants';
import { ModelDownloadResultCode, ReportResultCode,
    ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { IModelManagerCallback } from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/IModelManagerCallback';
import ModelManagerCallbackStub from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/ModelManagerCallbackStub';
import { IModelQueryCallback } from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/modelQuery/IModelQueryCallback';
import ModelQueryCallbackStub from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/modelQuery/ModelQueryCallbackStub';
import { ModelDownloadReportUtil } from './utils/ModelManagerReportUtil';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { abilityManager, Want } from '@kit.AbilityKit';
import HiAIServiceAbility from '../framework/HiAIServiceAbility';
import { BusinessError} from '@kit.BasicServicesKit';
import { JSON } from '@kit.ArkTS';
import { ElementName } from '@hms-ai/pdkfull/src/main/ets/interfaces/abililtyBridge';
import ModelDownloadConfig from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConfig';

const TAG: string = "ModelDownloadCapability";

/**
 * Indicates the ModelDownload Capability
 * @class
 * <AUTHOR>
 */
export default class ModelDownloadCapability implements IHiAICapability {
    /**
     * Model operation type, including download, update, query, and deletion.
     */
    public static modelDownloadType: ModelDownloadType = null;
    /**
     * Used during foreground download. Only one copy is stored before user authorization. After user authorization, it is not used.
     */
    public static foreModelBaseInfo: ModelBaseInfo = null;
    /**
     * Used during foreground download. Only one copy is stored before user authorization. After user authorization, it is not used.
     */
    public static foreModelDownloadInfo: ModelDownloadInfo = null;
    /**
     * Used during foreground download. Only one copy is stored before user authorization. After user authorization, it is not used.
     */
    public static foreDownloadCallbackProxy: IModelDownloadCallback = null;
    /**
     * Used during foreground download. Only one copy is stored before user authorization. After user authorization, it is not used.
     */
    public static backgroundModelBaseInfo: ModelBaseInfo = null;
    /**
     * Used during foreground download. Only one copy is stored before user authorization. After user authorization, it is not used.
     */
    public static backgroundModelDownloadInfo: ModelDownloadInfo = null;
    /**
     * Used during foreground download. Only one copy is stored before user authorization. After user authorization, it is not used.
     */
    public static backgroundDownloadCallbackProxy: IModelDownloadCallback = null;
    /**
     * Indicates model download config;
     */
    public static modelDownloadConfig: ModelDownloadConfig = null;
    /**
     * Indicates that a model is being downloaded from the foreground.
     */
    public static hasForeDownload: boolean = false;
    /**
     * Indicates that a model is being downloaded from the foreground.
     */
    public static foreDownloadStartTime: Date = null;
    /**
     * Indicates that a model is being downloaded from the foreground.
     */
    public static foreReportBaseInfo: BaseInfo = null;

    /**
     * Indicates that a model is being downloaded from the update;
     */
    public static upgradeReportBaseInfo: BaseInfo = null;

    /**
     * Indicates that the device type for the current model download;
     */
    public static deviceType: string = null;

    /**
     * Used during background download. Only one copy is stored before user authorization. After user authorization, it is not used.
     */
    public static backDownloadCallbackProxy: IModelDownloadCallback = null;

    /**
     * @override
     */
    async onRemoteRequestHandle(data: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): Promise< boolean> {
        ModelDownloadCapability.foreDownloadStartTime = new Date();

        ModelDownloadCapability.modelDownloadType = data.readInt();
        HiAiLog.info(TAG, "onRemoteRequestHandle ModelDownloadCapability: "  + ModelDownloadCapability.modelDownloadType);
        let handleResult: boolean = false;
        if( ModelDownloadCapability.modelDownloadType <= 2) {
            handleResult = await this.modelDownloadTaskHandle(data);
        } else {
            handleResult = await this.modelManagerTaskHandle(data);
        }
        return handleResult;
    }


    private async modelDownloadTaskHandle(data: rpc.MessageSequence): Promise< boolean> {
        HiAiLog.info(TAG, "onRemoteRequestHandle modelDownloadTaskHandle" );
        let modelDownloadInfo: ModelDownloadInfo = new ModelDownloadInfo();
        data.readParcelable(modelDownloadInfo);
        HiAiLog.info(TAG, "onRemoteRequestHandle modelDownloadTaskHandle modelDownloadInfo  " + JSON.stringify(modelDownloadInfo));
        let downloadCallback: rpc.IRemoteObject;
        try {
            downloadCallback= data.readRemoteObject();
        }
        catch (e) {
            HiAiLog.error(TAG,`Failec to read Remote Object error ${e.message}`)
        }
        let downloadCallbackStub: IModelDownloadCallback = ModelDownloadCallbackStub.asInterface(downloadCallback);
        NetworkChangeUtil.getInstance().registerNetworkChange(); // Starting Network Listening

        let modelBaseInfo: ModelBaseInfo = new ModelBaseInfo(modelDownloadInfo.resId, modelDownloadInfo.domain,
            ModelDownloadCapability.modelDownloadType, ModelDomain.MODEL_TYPE_AI, modelDownloadInfo.resCompatibleVersion);

        switch(ModelDownloadCapability.modelDownloadType) {
            case ModelDownloadType.MODEL_FORE_DOWNLOAD: { // Foreground download, which is invoked only by AI models.
                ModelDownloadCapability.hasForeDownload = true;
                HiAiLog.info(TAG, "ForegroundDownload");
                DownloadWindowManager.foreModuleName = data.readString();
                DownloadWindowManager.foreModuleType = data.readInt();
                DownloadWindowManager.windowName = modelDownloadInfo.resId;
                ModelDownloadCapability.foreModelBaseInfo = modelBaseInfo;
                ModelDownloadCapability.foreModelDownloadInfo = modelDownloadInfo;
                ModelDownloadCapability.foreDownloadCallbackProxy = downloadCallbackStub;

                HiAiLog.info(TAG,`IPC message success foreModuleName :${DownloadWindowManager.foreModuleName} foreModuleType:${DownloadWindowManager.foreModuleType}`);
                HiAiLog.info(TAG,`ModelDownloadCapability :${JSON.stringify(ModelDownloadCapability.foreModelBaseInfo)} ${ModelDownloadCapability.foreModelDownloadInfo}`);

                let deviceType: string = data.readString();
                HiAiLog.info(TAG,`Download device type is ${deviceType}`)
                ModelDownloadCapability.deviceType = deviceType;
                let downloadConfig: DownloadConfig = new DownloadConfig();
                try {
                    data.readParcelable(downloadConfig);
                    HiAiLog.info(TAG,`Try read downloadConfig,downloadConfig is ${downloadConfig.networkType}`);
                }catch (e) {
                    HiAiLog.error(TAG,`Failed to read downloadConfig e message: ${e.message}`)
                }
                if(downloadConfig){
                    ModelDownloadCapability.modelDownloadConfig = downloadConfig;
                    HiAiLog.info(TAG,`downloadConfig network type is ${downloadConfig.networkType}`)
                    modelBaseInfo = new ModelBaseInfo(modelDownloadInfo.resId, modelDownloadInfo.domain,
                        ModelDownloadCapability.modelDownloadType, ModelDomain.MODEL_TYPE_AI, modelDownloadInfo.resCompatibleVersion,downloadConfig.networkType);
                    ModelDownloadCapability.foreModelBaseInfo = modelBaseInfo;
                }else {
                    let defaultDownloadConfig: ModelDownloadConfig = new ModelDownloadConfig();
                    defaultDownloadConfig.networkType = 0;
                    ModelDownloadCapability.modelDownloadConfig = defaultDownloadConfig;
                    HiAiLog.info(TAG, `defaultDownloadConfig network type is ${downloadConfig.networkType}`)
                    modelBaseInfo = new ModelBaseInfo(modelDownloadInfo.resId, modelDownloadInfo.domain,
                        ModelDownloadCapability.modelDownloadType, ModelDomain.MODEL_TYPE_AI,
                        modelDownloadInfo.resCompatibleVersion, downloadConfig.networkType);
                    ModelDownloadCapability.foreModelBaseInfo = modelBaseInfo;
                }
                HiAiLog.info(TAG,`foreground modelBaseInfo is ${JSON.stringify(modelBaseInfo)}`)
                let topAbilityName: ElementName = await this.getTopAbilityElementName();
                let topBundleName: string = this.getBundleNameFromTopElementName(topAbilityName);
                HiAiLog.info(TAG, `Top bundle name is ${topBundleName}`)

                let want: Want = {
                    bundleName: "com.huawei.hmsapp.hiai",
                    abilityName: "ModelDownloadUIExtensionAbility",
                    moduleName: "support",
                    parameters: {
                        'ability.want.params.uiExtensionType': 'sys/commonUI',
                        'bundleName': topBundleName,
                    }
                }
                HiAiLog.info(TAG, `try startAbility`)
                try {
                    HiAIServiceAbility.globalContext.requestModalUIExtension(want, (err: BusinessError) => {
                        HiAiLog.error(TAG, `Failed to start error code :${err.code},error message :${err.message}`)
                    })
                } catch (e) {
                    HiAiLog.error(TAG, `startAbility failed ,errorCode:${e.code},error:${e.message}`);
                }
                ModelDownloadCapability.foreReportBaseInfo =
                    ModelDownloadReportUtil.setBaseLogInfo(modelDownloadInfo.resId, 'ForegroundDownload',
                        modelDownloadInfo.domain);
                ScreenChangeUtil.getInstance().registerScreenChange();
                return true;
            }
            case ModelDownloadType.MODEL_BACK_DOWNLOAD: { // Background download, which is invoked only by AI models.
                HiAiLog.info(TAG, "BackgroundDownload");
                ModelDownloadCapability.foreReportBaseInfo =
                    ModelDownloadReportUtil.setBaseLogInfo(modelDownloadInfo.resId, 'BackgroundDownload',
                        modelDownloadInfo.domain);
                let downloadConfig: DownloadConfig = new DownloadConfig();
                try {
                    data.readParcelable(downloadConfig);
                    HiAiLog.info(TAG,`Try read downloadConfig`);
                }catch (e) {
                    HiAiLog.error(TAG,`Failed to read downloadConfig e message: ${e.message}`)
                }
                if(downloadConfig){
                    ModelDownloadCapability.modelDownloadConfig = downloadConfig;
                    modelBaseInfo = new ModelBaseInfo(modelDownloadInfo.resId, modelDownloadInfo.domain,
                        ModelDownloadCapability.modelDownloadType, ModelDomain.MODEL_TYPE_AI, modelDownloadInfo.resCompatibleVersion,downloadConfig.networkType);
                    ModelDownloadCapability.backgroundModelBaseInfo = modelBaseInfo;
                }else {
                    let defaultDownloadConfig: ModelDownloadConfig = new ModelDownloadConfig();
                    defaultDownloadConfig.networkType = 0;
                    ModelDownloadCapability.modelDownloadConfig = defaultDownloadConfig;
                    HiAiLog.info(TAG, `defaultDownloadConfig network type is ${downloadConfig.networkType}`)
                    modelBaseInfo = new ModelBaseInfo(modelDownloadInfo.resId, modelDownloadInfo.domain,
                        ModelDownloadCapability.modelDownloadType, ModelDomain.MODEL_TYPE_AI,
                        modelDownloadInfo.resCompatibleVersion, downloadConfig.networkType);
                    ModelDownloadCapability.backgroundModelBaseInfo = modelBaseInfo;
                }
                HiAiLog.info(TAG,`background modelBaseInfo is ${JSON.stringify(modelBaseInfo)}`)
                ModelDownloadCapability.backgroundModelBaseInfo = modelBaseInfo;
                ModelDownloadCapability.backgroundModelDownloadInfo = modelDownloadInfo;
                ModelDownloadCapability.backgroundDownloadCallbackProxy = downloadCallbackStub;
                ModelDownloadCapability.backDownloadCallbackProxy = downloadCallbackStub;
                ModelTaskManager.getInstance().addModelDownloadTask(modelBaseInfo, modelDownloadInfo, downloadCallbackStub,downloadConfig);
                return true;
            }
            case ModelDownloadType.MODEL_UPGRADE : { // Update model immediately in the background.
                HiAiLog.info(TAG, "Upgrade model now");
                let modelType: string = data.readString();
                modelBaseInfo.setModelType(modelType);
                let upgradeConfig: DownloadConfig = new DownloadConfig();
                try {
                    data.readParcelable(upgradeConfig);
                    HiAiLog.info(TAG,`Try read upgradeConfigConfig`);
                }catch (e) {
                    HiAiLog.error(TAG,`Failed to read upgradeConfigConfig e message: ${e.message}`)
                }
                if(upgradeConfig){
                    ModelDownloadCapability.modelDownloadConfig = upgradeConfig;
                    HiAiLog.info(TAG,`UpgradeConfigConfig network type is ${upgradeConfig.networkType}`)
                    modelBaseInfo = new ModelBaseInfo(modelDownloadInfo.resId, modelDownloadInfo.domain,
                        ModelDownloadCapability.modelDownloadType, ModelDomain.MODEL_TYPE_AI, modelDownloadInfo.resCompatibleVersion,upgradeConfig.networkType);
                    ModelDownloadCapability.foreModelBaseInfo = modelBaseInfo;
                }else {
                    let defaultDownloadConfig: ModelDownloadConfig = new ModelDownloadConfig();
                    defaultDownloadConfig.networkType = 0;
                    ModelDownloadCapability.modelDownloadConfig = defaultDownloadConfig;
                    HiAiLog.info(TAG, `defaultUpgradeConfigConfig network type is ${upgradeConfig.networkType}`)
                    modelBaseInfo = new ModelBaseInfo(modelDownloadInfo.resId, modelDownloadInfo.domain,
                        ModelDownloadCapability.modelDownloadType, ModelDomain.MODEL_TYPE_AI,
                        modelDownloadInfo.resCompatibleVersion, upgradeConfig.networkType);
                    ModelDownloadCapability.foreModelBaseInfo = modelBaseInfo;
                }
                ModelTaskManager.getInstance().modelUpgrade(modelBaseInfo, modelDownloadInfo, downloadCallbackStub,upgradeConfig);
                return true;
            }
            default:
                return false;
        }
    }


    private async modelManagerTaskHandle(data: rpc.MessageSequence): Promise< boolean> {
        let modelType: string = data.readString();
        let modelInfoArray: Array<ModelDownloadInfo> = new Array();
        let length: number = data.readInt();
        for(let i = 1; i <= length; i++) {
            let modelDownloadInfo: ModelDownloadInfo = new ModelDownloadInfo();
            data.readParcelable(modelDownloadInfo);
            modelInfoArray.push(modelDownloadInfo);
        }
        HiAiLog.info(TAG, "onRemoteRequestHandle modelManagerTaskHandle modelInfoArray length is: " +modelInfoArray.length );

        switch(ModelDownloadCapability.modelDownloadType) {
            case ModelDownloadType.MODEL_DELETE : { // Delete model in local, which is invoked only by AI models.
                HiAiLog.info(TAG, "Delete model in local");
                let modelManagerCallback: rpc.IRemoteObject = data.readRemoteObject()
                let modelManagerCallbackStub: IModelManagerCallback = ModelManagerCallbackStub.asInterface(modelManagerCallback);
                ModelTaskManager.getInstance().modelDeleteLocal(modelInfoArray, modelManagerCallbackStub);
                return true;
            }
            case ModelDownloadType.MODEL_QUERY_LOCAL : { // Query model in local, which is invoked only by AI models.
                HiAiLog.info(TAG, "Query model in local");
                let modelQueryCallback: rpc.IRemoteObject = data.readRemoteObject()
                let modelQueryCallbackStub: IModelQueryCallback = ModelQueryCallbackStub.asInterface(modelQueryCallback);
                ModelTaskManager.getInstance().modelQueryLocal(modelInfoArray, modelQueryCallbackStub);
                return true;
            }
            case ModelDownloadType.MODEL_SUBSCRIBE: { // Model subscription.
                HiAiLog.info(TAG, "Model subscription");
                let modelManagerCallback: rpc.IRemoteObject = data.readRemoteObject()
                let modelManagerCallbackStub: IModelManagerCallback = ModelManagerCallbackStub.asInterface(modelManagerCallback);
                ModelTaskManager.getInstance().modelSubscribe(modelType, modelInfoArray, modelManagerCallbackStub);
                for(let modelDownloadInfo of modelInfoArray) {
                    let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(modelDownloadInfo.resId, 'subscribe', modelDownloadInfo.domain);
                    let startTime: Date = new Date()
                    ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.SUCCESS, ReportResultCodeMessage.SUCCESS, startTime.getTime());
                    ModelDownloadReportUtil.setReportInfoByBundleName(baseInfo);
                }
                return true;
            }
            case ModelDownloadType.MODEL_UNSUBSCRIBE : { // Model unsubscription.
                HiAiLog.info(TAG, "Model unsubscription");
                let modelManagerCallback: rpc.IRemoteObject = data.readRemoteObject()
                let modelManagerCallbackStub: IModelManagerCallback = ModelManagerCallbackStub.asInterface(modelManagerCallback);
                ModelTaskManager.getInstance().modelUnSubscribe(modelType, modelInfoArray, modelManagerCallbackStub);
                for(let modelDownloadInfo of modelInfoArray) {
                    let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(modelDownloadInfo.resId, 'unSubscribe', modelDownloadInfo.domain);
                    let startTime: Date = new Date()
                    ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.SUCCESS, ReportResultCodeMessage.SUCCESS, startTime.getTime());
                    ModelDownloadReportUtil.setReportInfoByBundleName(baseInfo);
                }
                return true;
            }
            default:
                return false;
        }
    }

    private async getTopAbilityElementName(): Promise<ElementName> {
        let elementName;
        try {
            elementName = await abilityManager.getTopAbility();
        } catch (error) {
            HiAiLog.error(TAG, "getTopAbility fail: " + JSON.stringify(error))
        }
        return elementName;
    }

    private getBundleNameFromTopElementName(elementName:ElementName): string {
        if (elementName === undefined) {
            // 没获取到顶部界面，异常
            return ""
        }
        try {
            let topBundleName = elementName.bundleName
            return topBundleName
        } catch (error) {
            return ""
        }
    }

}