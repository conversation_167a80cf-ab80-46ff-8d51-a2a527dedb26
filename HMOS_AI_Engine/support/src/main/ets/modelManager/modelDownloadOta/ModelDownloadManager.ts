/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import ModelBaseInfo, { ExtraInfo } from '../modelDownloadOta/modelDownloadInfo/ModelBaseInfo';
import ModelInfoManager, { ModelInfo } from '../modelDownloadOta/modelDownloadInfo/ModelInfoManager';
import ModelDownloadOta from './ModelDownloadOta';
import CommonConstants from '../utils/constants/CommonConstants';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import update from '@hms.system.update';
import { NetworkType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';

const TAG: string = "ModelDownloadManager";

/**
 * Model download task management class, which is the entry of the overall model download task.
 * Note that the OTA platform is preferentially used for model download.
 * If the OTA platform does not have a corresponding model, the IDS platform is used for model download.
 * @class
 * <AUTHOR>
 */
export default class ModelDownloadManager {
    private static instance: ModelDownloadManager = null;

    private constructor() {
    }

    /**
     * get single instance.
     *
     * @returns { ModelDownloadManager } the instance of ModelDownloadManager.
     */
    public static getInstance(): ModelDownloadManager{
        if(!ModelDownloadManager.instance) {
            ModelDownloadManager.instance = new ModelDownloadManager();
        }
        return ModelDownloadManager.instance;
    }

    /**
     * An asynchronous function to add a model download task.
     *
     * @param { ModelBaseInfo } modelBaseInfo - The base information of the model of type ModelBaseInfo.
     * @param { ModelDownloadInfo } modelDownloadInfo - The download information of the model of type ModelDownloadInfo.
     * @param { IModelDownloadCallback } modelCallbackProxy - A callback proxy for the model download of type IModelDownloadCallback.
     */
    async addModelDownloadTask(modelBaseInfo: ModelBaseInfo, modelDownloadInfo: ModelDownloadInfo, modelCallbackProxy: IModelDownloadCallback): Promise<void> {
        HiAiLog.info(TAG, `addModelDownloadTask start`);
        let bundleModelInfos: Array<update.PluginUpdateInfo> = [];
        let bundleModelInfo: update.PluginUpdateInfo = modelBaseInfo.getModelUpdateInfo();
        bundleModelInfos.push(bundleModelInfo);
        let callerInfo: update.PluginCallerInfo = modelBaseInfo.getCallerInfo();
        let modelInfo: ModelInfo = {
            modelBaseInfo: modelBaseInfo,
            modelCallbackProxy: modelCallbackProxy,
            modelDownloadStatus: {
                isExisted: false,
                isDownloadStart: false,
                isDownloadEnd: false,
                isDownloadSuccess: false
            },
            modelDownloadProgress: CommonConstants.DEFAULT_INT,
            isOTAPlatform: true,
            networkType: NetworkType.WLAN
        }
        HiAiLog.info(TAG, `addModelDownloadTask start111`);

        ModelDownloadOta.getInstance().queryModelCloud(bundleModelInfos, callerInfo).then((extraInfoArray: Array<ExtraInfo>)=>{ // Query the model through the OTA, add the model to the download task queue, and download the model.
            HiAiLog.info(TAG, `addModelDownloadTask queryModelCloud then`);
            modelBaseInfo.setExtraInfo(extraInfoArray[CommonConstants.DEFAULT_INT]);
            ModelInfoManager.addModelInfo(modelDownloadInfo.resId, modelInfo);
            ModelInfoManager.updateIsExistedStatus(modelDownloadInfo.resId, true);
            ModelInfoManager.updateModelSize(modelDownloadInfo.resId, extraInfoArray[CommonConstants.DEFAULT_INT].modelSize);
            let modelIdArray: Array<string> = [];
            modelIdArray.push(extraInfoArray[CommonConstants.DEFAULT_INT].modelID);
            ModelDownloadOta.getInstance().modelDownload(modelDownloadInfo, modelIdArray, callerInfo);
        }).catch(()=>{ // No model is found through the OTA platform. The IDS platform is used to query and download the model.
            HiAiLog.info(TAG, `addModelDownloadTask queryModelCloud catch`);
            // ModelDownloadIds.modelDownload(modelDownloadInfo.resId, modelDownloadInfo.domain, modelDownloadInfo.resVersion, modelInfo);
        })

    }

    /**
     * An asynchronous function to delete a model download task.
     *
     * @param { string } modelName - The name of the model to be deleted.
     */
    deleteModelDownloadTask(modelName: string): void {
        HiAiLog.info(TAG, `deleteModelDownloadTask start, modelName is: ` + modelName);
        ModelInfoManager.deleteModelInfo(modelName);
        let modelId: string = ModelInfoManager.getModelId(modelName);
        let modelIdArray: Array<string> = [];
        modelIdArray.push(modelId);
        ModelDownloadOta.getInstance().cancelModelDownload(modelIdArray);
    }

    /**
     * Method to upgrade a model.
     *
     * @param { ModelBaseInfo } modelBaseInfo - The base information of the model of type ModelBaseInfo.
     * @param { IModelDownloadCallback } modelCallbackProxy - A callback proxy for the model of type IModelDownloadCallback.
     */
    modelUpgrade(modelBaseInfo: ModelBaseInfo, modelCallbackProxy: IModelDownloadCallback): void {
        HiAiLog.info(TAG, `modelUpgrade start`);
    }

    /**
     * Method to subscribe to a model.
     *
     * @param { Array<ModelBaseInfo> } modelBaseInfos - An array of ModelBaseInfo objects representing the base information of multiple models.
     * @param { IModelDownloadCallback } modelCallbackProxy - A callback proxy for the model of type IModelDownloadCallback.
     */
    modelSubscribe(modelBaseInfos: Array<ModelBaseInfo>, modelCallbackProxy: IModelDownloadCallback): void {
        HiAiLog.info(TAG, `modelSubscribe start`);
    }

    /**
     * Method to unsubscribe from a model.
     *
     * @param { Array<ModelBaseInfo> } modelBaseInfos - An array of ModelBaseInfo objects representing the base information of multiple models.
     * @param { IModelDownloadCallback } modelCallbackProxy - A callback proxy for the model of type IModelDownloadCallback.
     */
    modelUnSubscribe(modelBaseInfos: Array<ModelBaseInfo>, modelCallbackProxy: IModelDownloadCallback): void {
        HiAiLog.info(TAG, `modelUnSubscribe start`);
    }
}

