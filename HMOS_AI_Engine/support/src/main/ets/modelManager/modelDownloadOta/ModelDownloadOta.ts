/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import ModelBaseInfo, { ExtraInfo } from './modelDownloadInfo/ModelBaseInfo'
import ModelInfoManager from './modelDownloadInfo/ModelInfoManager';
import OtaResultCodeConstant from '../utils/constants/OtaResultCodeConstant';
import CommonConstants from '../utils/constants/CommonConstants';
import HiAIServiceAbility from '../../framework/HiAIServiceAbility';
import ModelCopy from '../modelDownloadIds/modelManagerIds/ModelCopyIds';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import update from '@hms.system.update';
import fileUri from '@ohos.file.fileuri';
import fs from '@ohos.file.fs';
import File from '@ohos.file.fs';
import { BusinessError } from '@kit.BasicServicesKit';
import {
  ModelDownloadResultCode,
  ReportResultCode,
  ReportResultCodeMessage
} from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { ModelDownloadReportUtil } from '../utils/ModelManagerReportUtil';
import ModelDownloadCapability from '../ModelDownloadCapability';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { ModelDomain, ModelDownloadType,NetworkType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';
import ModelAgingManager from '../modelAgingManager/ModelAgingManager';
import { NetWorkUtil } from '@hms-ai/pdkfull/src/main/ets/utils/NetWorkUtil';
import ModelTaskManager from './ModelTaskManager';
import { NetworkChangeUtil } from '../utils/NetworkChangeUtil';
import StorageLevelWhiteListConstants from '../utils/constants/StorageLevelWhiteListConstants';
import { contextConstant, UIExtensionContentSession } from '@kit.AbilityKit';
import ModelDownloadUIExtentionAbility from '../../framework/ability/ModelDownloadUIExtensionAbility';

const TAG: string = "ModelDownloadOta";

/**
 * Indicates the implements of model download from OTA platform.
 * @class
 * <AUTHOR>
 */
export default class ModelDownloadOta {
  private static instance: ModelDownloadOta;
  private static networkTimeId: number;
  private modelUpdater: update.PluginUpdater;
  private listeningModels: Set<string> = new Set<string>();
  private static noNetWorkTimeId: number | undefined;
  private downloadProgressCallbacks: Map<string, ((downloadCallbackInfo: update.PluginUpdateCallbackInfo) => Promise<void>)> =
    new Map();

  private constructor() {
    HiAiLog.info(TAG, `ModelDownloadOta constructor`);
    this.modelUpdater = update.getPluginUpdater();
  }

  /**
   * get single instance.
   *
   * @returns { ModelDownloadOta } the instance of ModelDownloadOta.
   */
  public static getInstance(): ModelDownloadOta {
    HiAiLog.info(TAG, `ModelDownloadOta getInstance`);
    if (!ModelDownloadOta.instance) {
      ModelDownloadOta.instance = new ModelDownloadOta();
    }
    return ModelDownloadOta.instance;
  }


  /**
   * Query the model version on the OTA cloud server through the OTA client.
   * Note: Currently, only one model can be queried at a time.
   *
   * @param { string } modelName - The model name.
   * @param { Array<update.BundlePluginInfo> } bundleModelInfos - The model information to be queried.
   * @param { update.CallerInfo } callerInfo - The caller information to be queried.
   * @returns { Promise<Array<ExtraInfo>> } The extra model information which to be queried by OTA platform.
   */
  async queryModelCloud(bundleModelInfos: Array<update.PluginUpdateInfo>,
    callerInfo: update.PluginCallerInfo): Promise<Array<ExtraInfo>> {
    HiAiLog.info(TAG, `queryModelCloud start`);
    return new Promise((resolve, reject) => {
      this.modelUpdater.checkNewVersion(bundleModelInfos, callerInfo)
        .then(async (result: update.PluginCheckNewResult) => {
          let errorInfo: update.PluginErrorInfo = result.errorInfo;
          HiAiLog.info(TAG, `OTA checkNewVersion result is: ` + JSON.stringify(result));
          if (errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) { // query success
            if (result.newVersionInfos.length === CommonConstants.DEFAULT_INT) { // query result is empty
              HiAiLog.error(TAG, `OTA checkNewVersion result is empty`);
              let error: BusinessError = {
                code: ModelDownloadResultCode.NO_MODEL,
                name: '',
                message: 'There is no model in OTA Cloud.'
              }
              reject(error);
            } else { // query result is not empty
              let modelSize: number = Math.round(result.newVersionInfos[CommonConstants.DEFAULT_INT].pluginPackageSize /
                (CommonConstants.CONSTANT_INT_BYTES * CommonConstants.CONSTANT_INT_BYTES));
              let modelExtraInfo: ExtraInfo = {
                modelSize: modelSize,
                modelID: result.newVersionInfos[CommonConstants.DEFAULT_INT].pluginVersionId,
                modelVersion: result.newVersionInfos[CommonConstants.DEFAULT_INT].pluginVersionCode
              };
              let modelExtraInfoArray: Array<ExtraInfo> = [];
              modelExtraInfoArray.push(modelExtraInfo);
              resolve(modelExtraInfoArray);
            }
          } else { // query failed
            HiAiLog.error(TAG,
              `OTA checkNewVersion fail, errorNum：` + errorInfo.errorCode + ', errorMessage:' + errorInfo.errorMessage);
            let error: BusinessError = {
              code: errorInfo.errorCode,
              name: '',
              message: errorInfo.errorMessage
            }
            reject(error);
          }
        }).catch((error: BusinessError) => {
        if (JSON.stringify(error) !== '{}') {
          reject(error);
        }
      })
    })
  }

  /**
   * Start model download on the OTA client. Note: Currently, only one model can be downloaded at a time.
   *
   * @param { ModelDownloadInfo } modelDownloadInfo - Information related to the model download, of type ModelDownloadInfo.
   * @param { Array<string> } modelIdArray - An array of model ID of the models to be downloaded.
   * @param { update.PluginCallerInfo } callerInfo - Information about the caller.
   */
  public async modelDownload(modelDownloadInfo: ModelDownloadInfo, modelIdArray: Array<string>,
    callerInfo: update.PluginCallerInfo): Promise<void> {
    HiAiLog.info(TAG, `modelDownload start for model: ${modelDownloadInfo.resId}`);

    let bundleInfo: update.PluginBundleInfo;
    if (modelDownloadInfo.domain === ModelDomain.MODEL_TYPE_LORA) {
      bundleInfo = {
        bundleName: CommonConstants.HIAI_PACKAGE_NAME,
        abilityName: CommonConstants.HIAI_ABILITY_NAME,
        packageName: modelDownloadInfo.domain
      };
    } else {
      bundleInfo = {
        bundleName: CommonConstants.HIAI_PACKAGE_NAME,
        abilityName: CommonConstants.HIAI_ABILITY_NAME,
        packageName: ModelDomain.MODEL_TYPE_AI
      };
    }

    this.listenDownloadProgress(bundleInfo, modelDownloadInfo.resId);

    this.modelUpdater.download(modelIdArray, callerInfo).then((errorInfo: update.PluginErrorInfo) => {
      HiAiLog.info(TAG,
        `OTA start download for ${modelDownloadInfo.resId}, errorNum：` + errorInfo.errorCode + ', errorMessage:' +
        errorInfo.errorMessage);
      if (errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) {
        HiAiLog.info(TAG, `OTA start download success for ${modelDownloadInfo.resId}`);
      } else {
        HiAiLog.info(TAG, `OTA start download fail for ${modelDownloadInfo.resId}`);
      }
    }).catch(async (error) => {
      let storage = LocalStorage.getShared()
      let session: UIExtensionContentSession | undefined = storage.get<UIExtensionContentSession>('session');
      let modelName: string = modelDownloadInfo.resId;
      this.handelNextDownload(modelName);
      let downloadCallbackProxy: IModelDownloadCallback =
        await ModelInfoManager.getCallbackProxy(modelName) ||
        ModelDownloadCapability.backDownloadCallbackProxy;
      downloadCallbackProxy.onError(modelName, ModelDownloadResultCode.DOWNLOAD_EXCEPTION,
        `OTA start download error for ${modelDownloadInfo.resId}`);
      this.cancelModelDownload(modelIdArray);
      session?.terminateSelf();
      HiAiLog.info(TAG, `OTA start download error for ${modelDownloadInfo.resId}: ` + JSON.stringify(error));
    });
  }

  /**
   * Cancel model download on the OTA client. Note: Currently, only one model can be canceled at a time.
   *
   * @param { Array<string> } modelIdArray - An array of model ID of the models to be downloaded.
   */
  public cancelModelDownload(modelIdArray: Array<string>): void {
    if (!modelIdArray || modelIdArray.length === 0) {
      HiAiLog.info(TAG, 'cancelModelDownload: modelIdArray is empty');
      return;
    }

    const modelId = modelIdArray[CommonConstants.DEFAULT_INT];
    const modelName = ModelInfoManager.getModelNameById(modelId);

    if (!modelName) {
      HiAiLog.info(TAG, `cancelModelDownload: No model found for modelId: ${modelId}`);
      return;
    }

    HiAiLog.info(TAG, `cancelModelDownload start for modelId: ${modelId}, modelName: ${modelName}`);

    let bundleInfo: update.PluginBundleInfo = {
      bundleName: CommonConstants.HIAI_PACKAGE_NAME,
      abilityName: CommonConstants.HIAI_ABILITY_NAME,
      packageName: ModelDomain.MODEL_TYPE_AI
    };

    // 先移除下载监听，确保清理状态
    this.removeDownloadProgressListener(modelName, bundleInfo);
    // 确保模型被标记为下载已结束
    let modelInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelInfo) {
      modelInfo.modelDownloadStatus.isDownloadEnd = true;
    }

    this.modelUpdater.cancelDownload(modelIdArray).then((errorInfo: update.PluginErrorInfo) => {
      HiAiLog.info(TAG, `OTA cancelDownload result errorInfo for ${modelName}: ` + JSON.stringify(errorInfo));
      if (errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) {
        HiAiLog.info(TAG, `OTA cancelDownload success for ${modelName}`);
      } else {
        HiAiLog.info(TAG, `OTA cancelDownload fail for ${modelName}`);
      }
      this.removeDownloadProgressListener(modelName, bundleInfo);
      NetworkChangeUtil.getInstance().unregisterNetworkChange();
    }).catch((error) => {
      HiAiLog.info(TAG, `OTA cancelDownload error for ${modelName}: ` + JSON.stringify(error));
      this.removeDownloadProgressListener(modelName, bundleInfo);
      NetworkChangeUtil.getInstance().unregisterNetworkChange();
    });
  }

  /**
   * Pause model download on OTA client
   *
   * @param { Array<string> } modelIdArray - An array of model ID of the models to be downloaded.
   */
  public async pauseModelDownload(modelIdArray: Array<string>): Promise<void> {
    const modelId = modelIdArray[0];
    const modelName = ModelInfoManager.getModelNameById(modelId);

    HiAiLog.info(TAG, `pauseModelDownload start for modelId: ${modelId}, modelName: ${modelName}`);
    this.modelUpdater.pauseDownload(modelIdArray).then((errorInfo: update.PluginErrorInfo) => {
      HiAiLog.info(TAG, `OTA pauseDownload result errorInfo for ${modelName}: ` + JSON.stringify(errorInfo));
      if (errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) {
        HiAiLog.info(TAG, `OTA pauseDownload success for ${modelName}`);
      } else {
        HiAiLog.info(TAG, `OTA pauseDownload fail for ${modelName}`);
      }
    }).catch((error) => {
      HiAiLog.info(TAG, `OTA cancelDownload error for ${modelName}: ` + JSON.stringify(error));
    });
  }

  private removeDownloadProgressListener(modelName: string, bundleInfo: update.PluginBundleInfo): void {
    if (this.downloadProgressCallbacks.has(modelName)) {
      const callback = this.downloadProgressCallbacks.get(modelName);
      if (callback) {
        this.modelUpdater.off(bundleInfo, callback);
        this.downloadProgressCallbacks.delete(modelName);
        this.listeningModels.delete(modelName);
        HiAiLog.info(TAG, `Successfully removed download progress listener for ${modelName}`);
      }
    }
  }

  private listenDownloadProgress(bundleInfo: update.PluginBundleInfo, modelName: string): void {
    HiAiLog.info(TAG, `listen download progress start for ${modelName}`);

    // 检查是否已在监听该模型的下载进度
    if (this.listeningModels.has(modelName)) {
      HiAiLog.info(TAG, `Already listening to download progress for ${modelName}, skip creating new listener`);
      return;
    }

    try {
      this.listeningModels.add(modelName);

      try {
        const progressCallback = async (downloadCallbackInfo: update.PluginUpdateCallbackInfo): Promise<void> => {
          const callbackModelName: string = downloadCallbackInfo.pluginInfo.pluginName;

          // Skip if this callback is for a different model
          if (callbackModelName !== modelName) {
            HiAiLog.info(TAG, `Received callback for ${callbackModelName} but expected ${modelName}, skipping`);
            return;
          }

          let modelDomain: string = downloadCallbackInfo.pluginInfo.packageName;
          let downloadStatus: update.UpdateStatus = downloadCallbackInfo.status;
          let progress: number = downloadCallbackInfo.progress;
          let modelBaseInfo = ModelInfoManager.getModelBaseInfo(modelName);
          let resVersion: string = ModelInfoManager.getModelVersion(modelName) ||
          modelBaseInfo.getModelUpdateInfo().pluginInfos[0].currentPluginVersion;
          let compatibleVersion: string = ModelInfoManager.getModelCompatibleVersion(modelName) ||
          modelBaseInfo.getModelUpdateInfo().pluginInfos[0].compatibleVersion;
          if (modelDomain === StorageLevelWhiteListConstants.TTS) {
            HiAIServiceAbility.globalContext.area = contextConstant.AreaMode.EL1;
          }
          let filePath: string =
            HiAIServiceAbility.globalContext?.filesDir + '/' + modelDomain + '/' + modelName + '/' + compatibleVersion +
              '/' + resVersion + '.zip';
          let modelId = ModelInfoManager.getModelId(modelName);
          let modelIdArray: Array<string> = [];
          modelIdArray.push(modelId);

          HiAiLog.info(TAG, `listen downloadCallbackInfo for ${modelName}: ` + JSON.stringify(downloadCallbackInfo));

          if (downloadStatus === update.UpdateStatus.TRANSFER_SUCCESSFUL) {
            // 清除网络超时定时器，防止之前设置的定时器继续执行
            if (ModelDownloadOta.noNetWorkTimeId) {
              clearTimeout(ModelDownloadOta.noNetWorkTimeId);
              ModelDownloadOta.noNetWorkTimeId = undefined;
            }
            this.deleteModel(modelIdArray);
            HiAiLog.info(TAG, `OTA transfer successful for model: ${modelName}`);
            HiAiLog.info(TAG,`OTA transfer downloadStatus is ${downloadStatus} `)
            let modelBaseInfo: ModelBaseInfo = ModelInfoManager.getModelBaseInfo(modelName);
            let downloadCallbackProxy: IModelDownloadCallback =
              await ModelInfoManager.getCallbackProxy(modelName) || ModelDownloadCapability.backDownloadCallbackProxy;
            ModelAgingManager.getInstance().addLocalModel(modelBaseInfo, filePath, downloadCallbackProxy);

            this.removeDownloadProgressListener(modelName, bundleInfo);
            this.handelNextDownload(modelName);
            ModelInfoManager.deleteModelInfo(modelName);
            NetworkChangeUtil.getInstance().unregisterNetworkChange();
            HiAiLog.info(TAG, `OTA copyPackage deleteModelInfo for ${modelName}`);
          }

          switch (progress) {
            case CommonConstants.DEFAULT_INT: { // download start
              if (downloadStatus === update.UpdateStatus.DOWNLOADING) {
                HiAiLog.info(TAG, 'OTA download onStart modelName: ' + modelName);
                let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(modelName);
                ModelInfoManager.updateIsDownloadStartStatus(modelName, true);
                downloadCallbackProxy.onStart(modelName);
              }
              break;
            }
            case CommonConstants.MAX_PROGRESS: { // download end
              if (downloadStatus === update.UpdateStatus.DOWNLOAD_SUCCESSFUL) {
                // 清除网络超时定时器，防止之前设置的定时器继续执行
                if (ModelDownloadOta.noNetWorkTimeId) {
                  try {
                    HiAiLog.info(TAG,`Try clear noNetWorkTimeId`)
                    clearTimeout(ModelDownloadOta.noNetWorkTimeId);
                    ModelDownloadOta.noNetWorkTimeId = undefined;
                  }catch (e) {
                    HiAiLog.error(TAG,`Failed to clear noNetWorkTimeId cause :${e.message}`)
                  }
                }
                HiAiLog.info(TAG, 'OTA download onEnd modelName: ' + modelName);
                HiAiLog.info(TAG,`OTA onEnd downloadStatus is ${downloadStatus} `)
                this.copyModel(modelName, modelBaseInfo, modelIdArray).then((result: boolean) => {
                  HiAiLog.info(TAG, 'OTA copyModel onEnd result: ' + result);
                  if (result) { // HA report, download complete.
                    let baseInfo: BaseInfo;
                    if (ModelDownloadCapability.modelDownloadType === ModelDownloadType.MODEL_FORE_DOWNLOAD) {
                      baseInfo = ModelDownloadReportUtil.setBaseLogInfo(modelName, 'ForegroundDownload', modelDomain);
                      ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.SUCCESS,
                        ReportResultCodeMessage.SUCCESS, ModelDownloadCapability.foreDownloadStartTime.getTime());
                      ModelDownloadCapability.foreReportBaseInfo = baseInfo;
                    } else if (ModelDownloadCapability.modelDownloadType === ModelDownloadType.MODEL_BACK_DOWNLOAD) {
                      baseInfo = ModelDownloadReportUtil.setBaseLogInfo(modelName, 'BackgroundDownload', modelDomain);
                      ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.SUCCESS,
                        ReportResultCodeMessage.SUCCESS, ModelDownloadCapability.foreDownloadStartTime.getTime());
                      ModelDownloadCapability.foreReportBaseInfo = baseInfo;
                    } else {
                      baseInfo = ModelDownloadReportUtil.setBaseLogInfo(modelName, 'ActiveUpgrade', modelDomain);
                      ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.SUCCESS,
                        ReportResultCodeMessage.SUCCESS, ModelDownloadCapability.foreDownloadStartTime.getTime());
                      ModelDownloadCapability.foreReportBaseInfo = baseInfo;
                    }
                    ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);
                  }
                });
              }
              return;
            }
            default: // download schedule
              if (downloadStatus === update.UpdateStatus.DOWNLOADING) {
                if (ModelDownloadOta.networkTimeId) {
                  clearTimeout(ModelDownloadOta.networkTimeId);
                }
                if (ModelDownloadOta.noNetWorkTimeId) {
                  clearTimeout(ModelDownloadOta.noNetWorkTimeId);
                }
                HiAiLog.info(TAG, 'OTA download onSchedule modelName: ' + modelName);
                HiAiLog.info(TAG,`OTA onSchedule downloadStatus is ${downloadStatus} `)
                let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(modelName);
                ModelInfoManager.updateDownloadProgress(modelName, progress);
                downloadCallbackProxy.onSchedule(modelName, progress + '%');
                if (ModelDownloadCapability.modelDownloadType === ModelDownloadType.MODEL_BACK_DOWNLOAD ||
                  ModelDownloadCapability.modelDownloadType ===
                  ModelDownloadType.MODEL_UPGRADE) { // when the background downloads or upgrade
                  ModelDownloadOta.networkTimeId = setTimeout(async () => {
                    HiAiLog.info(TAG, `Network disconnect for ${modelName}, time out is 60000`);
                    let downloadCallbackProxy: IModelDownloadCallback =
                      await ModelInfoManager.getCallbackProxy(modelName) ||
                      ModelDownloadCapability.backDownloadCallbackProxy;
                    let isDownloadEnd: boolean = ModelInfoManager.getIsDownloadEndStatus(modelName);
                    let networkType = ModelInfoManager.getModelDownloadNetworkType(modelName);
                    if (!this.checkNetworkType(networkType ?? NetworkType.WLAN, modelName, downloadCallbackProxy)) {
                      downloadCallbackProxy.onError(modelName, ModelDownloadResultCode.DOWNLOAD_INTERRUPTION,
                        "The network is not connected,waiting for the network to recover");
                    } else if (!isDownloadEnd) {
                      downloadCallbackProxy.onError(modelName, ModelDownloadResultCode.DOWNLOAD_EXCEPTION,
                        "Download exception.");
                    }
                    clearTimeout(ModelDownloadOta.networkTimeId);
                  }, CommonConstants.NO_NETWORK_TIMEOUT);
                  ModelDownloadOta.noNetWorkTimeId = setTimeout(() => {
                    ModelTaskManager.getInstance().deleteModelDownloadTask(modelName);
                    clearTimeout(ModelDownloadOta.noNetWorkTimeId);
                  }, CommonConstants.NO_NETWORK_TIMEOUT * 2.5)
                }
                return;
              } else {
                HiAiLog.info(TAG, 'OTA copyModel onSchedule modelName: ' + modelName + ', progress is: ' + progress);
              }
          }
        };

        this.downloadProgressCallbacks.set(modelName, progressCallback);
        this.modelUpdater.on(bundleInfo, progressCallback);
        HiAiLog.info(TAG, `Successfully registered download progress listener for ${modelName}`);
      } catch (e) {
        this.removeDownloadProgressListener(modelName, bundleInfo);
        NetworkChangeUtil.getInstance().unregisterNetworkChange();
        HiAiLog.info(TAG, `Failed to register listening progress for ${modelName}: ${e}`);
      }
    } catch (error) {
      HiAiLog.info(TAG, `OTA listenDownloadProgress error for ${modelName}: ` + JSON.stringify(error));
      const callback = this.downloadProgressCallbacks.get(modelName);
      if (callback) {
        this.modelUpdater.off(bundleInfo, callback);
        this.downloadProgressCallbacks.delete(modelName);
      }
      this.listeningModels.delete(modelName);
      NetworkChangeUtil.getInstance().unregisterNetworkChange();
    }
  }


  private copyModel(modelName: string, modelBaseInfo: ModelBaseInfo, modelIdArray: Array<string>): Promise<boolean> {
    HiAiLog.info(TAG, `copyModel start for ${modelName}`);
    let domain: string =
      ModelInfoManager.getDomain(modelName) || modelBaseInfo.getModelUpdateInfo().pluginInfos[0].packageName;
    let resVersion: string = ModelInfoManager.getModelVersion(modelName) ||
    modelBaseInfo.getModelUpdateInfo().pluginInfos[0].currentPluginVersion;
    let compatibleVersion: string = ModelInfoManager.getModelCompatibleVersion(modelName) ||
    modelBaseInfo.getModelUpdateInfo().pluginInfos[0].compatibleVersion;
    let filePath: string =
      HiAIServiceAbility.globalContext?.filesDir + '/' + domain + '/' + modelName + '/' + compatibleVersion + '/' +
        resVersion + '.zip';
    let uri = fileUri.getUriFromPath(filePath);
    HiAiLog.info(TAG, `copyPackage uri for ${modelName} is: ` + uri);

    return new Promise((resolve, reject) => {
      let isMakeFile: boolean = ModelCopy.makefile(filePath);
      if (isMakeFile) {
        fs.open(uri, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE).then(async (fileObj: File.File) => {
          let file = fileObj;
          let fd: number = file.fd;
          let fds: Array<number> = [];
          fds.push(fd);
          await this.modelUpdater.copyPackage(modelIdArray, fds)
            .then(async (errorInfo: update.PluginErrorInfo) => {
              HiAiLog.info(TAG, `OTA copyPackage result errorInfo for ${modelName}: ` + JSON.stringify(errorInfo));
              let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(modelName);
              if (errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) {
                HiAiLog.info(TAG, `OTA copyPackage success for ${modelName}`);
                ModelInfoManager.updateIsDownloadSuccessStatus(modelName, true);
                ModelInfoManager.updateIsDownloadStartStatus(modelName, false);
                ModelInfoManager.updateIsDownloadEndStatus(modelName, true);
                ModelDownloadUIExtentionAbility.hasTriggeredCallback = true;
                resolve(true);
              } else {
                HiAiLog.info(TAG, `OTA copyPackage fail for ${modelName}`);
                ModelInfoManager.updateIsDownloadStartStatus(modelName, false);
                ModelInfoManager.updateIsDownloadEndStatus(modelName, true);
                downloadCallbackProxy.onError(modelName, errorInfo.data[CommonConstants.DEFAULT_INT].errorCode,
                  errorInfo.data[CommonConstants.DEFAULT_INT].errorMessage);
                this.handelNextDownload(modelName);
                resolve(false);
              }
            })
            .catch((error) => {
              HiAiLog.info(TAG, `OTA copyPackage error for ${modelName}: ` + JSON.stringify(error));
              this.handelNextDownload(modelName);
              let bundleInfo: update.PluginBundleInfo = {
                bundleName: CommonConstants.HIAI_PACKAGE_NAME,
                abilityName: CommonConstants.HIAI_ABILITY_NAME,
                packageName: ModelDomain.MODEL_TYPE_AI
              };
              this.removeDownloadProgressListener(modelName, bundleInfo);
              resolve(false);
            })
            .finally(() => {
              if (fd) {
                fs.closeSync(fd);
              }
            })
        });
      }
    })
  }

  private handelNextDownload(modelName: string): void {
    if (ModelInfoManager.getIsForeGroundDownload(modelName)) {
      ModelTaskManager.handleForegroundTaskComplete(modelName);
    }
  }

  private deleteModel(modelIdArray: Array<string>): void {
    HiAiLog.info(TAG, `deleteModel start`);
    let bundleInfo: update.PluginBundleInfo = {
      bundleName: CommonConstants.HIAI_PACKAGE_NAME,
      abilityName: CommonConstants.HIAI_ABILITY_NAME,
      packageName: ModelDomain.MODEL_TYPE_AI
    };

    this.modelUpdater.deletePackage(modelIdArray).then((errorInfo: update.PluginErrorInfo) => {
      HiAiLog.info(TAG, `OTA deletePackage result errorInfo ` + JSON.stringify(errorInfo));
      if (errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) {
        HiAiLog.info(TAG, `OTA deletePackage success`);
      } else {
        HiAiLog.info(TAG, `OTA deletePackage fail`);
      }
    }).catch((error) => {
      const modelId = modelIdArray[CommonConstants.DEFAULT_INT];
      const modelName = ModelInfoManager.getModelNameById(modelId);
      if (modelName) {
        this.removeDownloadProgressListener(modelName, bundleInfo);
      }
      NetworkChangeUtil.getInstance().unregisterNetworkChange();
      HiAiLog.info(TAG, `OTA deletePackage error: ` + JSON.stringify(error));
    });
  }

  /**
   * Check if the current network type matches the required network type for download
   *
   * @param { number } networkType - The required network type (0 for WLAN only, 1 for any network)
   * @param { string } resId - The model ID for error reporting
   * @param { IModelDownloadCallback } downloadCallback - The download callback
   * @returns { boolean } true if network type is valid, false otherwise
   */
  private checkNetworkType(networkType: number, resId: string, downloadCallback: IModelDownloadCallback): boolean {
    if (!NetWorkUtil.isConnectNetwork()) {
      HiAiLog.error(TAG,`net is not connected`)
      return false;
    }

    if (networkType === NetworkType.WLAN) {
      if (!NetWorkUtil.isWifiNetwork()) {
        return false;
      }
    } else if (networkType === NetworkType.ANY) {
      if (!NetWorkUtil.isWifiNetwork() && !NetWorkUtil.isCellularNetwork()) {
        return false;
      }
    }
    return true;
  }
}