/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import StaticSubscriberExtensionAbility from '@ohos.application.StaticSubscriberExtensionAbility';
import commonEventManager from '@ohos.commonEventManager'
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import update from '@hms.system.update';
import fileUri from '@ohos.file.fileuri';
import fs from '@ohos.file.fs';
import ModelAgingManager from '../modelAgingManager/ModelAgingManager';
import ModelBaseInfo from './modelDownloadInfo/ModelBaseInfo';
import { ModelDownloadType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';
import { PluginInfo, EventParameters } from '../utils/constants/ReUpdateConstants';
import HiAIServiceAbility from '../../framework/HiAIServiceAbility';
import CommonConstants from '../utils/constants/CommonConstants';
import ModelCopyIds from '../modelDownloadIds/modelManagerIds/ModelCopyIds';
import ModelTaskManager from '../modelDownloadOta/ModelTaskManager';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import { ModelDownloadReportUtil } from '../utils/ModelManagerReportUtil';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { ReportResultCode, ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import ModelDownloadCapability from '../ModelDownloadCapability';
import StorageLevelWhiteListConstants from '../utils/constants/StorageLevelWhiteListConstants';
import { contextConstant } from '@kit.AbilityKit';

const TAG: string = "StaticSubscriber";

/**
 * Static subscriber class for handling model updates.
 * This class extends StaticSubscriberExtensionAbility to receive and process model update events.
 *
 * @class
 * @extends StaticSubscriberExtensionAbility
 */
export default class StaticSubscriber extends StaticSubscriberExtensionAbility {
  /**
   * Handles the model package by copying and unzipping it.
   *
   * @param {PluginInfo} pluginInfo - Information about the plugin.
   * @param {string} modelId - The ID of the model.
   * @returns {Promise<void>} A promise that resolves when the model package is handled.
   */
  private async handleModelPackage(pluginInfos: PluginInfo[], modelIds: string[]): Promise<void> {
    for (let i = 0; i < pluginInfos.length; i++) {
      let pluginInfo = pluginInfos[i];
      let modelId = modelIds[i];
      const filePath = this.getModelFilePath(pluginInfo);
      await ModelCopyIds.makefile(filePath);
      await this.copyAndUnzipModel(filePath, modelId, pluginInfo);
    }
  }

  /**
   * Gets the file path for the model based on plugin information.
   *
   * @param {PluginInfo} pluginInfo - Information about the plugin.
   * @returns {string} The complete file path for the model.
   */
  private getModelFilePath(pluginInfo: PluginInfo): string {
    if (pluginInfo.packageName === StorageLevelWhiteListConstants.TTS) {
      HiAIServiceAbility.globalContext.area = contextConstant.AreaMode.EL1;
    }
    return `${HiAIServiceAbility.globalContext?.filesDir}/${pluginInfo.packageName}/${pluginInfo.pluginName}/${pluginInfo.compatibleVersion}/${pluginInfo.currentPluginVersion}.zip`;
  }

  /**
   * Copies and unzips the model package.
   *
   * @param {string} filePath - The path where the model will be stored.
   * @param {string} modelId - The ID of the model.
   * @param {PluginInfo} pluginInfo - Information about the plugin.
   * @returns {Promise<void>} A promise that resolves when copying and unzipping is complete.
   */
  private async copyAndUnzipModel(filePath: string, modelId: string, pluginInfo: PluginInfo): Promise<void> {
    const uri = fileUri.getUriFromPath(filePath);
    const fileObj = await fs.open(uri, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
    const fd = fileObj.fd;

    try {
      await this.copyModelPackage(modelId, fd);
      await this.unzipModelPackage(filePath, pluginInfo);
    } finally {
      fs.closeSync(fd);
    }
  }

  /**
   * Copies the model package from the update system.
   *
   * @param {string} modelId - The ID of the model.
   * @param {number} fd - The file descriptor for writing.
   * @returns {Promise<void>} A promise that resolves when copying is complete.
   * @throws {Error} If copying fails.
   */
  private async copyModelPackage(modelId: string, fd: number): Promise<void> {
    const modelUpdater = update.getPluginUpdater();
    const result = await modelUpdater.copyPackage([modelId], [fd]);

    if (result.errorCode === 0) {
      HiAiLog.info(TAG, 'Model package copied successfully');
      await modelUpdater.deletePackage([modelId]);
    } else {
      throw new Error(`Failed to copy model package: ${result.errorMessage}`);
    }
  }

  /**
   * Unzips the model package and adds it to the local model manager.
   *
   * @param {string} filePath - The path to the model package.
   * @param {PluginInfo} pluginInfo - Information about the plugin.
   * @returns {Promise<void>} A promise that resolves when unzipping is complete.
   */
  private async unzipModelPackage(filePath: string, pluginInfo: PluginInfo): Promise<void> {
    let modelBaseInfo = new ModelBaseInfo(
      pluginInfo.pluginName,
      pluginInfo.packageName,
      ModelDownloadType.MODEL_UPGRADE,
      pluginInfo.pluginType,
      pluginInfo.compatibleVersion
    );
    // 设置额外信息
    const extraInfo = {
      modelSize: await this.getFileSize(filePath),
      modelID: pluginInfo.pluginName, // 使用 pluginName 作为 modelID
      modelVersion: pluginInfo.currentPluginVersion
    };
    modelBaseInfo.setExtraInfo(extraInfo);
    HiAiLog.info(TAG, `update modelBaseInfo is ${JSON.stringify(modelBaseInfo)}`);
    await ModelAgingManager.getInstance().addLocalModel(modelBaseInfo, filePath);
    HiAiLog.info(TAG, 'Model unzipped successfully');
  }

  /**
   * Gets the size of a file in MB.
   *
   * @param {string} filePath - The path to the file.
   * @returns {Promise<number>} A promise that resolves with the file size in MB.
   */
  private async getFileSize(filePath: string): Promise<number> {
    try {
      const stat = await fs.stat(filePath);
      return Math.round(stat.size /
        (CommonConstants.CONSTANT_INT_BYTES * CommonConstants.CONSTANT_INT_BYTES)); // 转换为 MB
    } catch (error) {
      HiAiLog.error(TAG, `Failed to get file size: ${error}`);
      return CommonConstants.DEFAULT_INT;
    }
  }

  /**
   * @override
   *
   * Handles the received event for model updates.
   * This method is called when a model update event is received.
   *
   * @param {commonEventManager.CommonEventData} event - The received event data.
   */
  onReceiveEvent(event: commonEventManager.CommonEventData): void {
    HiAiLog.info(TAG, `onReceiveEvent test event: ${JSON.stringify(event.parameters)}`);

    try {
      const parameters = event.parameters as EventParameters;
      HiAiLog.info(TAG, `onReceiveEvent test event parameters is ${JSON.stringify(parameters)}}`);

      HiAiLog.info(TAG, `Raw pluginInfos: ${parameters.pluginInfos},Raw versionIds: ${parameters.versionIds}`);

      let pluginInfos: PluginInfo[];
      let versionIds: string[];
      try {
        pluginInfos = JSON.parse(parameters.pluginInfos);
        versionIds = JSON.parse(parameters.versionIds);
        HiAiLog.info(TAG, `First parse pluginInfos: ${JSON.stringify(pluginInfos)}`);
      } catch (parseError) {
        HiAiLog.error(TAG, `First parse attempt failed: ${parseError}`);
        try {
          let cleanPluginInfos = parameters.pluginInfos.replace(/\\/g, '').replace(/^"(.*)"$/, '$1');
          let cleanVersionIds = parameters.versionIds.replace(/\\/g, '').replace(/^"(.*)"$/, '$1');
          HiAiLog.info(TAG, `Cleaned pluginInfos: ${cleanPluginInfos},Cleaned versionIds: ${cleanVersionIds}`);
          pluginInfos = JSON.parse(cleanPluginInfos);
          versionIds = JSON.parse(cleanVersionIds);
        } catch (retryError) {
          HiAiLog.error(TAG, `Failed to parse JSON after cleanup: ${retryError}`);
          throw retryError;
        }
      }
      this.handleModelPackage(pluginInfos, versionIds).catch(error => {
        HiAiLog.error(TAG, `Error handling model package: ${error}`);
      });

      let baseInfo: BaseInfo;
      let modelDownloadInfo = new ModelDownloadInfo();
      modelDownloadInfo.domain = pluginInfos[0].packageName;
      modelDownloadInfo.resId = pluginInfos[0].pluginName;
      modelDownloadInfo.resCompatibleVersion = pluginInfos[0].compatibleVersion;
      modelDownloadInfo.resVersion = pluginInfos[0].currentPluginVersion;

      baseInfo =
        ModelDownloadReportUtil.setBaseLogInfo(modelDownloadInfo.resId, 'StaticUpgrade', modelDownloadInfo.domain);
      ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.SUCCESS, ReportResultCodeMessage.SUCCESS,
        ModelDownloadCapability.foreDownloadStartTime.getTime());
      ModelDownloadReportUtil.setReportInfoByBundleName(baseInfo);

      HiAiLog.info(TAG,
        `static upgrade model domain is ${modelDownloadInfo.domain}、model is ${modelDownloadInfo.resId}、compatibleVersion is ${modelDownloadInfo.resCompatibleVersion}、version is ${modelDownloadInfo.resVersion}`);
      let modelType = pluginInfos[0].pluginType;
      HiAiLog.info(TAG, `static upgrade model type is ${modelType}`);
      ModelTaskManager.getInstance().modelSubscribe(modelType, [modelDownloadInfo]);
    } catch (error) {
      HiAiLog.error(TAG, `Error processing event: ${error}`);
    }
  }
}