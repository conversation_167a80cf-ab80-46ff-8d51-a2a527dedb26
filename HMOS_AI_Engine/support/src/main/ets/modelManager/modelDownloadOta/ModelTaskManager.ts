/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import ModelBaseInfo, { ExtraInfo } from './modelDownloadInfo/ModelBaseInfo';
import ModelInfoManager, { ModelInfo } from './modelDownloadInfo/ModelInfoManager';
import ModelDownloadOta from './ModelDownloadOta';
import CommonConstants from '../utils/constants/CommonConstants';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';
import update from '@hms.system.update';
import ModelDownloadIds from '../modelDownloadIds/modelDownloadIds/ModelDownloadIds';
import { BusinessError } from '@kit.BasicServicesKit';
import { ModelDownloadResultCode, ReportResultCode } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { IModelManagerCallback } from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/IModelManagerCallback';
import ModelUpgradeOta from './ModelUpgradeOta';
import { IModelQueryCallback } from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/modelQuery/IModelQueryCallback';
import OtaResultCodeConstant from '../utils/constants/OtaResultCodeConstant';
import ModelAgingManager from '../modelAgingManager/ModelAgingManager';
import CeliaPrivacyVerification from '../privacyVerification/celiaPrivacyVerification/CeliaPrivacyVerification'
import { ModelDomain } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';
import ResultInfo from '@hms-ai/pdkfull/src/main/ets/modelUpgrade/ModelManagerResultInfo';
import ModelOperate from '../modelAgingManager/ModelOperate';
import { ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode'
import HiAIServiceAbility from '../../framework/HiAIServiceAbility';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { ModelDownloadReportUtil } from '../utils/ModelManagerReportUtil';
import { NetworkChangeUtil } from '../utils/NetworkChangeUtil';
import ModelDownloadConfig from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConfig';

const TAG: string = "ModelTaskManager";

/**
 * Model download task management class, which is the entry of the overall model download task.
 * Note that the OTA platform is preferentially used for model download.
 * If the OTA platform does not have a corresponding model, the IDS platform is used for model download.
 * @class
 * <AUTHOR>
 */
export default class ModelTaskManager {
    private static instance: ModelTaskManager = null;

    public static activeForegroundTasks: Set<string> = new Set();

    /**
     * Active window set , which is control model download foreground window
     */
    public static activeWindowSet: Set<string> = new Set();

    private constructor() {
    }

    /**
     * get single instance.
     *
     * @returns { ModelTaskManager } the instance of ModelTaskManager.
     */
    public static getInstance(): ModelTaskManager{
        if(!ModelTaskManager.instance) {
            ModelTaskManager.instance = new ModelTaskManager();
        }
        return ModelTaskManager.instance;
    }

    /**
     * Adds a model download task and execute download processes.
     *
     * @param { ModelBaseInfo } modelBaseInfo - The base information of the model to be downloaded.
     * @param { ModelDownloadInfo } modelDownloadInfo - The detailed information of the model download task.
     * @param { IModelDownloadCallback } modelCallbackProxy - The callback proxy for handling download events and errors.
     * @returns { Promise<void> } A promise that resolves when the download task is added or processed.
     */
    async addModelDownloadTask(modelBaseInfo: ModelBaseInfo, modelDownloadInfo: ModelDownloadInfo,
      modelCallbackProxy: IModelDownloadCallback, downloadConfig: ModelDownloadConfig): Promise<void> {
        // 检查是否已有相同模型的下载任务
        if (ModelInfoManager.getModelInfo(modelDownloadInfo.resId)) {
            let isDownloadEnd = ModelInfoManager.getIsDownloadEndStatus(modelDownloadInfo.resId);
            if (!isDownloadEnd) {
                HiAiLog.info(TAG, `${modelDownloadInfo.resId} is already downloading, reject new request`);
                modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.TASK_BUSY,
                    `Model ${modelDownloadInfo.resId} is downloading`);
                return;
            }
            this.deleteModelDownloadTask(modelDownloadInfo.resId);
        }

        let modelInfo = this.getModelInfo(modelBaseInfo, modelCallbackProxy,downloadConfig);
        ModelInfoManager.addModelInfo(modelDownloadInfo.resId, modelInfo);
        let isForeground = ModelInfoManager.getIsForeGroundDownload(modelDownloadInfo.resId);
        HiAiLog.info(TAG,`addModelDownloadTask model download type is ${isForeground}`)
        if (isForeground) {
            if (ModelTaskManager.activeForegroundTasks.has(modelDownloadInfo.resId)) {
                HiAiLog.info(TAG,`activeForegroundTasks size is ${ModelTaskManager.activeForegroundTasks.size}`)
                modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.DOWNLOAD_INTERRUPTION,
                    `Foreground download limit reached.Please wait for other downloads to complete.`)
                this.deleteModelDownloadTask(modelDownloadInfo.resId);
                return;
            }
            if (ModelTaskManager.activeForegroundTasks.size >= CommonConstants.MAX_CONCURRENT_FOREGROUND) {
                modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.DOWNLOAD_INTERRUPTION,
                    `Foreground download limit reached.Please wait for other downloads to complete.`)
                this.deleteModelDownloadTask(modelDownloadInfo.resId);
                return;
            }
            ModelTaskManager.activeForegroundTasks.add(modelDownloadInfo.resId);
        }
        await this.executeDownload(modelBaseInfo, modelDownloadInfo, modelCallbackProxy,downloadConfig);
    }


    private async executeDownload(modelBaseInfo: ModelBaseInfo, modelDownloadInfo: ModelDownloadInfo,
        modelCallbackProxy: IModelDownloadCallback,downloadConfig: ModelDownloadConfig): Promise<void> {

        HiAiLog.info(TAG, `executeDownload start for ${modelDownloadInfo.resId}`);

        try {
            let bundleModelInfos = this.getBundleInfos(modelBaseInfo);
            let callerInfo: update.PluginCallerInfo = modelBaseInfo.getCallerInfo();
            let modelInfo = this.getModelInfo(modelBaseInfo,modelCallbackProxy,downloadConfig);

            ModelInfoManager.addModelInfo(modelDownloadInfo.resId, modelInfo);

            await ModelDownloadOta.getInstance().queryModelCloud(bundleModelInfos, callerInfo)
                .then(async (extraInfoArray: Array<ExtraInfo>) => {
                    HiAiLog.info(TAG, `addModelDownloadTask queryModelCloud then`);
                    modelBaseInfo.setExtraInfo(extraInfoArray[CommonConstants.DEFAULT_INT]);
                    ModelInfoManager.updateIsExistedStatus(modelDownloadInfo.resId, true);
                    let modelIdArray: Array<string> = [];
                    modelIdArray.push(extraInfoArray[CommonConstants.DEFAULT_INT].modelID);
                    let storePath = HiAIServiceAbility.globalContext?.filesDir;
                    let checkResult = await ModelOperate.getInstance().checkStorageSpace(modelBaseInfo, storePath)
                    HiAiLog.info(TAG,`space check result is ${checkResult}`);
                    if (!checkResult) {
                        HiAiLog.info(TAG,`Start callback space model is ${modelDownloadInfo.resId}`);
                        modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.OUT_OF_MEMORY, ReportResultCodeMessage.SPACE_INSUFFICIENT);
                        let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(modelDownloadInfo.resId, 'Space insufficient, failed to download', modelDownloadInfo.domain);
                        let startTime: Date = new Date()
                        ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.OUT_OF_MEMORY, ReportResultCodeMessage.SPACE_INSUFFICIENT, startTime.getTime());
                        this.deleteModelDownloadTask(modelDownloadInfo.resId);
                        return;
                    }
                    ModelDownloadOta.getInstance().modelDownload(modelDownloadInfo, modelIdArray, callerInfo);
                })
                .catch((error: BusinessError)=>{
                    HiAiLog.error(TAG, `addModelDownloadTask queryModelCloud on OTA catch error is: ` + JSON.stringify(error));
                    if(this.isIdsVersion(modelDownloadInfo.resVersion)) {
                        ModelInfoManager.updateDownloadPlatform(modelDownloadInfo.resId, false);
                        ModelDownloadIds.modelDownload(modelDownloadInfo.resId, modelDownloadInfo.domain, modelDownloadInfo.resVersion, modelInfo);
                    } else {
                        ModelTaskManager.handleForegroundTaskComplete(modelDownloadInfo.resId);
                        ModelInfoManager.updateIsDownloadEndStatus(modelDownloadInfo.resId, true);
                        modelCallbackProxy.onError(modelDownloadInfo.resId, error.code, error.message);
                        NetworkChangeUtil.getInstance().unregisterNetworkChange();
                    }
                });
        } catch (error) {
            HiAiLog.error(TAG, `executeDownload error: ${error}`);
            let modelInfo = this.getModelInfo(modelBaseInfo, modelCallbackProxy,downloadConfig);
            if(this.isIdsVersion(modelDownloadInfo.resVersion)) {
                ModelInfoManager.updateDownloadPlatform(modelDownloadInfo.resId, false);
                ModelDownloadIds.modelDownload(modelDownloadInfo.resId, modelDownloadInfo.domain, modelDownloadInfo.resVersion, modelInfo);
            } else {
                ModelTaskManager.handleForegroundTaskComplete(modelDownloadInfo.resId);
                ModelInfoManager.updateIsDownloadEndStatus(modelDownloadInfo.resId, true);
                modelCallbackProxy.onError(modelDownloadInfo.resId, error.code, error.message);
                NetworkChangeUtil.getInstance().unregisterNetworkChange();
            }
            // NetworkChangeUtil.getInstance().unregisterNetworkChange();
            throw error;
        }
    }

    private onForegroundTaskComplete(taskId: string): void {
        ModelTaskManager.activeForegroundTasks.delete(taskId);
        HiAiLog.info(TAG,`After download one model , the downing tasks are ${ModelTaskManager.activeForegroundTasks.size}`);
    }

    /**
     * An synchronization function to delete a model download task.
     *
     * @param { string } modelName - The name of the model to be deleted.
     */
    deleteModelDownloadTask(modelName: string): void {
        HiAiLog.info(TAG, `ModelTaskManager deleteModelDownloadTask start, modelName is: ` + modelName);

        // 先标记下载已结束，防止再次请求时状态不一致
        let modelInfo = ModelInfoManager.getModelInfo(modelName);
        if (modelInfo) {
            modelInfo.modelDownloadStatus.isDownloadEnd = true;
            modelInfo.modelDownloadStatus.isDownloadStart = false;
        }

        let isOTAPlatform: boolean = ModelInfoManager.getDownloadPlatform(modelName);
        if(isOTAPlatform) {
            let modelId: string = ModelInfoManager.getModelId(modelName);
            let modelIdArray: Array<string> = [];
            modelIdArray.push(modelId);
            ModelDownloadOta.getInstance().cancelModelDownload( modelIdArray);
        } else {
            ModelDownloadIds.cancelModelDownload(modelName);
        }
        ModelTaskManager.handleForegroundTaskComplete(modelName);
        ModelInfoManager.deleteModelInfo(modelName);
    }

    /**
     * An synchronization function to upgrade a model.
     *
     * @param { ModelBaseInfo } modelBaseInfo - The base information of the model of type ModelBaseInfo.
     * @param { ModelDownloadInfo } modelDownloadInfo - The download information of the model of type ModelDownloadInfo.
     * @param { IModelDownloadCallback } modelCallbackProxy - A callback proxy for the model of type IModelDownloadCallback.
     */
    modelUpgrade(modelBaseInfo: ModelBaseInfo, modelDownloadInfo: ModelDownloadInfo, modelCallbackProxy: IModelDownloadCallback,downloadConfig:ModelDownloadConfig): void {
        HiAiLog.info(TAG, `ModelTaskManager modelUpgrade start`);
        let modelCurrentVersion: string = modelDownloadInfo.resVersion;
        let modelCompatibleVersion: string = modelDownloadInfo.resCompatibleVersion;
        if ( modelCompatibleVersion === CommonConstants.EMPTY_STRING ) {
            modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, ReportResultCodeMessage.COMPATIBLE_VERSION_IS_EMPTY);
            return;
        } else if( modelCurrentVersion === CommonConstants.EMPTY_STRING ) {
            modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, ReportResultCodeMessage.CURRENT_VERSION_IS_EMPTY);
            return;
        }
        let bundleModelInfos = this.getBundleInfos(modelBaseInfo);
        let callerInfo: update.PluginCallerInfo = modelBaseInfo.getCallerInfo();
        let modelInfo = this.getModelInfo(modelBaseInfo, modelCallbackProxy, downloadConfig);
        ModelDownloadOta.getInstance().queryModelCloud(bundleModelInfos, callerInfo).then((extraInfoArray: Array<ExtraInfo>)=>{ // Query the model through the OTA, add the model to the download task queue, and download the model.
            HiAiLog.info(TAG, `modelUpgrade queryModelCloud then`);
            let cloudVersion: string = extraInfoArray[0].modelVersion;
            if(this.isLatestVersion(modelCurrentVersion, cloudVersion)) { // A new version model is available on the OTA cloud side.
                ModelInfoManager.addModelInfo(modelDownloadInfo.resId, modelInfo);
                modelBaseInfo.setExtraInfo(extraInfoArray[CommonConstants.DEFAULT_INT]);
                ModelInfoManager.updateIsExistedStatus(modelDownloadInfo.resId, true);
                let modelIdArray: Array<string> = [];
                modelIdArray.push(extraInfoArray[CommonConstants.DEFAULT_INT].modelID);
                let storePath = HiAIServiceAbility.globalContext?.filesDir;
                ModelOperate.getInstance().checkStorageSpace(modelBaseInfo, storePath).then((checkResult) => {
                    HiAiLog.info(TAG, `space check result is ${checkResult}`);
                    if (!checkResult) {
                        HiAiLog.info(TAG, `Start callback space model is ${modelDownloadInfo.resId}`);
                        modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.OUT_OF_MEMORY,
                            ReportResultCodeMessage.SPACE_INSUFFICIENT);
                        this.deleteModelDownloadTask(modelDownloadInfo.resId);
                        return;
                    }
                }).catch((error) => {
                    HiAiLog.error(TAG, `checkStorageSpace failed: ${error}`);
                });
                ModelDownloadOta.getInstance().modelDownload(modelDownloadInfo, modelIdArray, callerInfo);
            } else { // No new version model is available on the OTA cloud side.
                modelCallbackProxy.onError(modelDownloadInfo.resId, ModelDownloadResultCode.IS_LATEST_VERSION, ReportResultCodeMessage.MODEL_IS_LATEST);
            }
        }).catch((error: BusinessError)=>{ // No model is found through the OTA platform.
            HiAiLog.error(TAG, `modelUpgrade queryModelCloud on OTA catch error is: ` + JSON.stringify(error));
            modelCallbackProxy.onError(modelDownloadInfo.resId, error.code, error.message);
        })
    }

    /**
     * An synchronization function to query model in local.
     *
     * @param { Array<ModelDownloadInfo> } modelDownloadInfos - An array of ModelDownloadInfo objects representing the base information of multiple models.
     * @param { IModelQueryCallback } modelQueryCallbackProxy - A callback proxy for the model of type IModelQueryCallback.
     */
    modelQueryLocal(modelDownloadInfos: Array<ModelDownloadInfo>, modelQueryCallbackProxy: IModelQueryCallback): void {
        HiAiLog.info(TAG, `modelQueryLocal start`);
        ModelAgingManager.getInstance().queryLatestModel(modelDownloadInfos, modelQueryCallbackProxy);
    }


    /**
     * An synchronization function to delete model in local.
     *
     * @param { ModelDownloadInfo } modelDownloadInfos - An array of ModelDownloadInfo objects representing the base information of multiple models.
     * @param { IModelManagerCallback } modelManagerCallbackProxy - A callback proxy for the model of type IModelManagerCallback.
     */
    modelDeleteLocal(modelDownloadInfos: Array<ModelDownloadInfo>, modelManagerCallbackProxy: IModelManagerCallback): void {
        HiAiLog.info(TAG, `modelDeleteLocal start`);
        ModelAgingManager.getInstance().deleteModel(modelDownloadInfos, modelManagerCallbackProxy);
    }


    /**
     * An synchronization function to subscribe to model.
     *
     * @param { string } modelType - The model type, including 'AI' and 'lora'.
     * @param { Array<ModelBaseInfo> } modelBaseInfos - An array of ModelDownloadInfo objects representing the base information of multiple models.
     * @param { IModelDownloadCallback } modelCallbackProxy - A callback proxy for the model of type IModelManagerCallback.
     */
    modelSubscribe(modelType: string, modelDownloadInfos: Array<ModelDownloadInfo>, modelManagerCallbackProxy?: IModelManagerCallback): void {
        HiAiLog.info(TAG, `modelSubscribe start`);
        if (!this.validateModelInfos(modelDownloadInfos, modelManagerCallbackProxy)) {
            return;
        }
        let bundleModelInfoArray = this.getPluginUpdateInfo(modelType, modelDownloadInfos);
        let callerInfo: update.PluginCallerInfo = {
            businessType: update.BusinessSubType.MODEL,
            isForegroundCall: false,
            allowedNetType:  update.NetType.WIFI
        };
        if (CeliaPrivacyVerification.getInstance().isAutomaticUpdatingAllowed()) {
            HiAiLog.info(TAG,`start check wlan status`);
            ModelUpgradeOta.getInstance().registerAutoUpgrade(bundleModelInfoArray, callerInfo).then((errorInfo: update.PluginErrorInfo)=>{
                HiAiLog.info(TAG, `modelSubscribe registerAutoUpgrade then`);
                if(errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) {
                    HiAiLog.info(TAG, `modelSubscribe registerAutoUpgrade success`); // register success
                    modelManagerCallbackProxy?.onResult(true, 'Model subscribe all success', null);
                } else{ // register failed
                    modelManagerCallbackProxy?.onResult(false, 'Some or all models fail to be subscribed.', null);
                }
            }).catch((error: BusinessError)=>{
                HiAiLog.error(TAG, `modelSubscribe registerAutoUpgrade on OTA catch error is: ` + JSON.stringify(error));
            })
        }
    }

    /**
     * An synchronization function to unsubscribe to model.
     *
     * @param { string } modelType - The model type, including 'AI' and 'lora'.
     * @param { Array<ModelDownloadInfo> } modelDownloadInfos - An array of ModelDownloadInfo objects representing the base information of multiple models.
     * @param { IModelManagerCallback } modelManagerCallbackProxy - A callback proxy for the model of type IModelManagerCallback.
     */
    modelUnSubscribe(modelType: string, modelDownloadInfos: Array<ModelDownloadInfo>, modelManagerCallbackProxy: IModelManagerCallback): void {
        HiAiLog.info(TAG, `modelUnSubscribe start`);
        if (!this.validateModelInfos(modelDownloadInfos, modelManagerCallbackProxy)) {
            return;
        }
        let bundleModelInfoArray = this.getPluginUpdateInfo(modelType, modelDownloadInfos);
        ModelUpgradeOta.getInstance().unRegisterAutoUpgrade(bundleModelInfoArray).then((errorInfo: update.PluginErrorInfo)=>{
            HiAiLog.info(TAG, `modelUnSubscribe unRegisterAutoUpgrade then`);
            if(errorInfo.errorCode === OtaResultCodeConstant.OTA_SUCCESS_RESULT) { // unRegister success
                modelManagerCallbackProxy.onResult(true, 'Model unSubscribe all success', null);
            } else{ // unRegister failed
                modelManagerCallbackProxy.onResult(false, 'Some or all models fail to be unSubscribed.', null);
            }
        }).catch((error: BusinessError)=>{
            HiAiLog.error(TAG, `modelUnSubscribe unRegisterAutoUpgrade on OTA catch error is: ` + JSON.stringify(error));
        })
    }


    /**
     * An synchronization function to subscribe to all model.
     *
     * @param { Array<ModelBaseInfo> } modelBaseInfos - An array of ModelDownloadInfo objects representing the base information of multiple models.
     */
    allModelSubscribe(modelDownloadInfos: Array<ModelDownloadInfo>): void {
        HiAiLog.info(TAG, `allModelSubscribe start`);
        let bundleModelInfoArray = this.getPluginUpdateInfo(ModelDomain.MODEL_TYPE_AI, modelDownloadInfos);
        let callerInfo: update.PluginCallerInfo = {
            businessType: update.BusinessSubType.MODEL,
            isForegroundCall: false,
            allowedNetType:  update.NetType.WIFI
        };

        ModelUpgradeOta.getInstance().registerAutoUpgrade(bundleModelInfoArray, callerInfo).then((errorInfo: update.PluginErrorInfo)=>{
            HiAiLog.info(TAG, `allModelSubscribe registerAutoUpgrade then errorInfo is `+ JSON.stringify(errorInfo));
        }).catch((error: BusinessError)=>{
            HiAiLog.error(TAG, `allModelSubscribe registerAutoUpgrade on OTA catch error is: ` + JSON.stringify(error));
        })
    }


    /**
     * An synchronization function to unsubscribe to model.
     *
     * @param { Array<ModelDownloadInfo> } modelDownloadInfos - An array of ModelDownloadInfo objects representing the base information of multiple models.
     */
    allModelUnSubscribe(modelDownloadInfos: Array<ModelDownloadInfo>): void {
        HiAiLog.info(TAG, `allModelUnSubscribe start`);
        let bundleModelInfoArray = this.getPluginUpdateInfo(ModelDomain.MODEL_TYPE_AI, modelDownloadInfos);

        ModelUpgradeOta.getInstance().unRegisterAutoUpgrade(bundleModelInfoArray).then((errorInfo: update.PluginErrorInfo)=>{
            HiAiLog.info(TAG, `allModelUnSubscribe unRegisterAutoUpgrade then errorInfo is `+ JSON.stringify(errorInfo));
        }).catch((error: BusinessError)=>{
            HiAiLog.error(TAG, `allModelUnSubscribe unRegisterAutoUpgrade on OTA catch error is: ` + JSON.stringify(error));
        })
    }


    private isIdsVersion(resVersion: string): boolean {
        let pattern = new RegExp("^0x\\d+");
        if (!pattern.test(resVersion)) {
            HiAiLog.info(TAG, `addModelDownloadTask queryModelCloud not idIdsVersion`);
            return false;
        }
        HiAiLog.info(TAG, `addModelDownloadTask queryModelCloud is idIdsVersion`);
        return true;
    }


    private getBundleInfos(modelBaseInfo: ModelBaseInfo): Array<update.PluginUpdateInfo> {
        let bundleModelInfos: Array<update.PluginUpdateInfo> = [];
        let bundleModelInfo: update.PluginUpdateInfo = modelBaseInfo.getModelUpdateInfo();
        bundleModelInfos.push(bundleModelInfo);
        return bundleModelInfos;
    }


    private getPluginUpdateInfo(modelType: string, modelDownloadInfos: Array<ModelDownloadInfo>): update.PluginUpdateInfo {
        let bundleInfo: update.PluginBundleInfo = {
            bundleName: CommonConstants.HIAI_PACKAGE_NAME,
            abilityName: CommonConstants.HIAI_ABILITY_NAME,
            packageName: modelDownloadInfos[0].domain
        }

        let modelInfos: Array<update.PluginInfo> = [];
        for(let modelDownloadInfo of modelDownloadInfos) {
            let modelInfo: update.PluginInfo = {
                pluginName: modelDownloadInfo.resId,
                compatibleVersion: modelDownloadInfo.resCompatibleVersion,
                currentPluginVersion: modelDownloadInfo.resVersion,
                pluginType: modelType,
                packageName: modelDownloadInfo.domain
            }
            modelInfos.push(modelInfo);
        }

        let modelUpdateInfo: update.PluginUpdateInfo = {
            bundleInfo: bundleInfo,
            pluginInfos: modelInfos,
            pluginCompatibleVersionCode: CommonConstants.EMPTY_STRING,
            executionPolicy: CommonConstants.EMPTY_STRING
        }
        HiAiLog.info(TAG, `getPluginUpdateInfo end`);
        return modelUpdateInfo;
    }


    private getModelInfo(modelBaseInfo: ModelBaseInfo,
      modelCallbackProxy: IModelDownloadCallback, modelDownloadConfig?: ModelDownloadConfig): ModelInfo {
      let modelInfo: ModelInfo = {
        modelBaseInfo: modelBaseInfo,
        modelCallbackProxy: modelCallbackProxy,
        modelDownloadStatus: {
          isExisted: false,
          isDownloadStart: false,
          isDownloadEnd: false,
          isDownloadSuccess: false
        },
        modelDownloadProgress: CommonConstants.DEFAULT_INT,
        isOTAPlatform: true,
        networkType: modelDownloadConfig.networkType
      }
      return modelInfo;
    }


    private isLatestVersion(currentVersion: string, cloudQueryVersion: string): boolean {
        let currentVersionCode: number = parseInt(currentVersion);
        let cloudQueryVersionCode: number = parseInt(cloudQueryVersion);
        HiAiLog.info(TAG, `isLatestVersion start currentVersionCode is: `  + currentVersionCode + ', cloudQueryVersionCode is:' + cloudQueryVersionCode);
        if(cloudQueryVersionCode > currentVersionCode) {
            return true;
        }
        return false;
    }

    private validateModelInfos(modelDownloadInfos: Array<ModelDownloadInfo>,
        modelManagerCallbackProxy?: IModelManagerCallback): boolean {
        for (const modelInfo of modelDownloadInfos) {
            if (!modelInfo?.domain || !modelInfo?.resId || !modelInfo?.resCompatibleVersion) {
                HiAiLog.error(TAG, `Invalid model info: domain=${modelInfo?.domain}, resId=${modelInfo?.resId}`);
                if (modelManagerCallbackProxy) {
                    modelManagerCallbackProxy.onResult(false, 'Invalid model parameters: Required fields are missing',
                        [this.createErrorResult(modelInfo, ModelDownloadResultCode.PARAMATER_INVALID,
                            'Invalid model parameters: Required fields are missing')]);
                }
                return false;
            }
        }
        return true;
    }

    /**
     * 创建错误结果对象。
     */
    private createErrorResult(modelInfo: ModelDownloadInfo, errorCode: ModelDownloadResultCode, errorMessage: string): ResultInfo {
        const resultInfo = new ResultInfo();
        resultInfo.resId = modelInfo?.resId || CommonConstants.EMPTY_STRING;
        resultInfo.domain = modelInfo?.domain || CommonConstants.EMPTY_STRING;
        resultInfo.resCompatibleVersion = modelInfo?.resCompatibleVersion || CommonConstants.EMPTY_STRING;
        resultInfo.resVersion = modelInfo?.resVersion || CommonConstants.EMPTY_STRING;
        resultInfo.errorCode = errorCode;
        resultInfo.errorMessage = errorMessage;
        return resultInfo;
    }

    /**
     * Handles the completion of a foreground task.
     *
     * @param { string } modelName - The name of the model associated with the foreground task.
     * @returns { void } No return value.
     */
    public static handleForegroundTaskComplete(modelName: string): void {
        if (ModelTaskManager.activeForegroundTasks.has(modelName)) {
            ModelTaskManager.instance.onForegroundTaskComplete(modelName);
        }
    }
}

