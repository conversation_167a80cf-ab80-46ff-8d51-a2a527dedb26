/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import DownloadWindowManager from '../DownloadWindowManager';
import WindowConstants from '../../../utils/constants/WindowConstants'
import CommonConstants from '../../../utils/constants/CommonConstants';
import HiAIServiceAbility from '../../../../framework/HiAIServiceAbility';
import HiA<PERSON>Log from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import prompt from '@ohos.promptAction'

const TAG: string = 'ExceptionToast'

/**
 * The exception toast window page.
 *
 */

@Entry
@Component
struct Index {
  /**
   * Automatically display a dialog box and obtain the image when the page is started.
   */
  async aboutToAppear() {
    HiAiLog.info(TAG, `index page toast about to appear.`);
    let toastMessage: string | undefined = HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('Network_exception');
    HiAiLog.info(TAG, `toastMessage is ` + toastMessage);

    prompt.showToast({
      message: toastMessage,
      duration: 2500
    });
    let timeId = setTimeout(() => {
      HiAiLog.info(TAG, `exception toast end.`);
      DownloadWindowManager.destroyWindow(WindowConstants.TOAST_WINDOW_NAME);
      clearTimeout(timeId);
      }, CommonConstants.TOAST_DELAY);
  }

  build() {
    Row() {
      Column() {
      }
      .width('100%')
    }
    .backgroundColor($r('app.color.toast_background_color'))
    .height('100%')
  }
}