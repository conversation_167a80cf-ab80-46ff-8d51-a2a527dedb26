/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { ModelDownloadReportUtil } from '../../../utils/ModelManagerReportUtil';
import HiAIServiceAbility from '../../../../framework/HiAIServiceAbility';
import ModelDownloadCapability from '../../../ModelDownloadCapability';
import DownloadWindowManager from '../DownloadWindowManager';
import ModelDownloadManager from '../../ModelTaskManager';
import { NetWorkUtil } from '@hms-ai/pdkfull/src/main/ets/utils/NetWorkUtil';
import { ModelDownloadResultCode, ReportResultCode, ReportResultCodeMessage, } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';

const TAG: string = 'DownloadNotificationDialog'

/**
 * The builder of User authorization dialog box.
 */
@CustomDialog
export struct DownloadNotificationDialog {
  @Link moduleName: string;
  @Link moduleType: number;
  @Link dialogWidth: number;
  @Link windowName: string;
  controller: CustomDialogController;
  openDownloadDialog?: ()=>void

  build() {
    Column() {
      Text(this.moduleType? $r('app.string.ModelDownload_Language_Model'): $r('app.string.ModelDownload_component'))
        .padding({ top: 15, bottom: 15, left: 24, right: 24 })
        .width('100%')
        .textAlign(TextAlign.Center)
        .fontWeight(FontWeight.Bold)
        .fontSize($r('sys.float.Title_S'))
        .fontColor($r('sys.color.font_primary'))
        .maxFontScale(2)

      Text(HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('ModelDownload_curent_operation', this.moduleName))
        .padding({ left: 24, right: 24 })
        .width('100%')
        .textAlign(TextAlign.JUSTIFY)
        .fontWeight(FontWeight.Regular)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_primary'))
        .lineHeight(19)
        .textAlign(TextAlign.Start)
        .textOverflow({overflow: TextOverflow.Ellipsis})
        .maxLines(10)

      Flex({ justifyContent: FlexAlign.SpaceAround}) {
        Button($r('app.string.ModelDownload_cancel'))
          .width('50%')
          .height('100%')
          .fontSize($r('sys.float.Body_L'))
          .fontColor($r('sys.color.font_emphasize'))
          .fontWeight(FontWeight.Medium)
          .buttonStyle(ButtonStyleMode.TEXTUAL)
          .onClick(async () => {
            HiAiLog.info(TAG, `取消 is called.`);
            ModelDownloadCapability.hasForeDownload = false;
            ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
              ReportResultCode.DOWNLOAD_NOT_ALLOWED, ReportResultCodeMessage.DOWNLOAD_NOT_ALLOWED,
              ModelDownloadCapability.foreDownloadStartTime.getTime());
            ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);
            await DownloadWindowManager.destroyWindow(this.windowName);
            ModelDownloadCapability.foreDownloadCallbackProxy.onError(this.windowName,
              ModelDownloadResultCode.DOWNLOAD_NOT_ALLOWED, ReportResultCodeMessage.DOWNLOAD_NOT_ALLOWED);
          })

        Button($r('app.string.ModelDownload_download'))
          .width('50%')
          .height('100%')
          .fontSize($r('sys.float.Body_L'))
          .fontColor($r('sys.color.font_emphasize'))
          .fontWeight(FontWeight.Medium)
          .buttonStyle(ButtonStyleMode.TEXTUAL)
          .onClick(async () => {
            HiAiLog.info(TAG, `下载 is called.`)
            this.controller.close();
            if (NetWorkUtil.isConnectNetwork()) {
              ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
                ReportResultCode.DOWNLOAD_ALLOWED, ReportResultCodeMessage.DOWNLOAD_ALLOWED,
                ModelDownloadCapability.foreDownloadStartTime.getTime());
              ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);

              ModelDownloadManager.getInstance().addModelDownloadTask(ModelDownloadCapability.foreModelBaseInfo,
                ModelDownloadCapability.foreModelDownloadInfo, ModelDownloadCapability.foreDownloadCallbackProxy,
                ModelDownloadCapability.modelDownloadConfig);
              this.openDownloadDialog!();
            } else {
              ModelDownloadCapability.foreDownloadCallbackProxy.onError(this.windowName,
                ModelDownloadResultCode.NO_NETWORK_STATUS, ReportResultCodeMessage.NO_NETWORK_STATUS);
            }
          })
      }
      .margin({ left: 16, right: 16})
      .height(64)
    }
    .width(this.dialogWidth)
    .borderRadius($r('sys.float.ohos_id_corner_radius_dialog'))
    .backgroundColor($r('app.color.dialog_background_color'))
  }
}