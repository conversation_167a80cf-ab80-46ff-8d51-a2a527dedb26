/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { ScreenChangeUtil } from '../../utils/ScreenChangeUtil';
import HiAIServiceAbility from '../../../framework/HiAIServiceAbility';
import BackgroundNotification from './pages/BackgroundNotification';
import WindowConstants from '../../utils/constants/WindowConstants';
import CommonConstants from '../../utils/constants/CommonConstants';
import ModelInfoManager from '../modelDownloadInfo/ModelInfoManager';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { ModelDownloadResultCode, ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { image } from '@kit.ImageKit';
import { display } from '@kit.ArkUI';
import window from '@ohos.window';
import { JSON } from '@kit.ArkTS';
import ModelDownloadCapability from '../../ModelDownloadCapability';
import { NetworkChangeUtil } from '../../utils/NetworkChangeUtil';

const TAG = 'DownloadWindowManager';

/**
 * The class for downloading window management.
 * @class
 * <AUTHOR>
 */
export default class DownloadWindowManager {
    /**
     * Subscription status of the background live video pane.
     */
    public static isSubscribed: boolean = false;

    /**
     * Initial width of the window.
     */
    static captionCurrentWidth: number = CommonConstants.DEFAULT_INT;

    /**
     * Initial height of the window.
     */
    static captionCurrentHeight: number = CommonConstants.DEFAULT_INT;

    /**
     * The model name to be displayed in the window.
     */
    public static foreModuleName: string = null;

    /**
     * Type of the downloaded model package, including resource package and language package.
     */
    public static foreModuleType: number = CommonConstants.DEFAULT_INT;

    /**
     * The window name, which must be the same as the model ID.
     */
    public static windowName: string = null;

    /**
     * Image object of the Cancel button
     */
    public static imagePixelMap: image.PixelMap;

    /**
     * Create and display a window.
     *
     * @param { string } windowName - the window name.
     * @param { string } pageUrl - the window path to display.
     */
    static createWindow(windowName: string, pageUrl: string): void {
        HiAiLog.info(TAG, "createWindow name is " + windowName);

        let config: window.Configuration = {
            name: windowName,
            windowType: window.WindowType.TYPE_FLOAT,
            ctx: HiAIServiceAbility.globalContext
        }

        window.createWindow(config).then(windowObj => {
            HiAiLog.info(TAG, `createWindow success`);
            windowObj.setUIContent(pageUrl).then(() => {
                windowObj.setWindowBackgroundColor('#00000000');
                if(windowName === WindowConstants.TOAST_WINDOW_NAME) {
                    let displayInfo = display.getDefaultDisplaySync();
                    windowObj.resize(displayInfo.width, displayInfo.height).then(()=>{
                        HiAiLog.info(TAG, `window resize`);
                        windowObj.showWindow();
                    })
                } else {
                    HiAiLog.info(TAG, `createWindow success111`);
                    windowObj.on('touchOutside', ()=> {
                        HiAiLog.info(TAG, `window touchOutside`);
                        DownloadWindowManager.swipeUpListen(windowName);
                    });
                    windowObj.showWindow();
                }
            }).catch((error) => {
                HiAiLog.error(TAG, `setUIContent . Cause: ${JSON.stringify(error)}`);
            })
        }).catch((err) => {
            HiAiLog.error(TAG, `Failed to create the subWindow. Cause: ${JSON.stringify(err)}`);
        });
    }

    /**
     * Returns a window object based on the window name.
     *
     * @param { string } windowName - the window name.
     * @returns  { window.Window | undefined } a window object.
     */
    static getWindow(windowName: string): window.Window | undefined {
        let windowObj: window.Window | undefined = undefined;
        try {
            windowObj = window.findWindow(windowName);
        } catch (error) {
            HiAiLog.error(TAG, `findWindow error is: ` + JSON.stringify(error));
        } finally {
            return windowObj;
        }
    }

    /**
     * Destroy Window.
     *
     * @param  { string } windowName - the window name.
     */
    static async destroyWindow(windowName: string): Promise<void> {
        HiAiLog.info(TAG, `destroyWindow name is: ` + windowName);
        DownloadWindowManager.getWindow(windowName)?.destroyWindow(() => {
            HiAiLog.info(TAG, `destroyWindow success. name is: ` + windowName);
            if(windowName !== WindowConstants.TOAST_WINDOW_NAME) {
                ScreenChangeUtil.getInstance().unregisterScreenChange();
                let isExist: boolean = ModelInfoManager.getIsExistedStatus(windowName);
                if(!isExist) {
                    ModelInfoManager.deleteModelInfo(windowName);
                }
            }
        })
        return;
    }

    /**
     * Return to Desktop Gesture Listening.
     *
     * @param  { string } windowName - the window name.
     */
    static async swipeUpListen(windowName: string): Promise<void> {
        HiAiLog.info(TAG, 'swipeUpListen start');
        DownloadWindowManager.destroyWindow(windowName);
        ModelDownloadCapability.hasForeDownload = false;
        let isStartDownload = ModelInfoManager.getModelInfo(windowName)?.modelDownloadStatus.isDownloadStart;
        let downloadCallbackProxy: IModelDownloadCallback =
            await ModelInfoManager.getCallbackProxy(windowName) || ModelDownloadCapability.foreDownloadCallbackProxy;
        HiAiLog.info(TAG, `swipeUpListen, isStartDownload is: ` + isStartDownload);
        if(isStartDownload) {
            downloadCallbackProxy?.onError(windowName, ModelDownloadResultCode.DOWNLOAD_BACKGROUND, ReportResultCodeMessage.DOWNLOAD_BACKGROUND);
            await BackgroundNotification.publish(windowName);
        } else {
            NetworkChangeUtil.getInstance().unregisterNetworkChange();
            downloadCallbackProxy?.onError(windowName, ModelDownloadResultCode.DOWNLOAD_BACK_TO_DESKTOP, ReportResultCodeMessage.DOWNLOAD_BACK_TO_DESKTOP);
        }
        return;
    }
}