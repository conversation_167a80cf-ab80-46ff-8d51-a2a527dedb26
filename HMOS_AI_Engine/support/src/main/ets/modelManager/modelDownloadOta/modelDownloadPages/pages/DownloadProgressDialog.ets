/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { ModelDownloadReportUtil } from '../../../utils/ModelManagerReportUtil';
import DownloadWindowManager from '../DownloadWindowManager';
import ModelInfoManager from '../../modelDownloadInfo/ModelInfoManager';
import ModelDownloadManager from '../../ModelTaskManager';
import HiAIServiceAbility from '../../../../framework/HiAIServiceAbility';
import { ReportResultCode, ReportResultCodeMessage} from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import ModelDownloadCapability from '../../../ModelDownloadCapability';
import mediaQuery from '@ohos.mediaquery';

const TAG: string = 'DownloadProgressDialog'

/**
 * The builder of Download progress dialog box.
 */
@CustomDialog
export struct DownloadProgressDialog {
  controller: CustomDialogController
  cancelDownload?: () => void
  publish?: () => Promise<void>
  @Link progress: number
  @Link moduleName: string
  @Link packageSize: number
  @Link pkgSize: number
  @Link dialogWidth: number;
  @Link progressTextWidth: number;
  @Link windowName: string;
  @State isDarkMode: boolean = false;

  listener: mediaQuery.MediaQueryListener = mediaQuery.matchMediaSync('(dark-mode: true)');

  aboutToAppear() {
    this.isDarkMode = this.listener.matches;
    this.listener.on('change', (mediaQueryResult: mediaQuery.MediaQueryResult) => {
      this.isDarkMode = mediaQueryResult.matches;
    });
  }

  aboutToDisappear() {
    this.listener.off('change');
  }

  build() {
    Column() {
      Row({space:12}) {
        Text( HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('ModelDownload_downloading',
          this.moduleName, this.packageSize.toString() + 'M'))
          .width(this.progressTextWidth)
          .fontWeight(FontWeight.Regular)
          .fontSize(14)
          .fontColor($r('sys.color.font_primary'))
          .lineHeight(19)

        Text(`${Math.round(this.progress)}%`)
          .width(48)
          .textAlign(TextAlign.End)
          .fontWeight(FontWeight.Regular)
          .fontSize(14)
          .fontColor($r('sys.color.font_secondary'))
      }
      .width('100%')

      Row({space:12}) {
        Progress({value: this.progress, total: this.pkgSize, type: ProgressType.Linear})
          .style({strokeWidth: 4, enableSmoothEffect: true})
          .width(this.dialogWidth-84)

        Column() {
          Image(this.isDarkMode ? $r('app.media.ic_public_cancel_progress_dark') : $r('app.media.ic_public_cancel_progress'))
            .width(24)
            .objectFit(ImageFit.Contain)
            .accessibilityLevel('no')
        }
        .onClick(async () => {
          let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(this.windowName);
          downloadCallbackProxy.onCancel(this.windowName);
          ModelDownloadManager.getInstance().deleteModelDownloadTask(this.windowName);

          ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
            ReportResultCode.DOWNLOAD_CANCELED, ReportResultCodeMessage.DOWNLOAD_CANCELED,
            ModelDownloadCapability.foreDownloadStartTime.getTime());
          ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);

          ModelDownloadCapability.hasForeDownload = false;
          this.controller.close();
          let timer = setTimeout(() => {
            clearTimeout(timer);
            this.cancelDownload!();
          },300)
        })
        .accessibilityText(HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('ModelDownload_cancel_button'))
        .id('点击取消下载')
      }
      .width('100%')
      .height(24)
    }
    .width(this.dialogWidth)
    .borderRadius($r('sys.float.ohos_id_corner_radius_dialog'))
    .backgroundColor($r('app.color.dialog_background_color'))
    .padding({ top: 24, left: 24, right: 24, bottom: 24 })
    .gesture(
      SwipeGesture({direction: SwipeDirection.All})
        .onAction(async ()=>{
          this.controller.close();
          await DownloadWindowManager.destroyWindow(this.windowName);
          HiAiLog.info(TAG, `DownloadProgress publish`)
          await this.publish!();
        })
    )
  }
}