/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { ModelDownloadReportUtil } from '../../../utils/ModelManagerReportUtil';
import HiAIServiceAbility from '../../../../framework/HiAIServiceAbility';
import CommonConstants from '../../../utils/constants/CommonConstants';
import DownloadWindowManager from '../DownloadWindowManager';
import ModelInfoManager from '../../modelDownloadInfo/ModelInfoManager';
import ModelDownloadManager from '../../ModelTaskManager';
import { ReportResultCode, ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import Hi<PERSON><PERSON>Log from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { BusinessError } from '@kit.BasicServicesKit';
import { notificationManager } from '@kit.NotificationKit';
import notificationSubscribe from '@ohos.notificationSubscribe';
import ModelDownloadCapability from '../../../ModelDownloadCapability';

const TAG: string = "BackgroundNotification"

/**
 * The class of background live video notification management.
 * @class
 * <AUTHOR>
 */
export default class BackgroundNotification {

  private static notificationIds: Map<string, number> = new Map();

  /**
   * Allocates a unique notification ID for a given resource ID.
   *
   * @param { string } resId - The model ID for which to allocate a notification ID.
   * @returns { number } The unique notification ID assigned to the resource.
   */
  private static getNotificationId(resId: string): number {
    if (!this.notificationIds.has(resId)) {
      const newId = CommonConstants.BASE_ID + this.notificationIds.size;
      this.notificationIds.set(resId, newId);
    }
    return this.notificationIds.get(resId);
  }

  /**
   * remove notification ID
   *
   * @param { string } resId - The model ID for which to allocate a notification ID.
   */
  private static removeNotificationId(resId: string): void {
    this.notificationIds.delete(resId);
  }

  /**
   * Subscribe to a Live Video Window.
   *
   * @param { BackgroundNotification.responseCallback } responseCallback - the response callback of subscribe to the background live video window.
   * @param { BackgroundNotification.cancelCallback } cancelCallback - the cancel callback of subscribe to the background live video window.
   */
  static async subscribeNotification(responseCallback: (id: number, opt: notificationManager.ButtonOptions)=> void,
    cancelCallback: (data:notificationSubscribe.SubscribeCallbackData) => void): Promise<void> {
    let systemLiveViewSubscriber: notificationManager.SystemLiveViewSubscriber  = {
      onResponse: responseCallback
    };
    try {
      notificationManager.subscribeSystemLiveView(systemLiveViewSubscriber);
      HiAiLog.info(TAG, `subscribe success.`)
    } catch (e) {
      let err = e as BusinessError
      HiAiLog.error(TAG, `subs failed: ${err.code}, ${err.message}.`)
    }

    let subscriber:notificationSubscribe.NotificationSubscriber = {
      onConsume: (data:notificationSubscribe.SubscribeCallbackData) => {
        let req = data.request;
        HiAiLog.info(TAG, `notificationSubscribe onConsume callback. req.id: ${req.id}`);
      },
      onCancel: cancelCallback,
      onUpdate: (data) => {
        HiAiLog.info(TAG, `notificationSubscribe onUpdate callback. req.id: ${data.sortedHashCode}`);
      },
      onConnect: () => {
        HiAiLog.info(TAG, `notificationSubscribe onConnect callback.}`);
      },
      onDisconnect: () => {
        HiAiLog.info(TAG, `notificationSubscribe onDisconnect callback.}`);
      },
      onDestroy: () => {
        HiAiLog.info(TAG, `notificationSubscribe onDestroy callback.}`);
      },
    };
    await notificationSubscribe.subscribe(subscriber).then(()=>{
      HiAiLog.info(TAG, `subscribe notification success`)
    }).catch((error: BusinessError) => {
      HiAiLog.error(TAG, `subscribe notificaiton failed error is: ` + JSON.stringify(error))
    });
  }

  /**
   * The response callback of Live Video Window.
   *
   * @param { BackgroundNotification.responseCallback } responseCallback - the response callback of subscribe to the background live video window.
   * @param { BackgroundNotification.cancelCallback } cancelCallback - the cancel callback of subscribe to the background live video window.
   */
  static async responseCallback(id: number, opt: notificationManager.ButtonOptions): Promise<void> {
    HiAiLog.info(TAG, `onresponse called, id: ${id}, opt: ${JSON.stringify(opt)}`);
    let resId: string = null;
    for (const [modelId, notificationId] of BackgroundNotification.notificationIds) {
      if (notificationId === id) {
        resId = modelId;
        break;
      }
    }
    if (!resId) {
      HiAiLog.error(TAG, `Cannot find model for notification id: ${id}`);
      return;
    }
    notificationManager.cancel(id).then(async ()=>{
      HiAiLog.info(TAG, `close notification success!`);
      DownloadWindowManager.isSubscribed = false;

      ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
        ReportResultCode.DOWNLOAD_CANCELED, ReportResultCodeMessage.DOWNLOAD_CANCELED,
        ModelDownloadCapability.foreDownloadStartTime.getTime());
      ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);

      let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(resId);
      downloadCallbackProxy.onCancel(resId);
      ModelDownloadCapability.hasForeDownload = false;

      BackgroundNotification.removeNotificationId(resId);
      ModelDownloadManager.getInstance().deleteModelDownloadTask(resId);
    }).catch((e:BusinessError) => {
      HiAiLog.error(TAG, `close notification failed: ${e.message}, ${e.code}`);
    });
  }

  /**
   * The cancel callback of the background live video window.
   *
   * @param { notificationSubscribe.SubscribeCallbackData } data - the subscribe callback data to the background live video window.
   */
  static async cancelCallback(data: notificationSubscribe.SubscribeCallbackData): Promise<void> {
    HiAiLog.info(TAG, `oncancel called, data: ${JSON.stringify(data)}.`);
  }

  /**
   * Publish background notification firstly.
   *
   * @param { string } resId - ID of the model being downloaded.
   */
  static async publish(resId: string): Promise<void> {
    HiAiLog.info(TAG, `Start publish Window for ${resId}`);
    ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
      ReportResultCode.DOWNLOAD_BACKGROUND, ReportResultCodeMessage.DOWNLOAD_BACKGROUND,
      ModelDownloadCapability.foreDownloadStartTime.getTime());
    ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);

    if (!DownloadWindowManager.isSubscribed) {
      await BackgroundNotification.subscribeNotification(BackgroundNotification.responseCallback,
        BackgroundNotification.cancelCallback);
      DownloadWindowManager.isSubscribed = !DownloadWindowManager.isSubscribed;
    }
    HiAiLog.info(TAG, `background active window notification`);
    let progress: number = ModelInfoManager.getDownloadProgress(resId);
    await BackgroundNotification.publishNotification(resId, progress, DownloadWindowManager.foreModuleName);
  }

  /**
   * Publish notification of the background live video window.
   *
   * @param { string } resId - ID of the model being downloaded.
   * @param { number } progress - the download progress of the model being downloaded.
   * @param { string } modelName - the model name show in the background live video window.
   */
  static async publishNotification(resId: string, progress: number, modelName: string): Promise<void> {
    HiAiLog.info(TAG, `begin to publish notification for ${resId}`);
    const notificationId = this.getNotificationId(resId);

    let serviceModelDownload: string = HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('Service_ModelDownload');
    let notificationRequest: notificationManager.NotificationRequest = {
      notificationSlotType: notificationManager.SlotType.LIVE_VIEW,
      id: notificationId,
      content: {
        notificationContentType : notificationManager.ContentType.NOTIFICATION_CONTENT_SYSTEM_LIVE_VIEW,
        systemLiveView: {
          title: HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('Title_ModelDownload_downloading'),
          text: (progress !== CommonConstants.MAX_PROGRESS) ? `${modelName}: ${Math.round(progress)}%` : HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('ModelDownload_downloaded'),
          typeCode: 8,
          button: {
            names: ["cancelDownload"],
            icons: [DownloadWindowManager.imagePixelMap],
          },
          progress: {
            maxValue: CommonConstants.MAX_PROGRESS,
            currentValue: progress,
            isPercentage: true,
          },
        }
      },
      extraInfo: {
        hw_button_accessibility_text: [HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('ModelDownload_cancel_button')],
        hw_customer_accessibility_text: serviceModelDownload
      }
    };

    let start = new Date().getTime()
    notificationManager.publish(notificationRequest).then(()=>{
      HiAiLog.info(TAG, `notification publish success for ${resId}!, cost time: ${new Date().getTime() - start} ms.`);
      let isDownloadEnd: boolean = ModelInfoManager.getIsDownloadEndStatus(resId);
      if(isDownloadEnd || progress === CommonConstants.MAX_PROGRESS) {
        let timeId = setTimeout(() => {
          clearTimeout(timeId);
          notificationManager.cancel(notificationId).then(()=>{
            BackgroundNotification.removeNotificationId(resId);
            HiAiLog.info(TAG, `downloaded, close notification success for ${resId}!`)
          }).catch((e:BusinessError) => {
            HiAiLog.error(TAG, `downloaded, close notification failed: ${e.message}, ${e.code}`)
          });
        }, CommonConstants.DOWNLOADED_DELAY)
      }
    }).catch((e:BusinessError) => {
      HiAiLog.error(TAG, `publish failed for ${resId}: ${e.message}, ${e.code}`)
    });
  }
}


