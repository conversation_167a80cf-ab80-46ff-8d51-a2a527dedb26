/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { NetworkChangeUtil } from '../../../utils/NetworkChangeUtil';
import { DownloadNotificationDialog } from './DownloadNotificationDialog';
import { DownloadProgressDialog } from './DownloadProgressDialog';
import { ScreenChangeUtil } from '../../../utils/ScreenChangeUtil';
import BackgroundNotification from './BackgroundNotification';
import DownloadWindowManager from '../DownloadWindowManager';
import CommonConstants from '../../../utils/constants/CommonConstants';
import HiAIServiceAbility from '../../../../framework/HiAIServiceAbility';
import WindowConstants from '../../../utils/constants/WindowConstants';
import ModelInfoManager from '../../modelDownloadInfo/ModelInfoManager';
import ModelDownloadManager from '../../ModelTaskManager';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { NetWorkUtil } from '@hms-ai/pdkfull/src/main/ets/utils/NetWorkUtil';
import { ModelDownloadResultCode,
  ReportResultCode,
  ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { image } from '@kit.ImageKit';
import ModelDownloadCapability from '../../../ModelDownloadCapability';
import { ModelDownloadReportUtil } from '../../../utils/ModelManagerReportUtil';

const TAG: string = 'ModelDownloadWindow'

/**
 * The model download window page.
 *
 */

@Entry
@Component
struct Index {
  @State moduleName: string = DownloadWindowManager.foreModuleName;
  @State moduleType: number = DownloadWindowManager.foreModuleType;
  @State windowName: string = DownloadWindowManager.windowName;
  @State progress: number = CommonConstants.DEFAULT_INT;
  @State pkgSize: number = CommonConstants.MAX_PROGRESS;
  @State packageSize: number = CommonConstants.DEFAULT_INT;
  @State timer: number = CommonConstants.DEFAULT_INT;
  @State isSubscribed: boolean = false;
  @State isStartProgress: boolean = true;
  @State lastProgressUpdateTime: number = Date.now();
  @StorageLink('dialogWidth') dialogWidth: number = ScreenChangeUtil.dialogWidth;
  @StorageLink('progressTextWidth') progressTextWidth: number = ScreenChangeUtil.progressTextWidth;
  /**
   * The User authorization dialog box.
   */
  notificationDialogController: CustomDialogController = new CustomDialogController({
    builder: DownloadNotificationDialog({
      moduleName: this.moduleName,
      moduleType: this.moduleType,
      dialogWidth: this.dialogWidth,
      windowName: this.windowName,
      openDownloadDialog: ()=>{
        HiAiLog.info(TAG, `request window open`);
        this.isStartProgress = true;
        this.progress = CommonConstants.DEFAULT_INT;
        this.startDownload(this.windowName);
      }
    }),
    autoCancel: false,
    customStyle: true,
    cancel: async ()=>{
      HiAiLog.info(TAG, `request window cancel`);
        let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(this.windowName) ||
        ModelDownloadCapability.foreDownloadCallbackProxy;
        downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.DOWNLOAD_NOT_ALLOWED,
            'The user returns to the previous page.');
      await DownloadWindowManager.destroyWindow(this.windowName);
    }
  })

  /**
   * The Download progress dialog box.
   */
  progressDialogController: CustomDialogController = new CustomDialogController({
    builder: DownloadProgressDialog({
      progress: this.progress,
      moduleName: this.moduleName,
      dialogWidth: this.dialogWidth,
      progressTextWidth: this.progressTextWidth,
      packageSize: this.packageSize,
      pkgSize: this.pkgSize,
      windowName: this.windowName,
      cancelDownload: () => {
        HiAiLog.info(TAG, `download window close, download model cancel.`);
        this.cancelDownload();
      },
      publish: async () => {
        HiAiLog.info(TAG, `download window publish`);
        await BackgroundNotification.publish(this.windowName);
      }
    }),
    autoCancel: false,
    customStyle: true,
    cancel: async ()=>{
      HiAiLog.info(TAG, `download window cancel, window name is: ` + this.windowName)
      await DownloadWindowManager.destroyWindow(this.windowName);
      let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(this.windowName);
      HiAiLog.info(TAG, `download window cancel`)
      downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.DOWNLOAD_BACKGROUND,
        ReportResultCodeMessage.DOWNLOAD_BACKGROUND);
      await BackgroundNotification.publish(this.windowName);
    }
  })

  /**
   * Automatically display a dialog box and obtain the image when the page is started.
   */
  aboutToAppear() {
    HiAiLog.info(TAG, `index page about to appear.`);
    this.notificationDialogController.open();
    this.getBackgroundImage();
  }

  /**
   * Get Background Notification Image PixelMap.
   */
  getBackgroundImage() {
    HiAiLog.info(TAG, `get Background Notification Image PixelMap.`);
    HiAIServiceAbility.globalContext?.resourceManager.getMediaContent($r('app.media.ic_public_cancel_background')).then((data) => {
      let arrayBuffer = data.buffer.slice(data.byteOffset, data.byteLength + data.byteOffset)
      let imageSource: image.ImageSource = image.createImageSource(arrayBuffer);
      imageSource.getImageInfo((err, value) => {
        if (err) {
          HiAiLog.info(TAG, `get Image Info error is: ` + JSON.stringify(err));
          return;
        }
        let opts: image.DecodingOptions = {
          editable: true,
          desiredSize: {
            height: value.size.height,
            width: value.size.width
          }
        };
        imageSource.createPixelMap(opts, (err, pixelMap) => {
          if (err) {
            HiAiLog.info(TAG, `create PixelMap error is: ` + JSON.stringify(err));
            return;
          }
          DownloadWindowManager.imagePixelMap = pixelMap;
          imageSource.release();
          HiAiLog.info(TAG, 'pixelMap is success');
        });
      })
    });
  }

  /**
   * Open the downloading pop-up window, use setInterval to set the interval, and display the interval.
   *
   * @param { string } windowName - the window name.
   */
  startDownload(windowName: string) {
    HiAiLog.info(TAG, 'download window StartDownload');
    this.timer = setInterval(async () => {
      let isDownloadEnd: boolean = ModelInfoManager.getIsDownloadEndStatus(windowName);
      HiAiLog.info(TAG, 'download window notification isDownloadEnd is: ' + isDownloadEnd);

      if (DownloadWindowManager.isSubscribed){
        BackgroundNotification.publishNotification(windowName, this.progress, this.moduleName);
      }

      if (this.progress === this.pkgSize || isDownloadEnd || !NetWorkUtil.isAvailableNetwork()) {
        HiAiLog.info(TAG, 'download window clearInterval timer is： ' + this.timer);
        clearInterval(this.timer);
        ModelDownloadCapability.hasForeDownload = false;
        DownloadWindowManager.destroyWindow(windowName);
        if(!NetWorkUtil.isAvailableNetwork() && !NetworkChangeUtil.networkExceptionToast) {
          NetworkChangeUtil.networkExceptionToast = true;
          let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(windowName);
          downloadCallbackProxy.onError(windowName, ModelDownloadResultCode.NO_NETWORK_STATUS,
            ReportResultCodeMessage.NO_NETWORK_STATUS);
          ModelDownloadManager.getInstance().deleteModelDownloadTask(windowName);
          DownloadWindowManager.createWindow(WindowConstants.TOAST_WINDOW_NAME, WindowConstants.TOAST_WINDOW_PATH);

          ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
            ReportResultCode.NO_NETWORK_STATUS, ReportResultCodeMessage.NO_NETWORK_STATUS,
            ModelDownloadCapability.foreDownloadStartTime.getTime());
          ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);
        }
      }

      let isExisted = ModelInfoManager.getIsExistedStatus(windowName);
      if(isExisted && this.isStartProgress) {
        this.isStartProgress = false;
        this.packageSize = ModelInfoManager.getModelSize(windowName);
        this.progressDialogController.open();
      }

      let currentProgress = ModelInfoManager.getDownloadProgress(this.windowName);
      if (currentProgress !== this.progress) {
        this.progress = currentProgress;
        this.lastProgressUpdateTime = Date.now();
      } else if (Date.now() - this.lastProgressUpdateTime > CommonConstants.NO_NETWORK_TIMEOUT) {
        HiAiLog.warn(TAG, 'Download progress has not updated for 1 minute, closing window');
        clearInterval(this.timer);
        ModelDownloadCapability.hasForeDownload = false;
        this.progressDialogController.close();
        ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
          ReportResultCode.DOWNLOAD_CANCELED, ReportResultCodeMessage.DOWNLOAD_CANCELED,
          ModelDownloadCapability.foreDownloadStartTime.getTime());
        ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);
        ModelDownloadCapability.hasForeDownload = false;
        let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(this.windowName);
        downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.DOWNLOAD_TIME_OUT,
          'Download progress has not updated for 1 minute');
        DownloadWindowManager.destroyWindow(this.windowName);
        ModelDownloadManager.getInstance().deleteModelDownloadTask(this.windowName);
      }
      HiAiLog.info(TAG, 'download window notification progress: ' + this.progress);
    },1000)
  }

  /**
   * Cancel the interval set by setInterval and close the dialog box.
   */
  async cancelDownload() {
    HiAiLog.warn(TAG, `download is canceled.`)
    this.progress = CommonConstants.DEFAULT_INT;
    clearInterval(this.timer);
    await DownloadWindowManager.destroyWindow(this.windowName);
  }

  build() {
    Row() {
      Column() {
      }
      .width('100%')
    }
    .backgroundColor('#00000000')
    .height('100%')
  }
}



