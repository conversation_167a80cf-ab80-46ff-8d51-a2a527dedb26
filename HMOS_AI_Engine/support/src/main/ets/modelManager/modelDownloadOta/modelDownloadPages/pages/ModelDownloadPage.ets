/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import {
    ModelDownloadResultCode,
    ReportResultCode,
    ReportResultCodeMessage
} from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import UIExtensionContentSession from '@ohos.app.ability.UIExtensionContentSession';
import { NetWorkUtil } from '@hms-ai/pdkfull/src/main/ets/utils/NetWorkUtil';
import ModelDownloadCapability from '../../../ModelDownloadCapability';
import { ModelDownloadReportUtil } from '../../../utils/ModelManagerReportUtil';
import ModelDownloadManager from '../../ModelTaskManager';
import CommonConstants from '../../../utils/constants/CommonConstants';
import ModelInfoManager from '../../modelDownloadInfo/ModelInfoManager';
import { ModelDownloadNotificationDialog } from './ModelDownloadNotificationDialog'
import { ModelDownloadProgressDialog } from './ModelDownloadProgressDialog';
import { ScreenChangeUtil } from '../../../utils/ScreenChangeUtil';
import DownloadWindowManager from '../DownloadWindowManager';
import WindowConstants from '../../../utils/constants/WindowConstants';
import BackgroundNotification from './BackgroundNotification';
import HiAIServiceAbility from '../../../../framework/HiAIServiceAbility';
import { image } from '@kit.ImageKit';
import ModelTaskManager from '../../ModelTaskManager'
import mediaQuery from '@ohos.mediaquery';
import NoToastConstants from '../../../utils/constants/NoToastConstants';
import ModelDownloadUIExtentionAbility from '../../../../framework/ability/ModelDownloadUIExtensionAbility';

let storage = LocalStorage.getShared();
const TAG: string = 'ModelDownloadPage';

@Entry(storage)
@Component
export struct ModelDownloadPage {
    @State message: string = 'ModelDownloadUIExtensionAbility'
    @State progress: number = CommonConstants.DEFAULT_INT;
    @State pkgSize: number = CommonConstants.MAX_PROGRESS;
    @State packageSize: number = CommonConstants.DEFAULT_INT;
    @State timer: number = CommonConstants.DEFAULT_INT;
    @State isStartProgress: boolean = true;
    @State moduleName: string = DownloadWindowManager.foreModuleName;
    @State moduleType: number = DownloadWindowManager.foreModuleType;
    @State windowName: string = DownloadWindowManager.windowName;
    @State lastProgressUpdateTime: number = Date.now();
    @State isDarkMode: boolean = false;
    @StorageLink('dialogWidth') dialogWidth: number = CommonConstants.PC_DIALOG_WIDTH;
    @StorageLink('progressTextWidth') progressTextWidth: number = CommonConstants.PC_PROGRESS_WIDTH;
    private session: UIExtensionContentSession | undefined = storage.get<UIExtensionContentSession>('session');
    private darkModeListener: mediaQuery.MediaQueryListener = mediaQuery.matchMediaSync('(dark-mode: true)');
    /**
     * 下载同意弹窗
     */
    agreeNotificationDialogController: CustomDialogController = new CustomDialogController({
        builder: ModelDownloadNotificationDialog({
            modelName: this.moduleName,
            modelType: this.moduleType,
            dialogWidth: this.dialogWidth,
            windowName: this.windowName,
            onOpenDownload: async () => {
                if (!ModelTaskManager.activeWindowSet.has(this.windowName)) {
                    ModelTaskManager.activeWindowSet.add(this.windowName)
                    HiAiLog.info(TAG, `request window open`);
                    this.isStartProgress = true;
                    this.progress = CommonConstants.DEFAULT_INT;
                    this.startDownload();
                } else {
                    HiAiLog.info(TAG, `foreground download window ${this.windowName} has already existed`)
                    this.session?.terminateSelf();
                }
            },
            onCloseDownload: () => {
                HiAiLog.info(TAG, `request window close`);
                this.isStartProgress = false;
                this.progress = CommonConstants.DEFAULT_INT;
                this.session?.terminateSelf();
                storage.clear();
            }
        }),
        autoCancel: false,
        customStyle: true,
        isModal: true,
        cancel: async () => {
            HiAiLog.info(TAG, `request window cancel`);
            let downloadCallbackProxy: IModelDownloadCallback =
                await ModelInfoManager.getCallbackProxy(this.windowName) ||
                ModelDownloadCapability.foreDownloadCallbackProxy;
                downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.DOWNLOAD_NOT_ALLOWED,
                    'The user returns to the previous page.');
            this.session?.terminateSelf();
        }
    })
    /**
     * 下载进度弹窗
     */
    progressDialogController: CustomDialogController = new CustomDialogController({
        builder: ModelDownloadProgressDialog({
            downloadProgress: this.progress,
            modelName: this.moduleName,
            dialogWidth: this.dialogWidth,
            textWidth: this.progressTextWidth,
            modelSize: this.packageSize,
            totalSize: this.pkgSize,
            windowName: this.windowName,
            onCancelDownload: () => {
                HiAiLog.info(TAG, `download window close, download model cancel.`);
                ModelDownloadUIExtentionAbility.hasTriggeredCallback = true;
                this.cancelDownload();
            },
            onPublish: async () => {
                HiAiLog.info(TAG, `download window publish`);
                await BackgroundNotification.publish(this.windowName);
            }
        }),
        autoCancel: false,
        customStyle: true,
        cancel: async () => {
            HiAiLog.info(TAG, `download window cancel, window name is: ` + this.windowName)
            let downloadCallbackProxy: IModelDownloadCallback =
                await ModelInfoManager.getCallbackProxy(this.windowName) ||
                ModelDownloadCapability.foreDownloadCallbackProxy;
            HiAiLog.info(TAG, `download window cancel`)
            downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.DOWNLOAD_BACKGROUND,
                ReportResultCodeMessage.DOWNLOAD_BACKGROUND);
            if (ModelDownloadCapability.deviceType === 'phone' || !ModelDownloadCapability.deviceType) {
                await BackgroundNotification.publish(this.windowName);
            }
            this.session?.terminateSelf();
        },

    })

    aboutToAppear() {
        let width = this.session?.getUIExtensionWindowProxy().properties.uiExtensionHostWindowProxyRect.width;
        HiAiLog.info(TAG, `UIExtension page show`);
        if (!width) {
            HiAiLog.error(TAG, `Can't get top window width`);
        } else if (width <= 1284) {
            width = this.dialogWidth = 345;
        }
        this.session?.getUIExtensionWindowProxy().on('windowSizeChange', (windowSize) => {
            HiAiLog.info(TAG, `windowSizeChange width is ${windowSize.width}`);
            HiAiLog.info(TAG, `this dialog width is ${this.dialogWidth}`)
            if (windowSize.width <= 1284) {
                this.dialogWidth = 345;
            }
        })
        // Initialize dark mode detection
        this.isDarkMode = this.darkModeListener.matches;
        this.darkModeListener.on('change', (mediaQueryResult: mediaQuery.MediaQueryResult) => {
            this.isDarkMode = mediaQueryResult.matches;
            HiAiLog.info(TAG, `Dark mode changed to: ${this.isDarkMode ? 'dark' : 'light'}`);
            // Update the cancel button image when theme changes
            this.getBackgroundImage();
        });

        this.agreeNotificationDialogController.open();
        this.getBackgroundImage();
    }

    aboutToDisappear() {
        HiAiLog.info(TAG, `UIExtension page destroy`)
        this.darkModeListener.off('change');
        storage.clear();
        this.agreeNotificationDialogController.close();
        this.progressDialogController.close();
    }

    /**
     * Get Background Notification Image PixelMap.
     */
    getBackgroundImage() {
        HiAiLog.info(TAG, `get Background Notification Image PixelMap.`);

        // Choose appropriate SVG based on dark mode
        let cancelIconResource = this.isDarkMode ?
        $r('app.media.ic_public_cancel_white') :
        $r('app.media.ic_public_cancel_background');

        HiAiLog.info(TAG, `Using cancel icon: ${this.isDarkMode ? 'white' : 'default'} for background`);

        HiAIServiceAbility.globalContext?.resourceManager.getMediaContent(cancelIconResource)
            .then((data) => {
                let arrayBuffer = data.buffer.slice(data.byteOffset, data.byteLength + data.byteOffset)
                let imageSource: image.ImageSource = image.createImageSource(arrayBuffer);
                imageSource.getImageInfo((err, value) => {
                    if (err) {
                        HiAiLog.info(TAG, `get Image Info error is: ` + JSON.stringify(err));
                        return;
                    }
                    let opts: image.DecodingOptions = {
                        editable: true,
                        desiredSize: {
                            height: value.size.height,
                            width: value.size.width
                        }
                    };
                    imageSource.createPixelMap(opts, (err, pixelMap) => {
                        if (err) {
                            HiAiLog.info(TAG, `create PixelMap error is: ` + JSON.stringify(err));
                            return;
                        }
                        DownloadWindowManager.imagePixelMap = pixelMap;
                        imageSource.release();
                        HiAiLog.info(TAG, 'pixelMap is success');
                    });
                })
            });
    }

    startDownload() {
        HiAiLog.info(TAG, 'download window StartDownload');

        this.timer = setInterval(async () => {
            let isDownloadEnd: boolean = ModelInfoManager.getIsDownloadEndStatus(this.windowName);
            HiAiLog.info(TAG, 'download window notification isDownloadEnd is: ' + isDownloadEnd);

            if (DownloadWindowManager.isSubscribed) {
                BackgroundNotification.publishNotification(this.windowName, this.progress, this.moduleName);
            }

            if (this.progress === this.pkgSize || isDownloadEnd || !NetWorkUtil.isAvailableNetwork()) {
                HiAiLog.info(TAG, 'download window clearInterval timer is： ' + this.timer);
                clearInterval(this.timer);
                ModelDownloadCapability.hasForeDownload = false;
                this.progressDialogController.close();
                if (!NetWorkUtil.isAvailableNetwork()) {
                    HiAiLog.info(TAG,`Trigger network unavailable in start download page`)
                    let downloadCallbackProxy: IModelDownloadCallback =
                        await ModelInfoManager.getCallbackProxy(this.windowName);
                    downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.NO_NETWORK_STATUS,
                        ReportResultCodeMessage.NO_NETWORK_STATUS);
                    let modelNames: string[] = ModelInfoManager.getAllModelNames();
                    for (let modelName of modelNames) {
                        if (ModelInfoManager.getDomain(modelName) !== 'TTS' && modelName !== NoToastConstants.IMAGE_CAPTION) {
                            DownloadWindowManager.createWindow(WindowConstants.TOAST_WINDOW_NAME,
                                WindowConstants.TOAST_WINDOW_PATH);
                            break;
                        }
                    }
                    ModelDownloadManager.getInstance()
                        .deleteModelDownloadTask(ModelDownloadCapability.foreModelDownloadInfo.resId);

                    ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
                        ReportResultCode.NO_NETWORK_STATUS, ReportResultCodeMessage.NO_NETWORK_STATUS,
                        ModelDownloadCapability.foreDownloadStartTime.getTime());
                    ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);
                    ModelTaskManager.activeWindowSet.delete(this.windowName);
                    this.session?.terminateSelf();
                    storage.clear();
                } else if (isDownloadEnd) {
                    ModelInfoManager.updateIsDownloadSuccessStatus(this.windowName, true);
                    ModelTaskManager.activeWindowSet.delete(this.windowName);
                    this.session?.terminateSelf();
                    storage.clear();
                }
            }

            let isExisted = ModelInfoManager.getIsExistedStatus(this.windowName);
            HiAiLog.info(TAG, `isExisted status is ${isExisted}`);
            if (isExisted && this.isStartProgress) {
                this.isStartProgress = false;
                this.packageSize = ModelInfoManager.getModelSize(this.windowName);
                try {
                    HiAiLog.info(TAG, `try open progressDialogController`)
                    this.progressDialogController.open();
                } catch (e) {
                    HiAiLog.error(TAG, `failed to open progressDialogController ${e.code}:${e.message}`)
                }
            }

            let currentProgress = ModelInfoManager.getDownloadProgress(this.windowName);
            if (currentProgress !== this.progress) {
                this.progress = currentProgress;
                this.lastProgressUpdateTime = Date.now();
            } else if (Date.now() - this.lastProgressUpdateTime >
            CommonConstants.NO_NETWORK_TIMEOUT) { // 1 minute timeout
                HiAiLog.warn(TAG, 'Download progress has not updated for 1 minute, closing window');
                clearInterval(this.timer);
                ModelDownloadCapability.hasForeDownload = false;
                this.progressDialogController.close();
                ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
                    ReportResultCode.DOWNLOAD_CANCELED, ReportResultCodeMessage.DOWNLOAD_CANCELED,
                    ModelDownloadCapability.foreDownloadStartTime.getTime());
                ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);
                ModelDownloadCapability.hasForeDownload = false;
                let downloadCallbackProxy: IModelDownloadCallback =
                    await ModelInfoManager.getCallbackProxy(this.windowName);
                downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.DOWNLOAD_TIME_OUT,
                    'Download progress has not updated for 1 minute');
                this.session?.terminateSelf();
                ModelDownloadManager.getInstance().deleteModelDownloadTask(this.windowName);
                storage.clear();
            }
            HiAiLog.info(TAG, 'download window notification progress: ' + this.progress);
        }, 1000)
    }

    async cancelDownload() {
        HiAiLog.warn(TAG, `download is canceled.`)
        this.progress = CommonConstants.DEFAULT_INT;
        clearInterval(this.timer);
        HiAiLog.info(TAG, `Terminating session after download cancellation`);
        ModelTaskManager.activeWindowSet.delete(this.windowName);
        if (this.session) {
            this.session.terminateSelf();
            HiAiLog.info(TAG, `Session termination called successfully`);
        } else {
            HiAiLog.error(TAG, `Session is undefined, cannot terminate`);
        }
        storage.clear();
    }

    build() {
        Column() {
        }
        .width('100%')
        .height('100%')
    }
}