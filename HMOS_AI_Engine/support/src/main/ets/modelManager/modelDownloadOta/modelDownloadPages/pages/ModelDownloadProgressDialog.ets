/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { ModelDownloadReportUtil } from '../../../utils/ModelManagerReportUtil';
import ModelInfoManager from '../../modelDownloadInfo/ModelInfoManager';
import ModelDownloadManager from '../../ModelTaskManager';
import HiAIServiceAbility from '../../../../framework/HiAIServiceAbility';
import {
    ModelDownloadResultCode,
    ReportResultCode, ReportResultCodeMessage} from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import ModelDownloadCapability from '../../../ModelDownloadCapability';
import mediaQuery from '@ohos.mediaquery';
import DownloadWindowManager from '../DownloadWindowManager';

const TAG: string = 'ModelDownloadProgressDialog'

/**
 * The builder of PC Download progress dialog box.
 */
@CustomDialog
export struct ModelDownloadProgressDialog {
    dialogController: CustomDialogController
    onCancelDownload?: () => void
    onPublish?: () => Promise<void>
    @Link downloadProgress: number
    @Link modelName: string
    @Link modelSize: number
    @Link totalSize: number
    @Link dialogWidth: number;
    @Link textWidth: number;
    @Link windowName: string;
    @State isDarkTheme: boolean = false;

    themeListener: mediaQuery.MediaQueryListener = mediaQuery.matchMediaSync('(dark-mode: true)');

    aboutToAppear() {
        this.isDarkTheme = this.themeListener.matches;
        this.themeListener.on('change', (mediaQueryResult: mediaQuery.MediaQueryResult) => {
            this.isDarkTheme = mediaQueryResult.matches;
        });
    }

    aboutToDisappear() {
        this.themeListener.off('change');
    }

    build() {
        Column() {
            Row({space:12}) {
                Text( HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('ModelDownload_downloading',
                    this.modelName, this.modelSize.toString() + 'M'))
                    .width(this.textWidth)
                    .fontWeight(FontWeight.Regular)
                    .fontSize(14)
                    .fontColor($r('sys.color.font_primary'))
                    .lineHeight(19)

                Text(`${Math.round(this.downloadProgress)}%`)
                    .width(48)
                    .textAlign(TextAlign.End)
                    .fontWeight(FontWeight.Regular)
                    .fontSize(14)
                    .fontColor($r('sys.color.font_secondary'))
            }
            .width('100%')

            Row({space:12}) {
                Progress({value: this.downloadProgress, total: this.totalSize, type: ProgressType.Linear})
                    .style({strokeWidth: 4, enableSmoothEffect: true})
                    .width(this.dialogWidth-84)

                Button() {
                    Image(this.isDarkTheme ? $r('app.media.ic_public_cancel_progress_dark') : $r('app.media.ic_public_cancel_progress'))
                        .width(24)
                        .objectFit(ImageFit.Contain)
                        .accessibilityLevel('no')
                }
                .onClick(async () => {
                    HiAiLog.info(TAG,`User cancel download`)
                    let downloadCallbackProxy: IModelDownloadCallback =
                        await ModelInfoManager.getCallbackProxy(this.windowName);
                    downloadCallbackProxy.onCancel(this.windowName);
                    ModelDownloadManager.getInstance().deleteModelDownloadTask(this.windowName);

                    ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
                        ReportResultCode.DOWNLOAD_CANCELED, ReportResultCodeMessage.DOWNLOAD_CANCELED,
                        ModelDownloadCapability.foreDownloadStartTime.getTime());
                    ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);

                    ModelDownloadCapability.hasForeDownload = false;
                    let timer: number = setTimeout(() => {
                        clearTimeout(timer);
                        this.onCancelDownload!();
                    }, 300)
                    this.dialogController.close();
                })
                .accessibilityText(HiAIServiceAbility.globalContext?.resourceManager.getStringByNameSync('ModelDownload_cancel_button'))
                .backgroundColor(Color.Transparent)
            }
            .width('100%')
            .height(24)
        }
        .width(this.dialogWidth)
        .borderRadius($r('sys.float.ohos_id_corner_radius_dialog'))
        .backgroundColor($r('app.color.dialog_background_color'))
        .padding({ top: 24, left: 24, right: 24, bottom: 24 })
        .gesture(
            SwipeGesture({direction: SwipeDirection.All})
                .onAction(async ()=>{
                    this.dialogController.close();
                    await DownloadWindowManager.destroyWindow(this.windowName);
                    let downloadCallbackProxy: IModelDownloadCallback =
                        await ModelInfoManager.getCallbackProxy(this.windowName) ||
                        ModelDownloadCapability.foreDownloadCallbackProxy;
                    downloadCallbackProxy.onError(this.windowName, ModelDownloadResultCode.DOWNLOAD_BACKGROUND,
                        ReportResultCodeMessage.DOWNLOAD_BACKGROUND);
                    HiAiLog.info(TAG, `DownloadProgress publish`)
                    await this.onPublish!();
                })
        )
    }
}