/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import update from '@hms.system.update';
import { BusinessError } from '@kit.BasicServicesKit';

const TAG: string = "ModelUpgradeOta";

/**
 * Indicates the implements of model upgrade from OTA platform.
 * @class
 * <AUTHOR>
 */
export default class ModelUpgradeOta{
  private static instance: ModelUpgradeOta;
  private modelUpdater: update.PluginUpdater;

  private constructor() {
    HiAiLog.info(TAG, `ModelUpgradeOta constructor`);
    this.modelUpdater = update.getPluginUpdater();
  }

  /**
   * get instance.
   *
   * @returns { ModelUpgradeOta } the instance of ModelUpgradeOta.
   */
  public static getInstance(): ModelUpgradeOta{
    HiAiLog.info(TAG, `ModelUpgradeOta getInstance`);
    if(!ModelUpgradeOta.instance) {
      ModelUpgradeOta.instance = new ModelUpgradeOta();
    }
    return ModelUpgradeOta.instance;
  }

  /**
   * Register automatic upgrade for a set of models.
   *
   * @param { update.PluginUpdateInfo } pluginUpdateInfo - An array of model ID of the models to be downloaded.
   * @param { update.PluginCallerInfo } callerInfo - Information about the plugin caller, of type update.PluginCallerInfo.
   * @returns { Promise<update.PluginErrorInfo> } A Promise that resolves to an object of type update.PluginErrorInfo.
   */
  public registerAutoUpgrade(pluginUpdateInfo: update.PluginUpdateInfo, callerInfo: update.PluginCallerInfo): Promise<update.PluginErrorInfo> {
    HiAiLog.info(TAG, `registerAutoUpgrade start`);
    return new Promise((resolve, reject)=>{
      this.modelUpdater.registerAutoUpdate(pluginUpdateInfo, callerInfo).then((errorInfo: update.PluginErrorInfo)=>{
        HiAiLog.info(TAG, `OTA registerAutoUpgrade result is: ` +JSON.stringify(errorInfo));
          resolve(errorInfo);
      }).catch((error: BusinessError)=>{
        if(JSON.stringify(error) !== '{}') {
          HiAiLog.error(TAG, `OTA registerAutoUpgrade catch error is: ` +JSON.stringify(error));
          reject(error);
        }
      })
    })
  }

  /**
   * Register cancel automatic upgrade for a set of models.
   *
   * @param { update.PluginUpdateInfo } pluginUpdateInfo - An array of model ID of the models to be downloaded.
   * @returns { Promise<update.PluginErrorInfo> } A Promise that resolves to an object of type update.PluginErrorInfo.
   * */
  public unRegisterAutoUpgrade(pluginUpdateInfo: update.PluginUpdateInfo): Promise<update.PluginErrorInfo> {
    HiAiLog.info(TAG, `unRegisterAutoUpgrade start`);
    return new Promise((resolve, reject)=>{
      this.modelUpdater.unregisterAutoUpdate(pluginUpdateInfo).then((errorInfo: update.PluginErrorInfo)=>{
        HiAiLog.info(TAG, `OTA unRegisterAutoUpgrade result is: ` +JSON.stringify(errorInfo));
        resolve(errorInfo);
      }).catch((error: BusinessError)=>{
        if(JSON.stringify(error) !== '{}') {
          reject(error);
        }
      })
    })
  }
}