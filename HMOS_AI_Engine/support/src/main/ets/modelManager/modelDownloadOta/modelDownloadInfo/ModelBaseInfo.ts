/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import CommonConstants from '../../utils/constants/CommonConstants';
import { ModelDownloadType, NetworkType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import update from '@hms.system.update';

const TAG: string = "ModelBaseInfo";

/**
 * Indicates the extra model information, which to be queried by OTA platform.
 */
export type ExtraInfo = {
  modelSize: number;
  modelID: string;
  modelVersion: string;
};

/**
 * Indicates the basic model information
 * @class
 * <AUTHOR>
 */
export default class ModelBaseInfo {
  private callerInfo: update.PluginCallerInfo = null;
  private modelUpdateInfo: update.PluginUpdateInfo = null;
  private extraInfo: ExtraInfo = null;

  /**
   * Constructor for the ModelBaseInfo class.
   *
   * @param { string } modelName - The name of the model.
   * @param { string } domain - The domain of the model.
   * @param { number } modelDownloadType - The type of model download.
   * @param { ModelDomain } modelDomain - The domain of the model.
   * @param { string }compatibleVersion - Optional compatible version of the model.
   */
  constructor(modelName: string, domain: string, modelDownloadType: number, modelDomain: string,
    compatibleVersion?: string, networkType?: number) {
    let initCompatibleVersion: string = CommonConstants.MODEL_INIT_COMPATIBLE_VERSION;
    if (compatibleVersion) { // compatibleVersion not empty
      initCompatibleVersion = compatibleVersion;
    }
    this.setCallerInfo(modelDownloadType, networkType);
    this.setModelUpdateInfo(modelName, domain, modelDomain, initCompatibleVersion);
  }

  /**
   * Method to set the caller information.
   *
   * @param { number } isForegroundCall - A number indicating if the call is from the foreground.
   */
  setCallerInfo(isForegroundCall: number, networkType?: number): void {
    HiAiLog.info(TAG, `setCallerInfo start`);
    this.callerInfo = {
      businessType: update.BusinessSubType.MODEL,
      isForegroundCall: (isForegroundCall === ModelDownloadType.MODEL_FORE_DOWNLOAD) ? true : false,
      allowedNetType: (isForegroundCall === ModelDownloadType.MODEL_FORE_DOWNLOAD) ?
        update.NetType.CELLULAR_AND_WIFI | 8 : (networkType === NetworkType.ANY) ?
          update.NetType.CELLULAR_AND_WIFI | 8 : update.NetType.WIFI | 8
    };
  }

  /**
   * Method to get the caller information.
   *
   * @returns { update.PluginCallerInfo } The caller information of type update.PluginCallerInfo.
   */
  getCallerInfo(): update.PluginCallerInfo {
    HiAiLog.info(TAG, `getCallerInfo start`);
    return this.callerInfo;
  }

  /**
   * Method to set the model update information.
   *
   * @param { string } modelName - The name of the model.
   * @param { string } domain - The domain of the model.
   * @param { ModelDomain } modelType - The type of the model.
   * @param { string } compatibleVersion - Optional compatible version of the model.
   */
  setModelUpdateInfo(modelName: string, domain: string, modelType: string, compatibleVersion?: string): void {
    HiAiLog.info(TAG, `setModelUpdateInfo start`);
    let bundleInfo: update.PluginBundleInfo = {
      bundleName: CommonConstants.HIAI_PACKAGE_NAME,
      abilityName: CommonConstants.HIAI_ABILITY_NAME,
      packageName: domain
    }
    let modelInfo: update.PluginInfo = {
      pluginName: modelName,
      compatibleVersion: compatibleVersion,
      currentPluginVersion: CommonConstants.EMPTY_STRING,
      pluginType: modelType,
      packageName: domain
    }
    let modelInfos: Array<update.PluginInfo> = [];
    modelInfos.push(modelInfo);

    let modelUpdateInfo: update.PluginUpdateInfo = {
      bundleInfo: bundleInfo,
      pluginInfos: modelInfos,
      pluginCompatibleVersionCode: CommonConstants.EMPTY_STRING,
      executionPolicy: CommonConstants.EMPTY_STRING
    }
    this.modelUpdateInfo = modelUpdateInfo;
  }

  /**
   * Method to get the model update information.
   *
   * @returns { update.PluginUpdateInfo } The model update information of type update.PluginUpdateInfo.
   */
  getModelUpdateInfo(): update.PluginUpdateInfo {
    HiAiLog.info(TAG, `getModelUpdateInfo start`);
    return this.modelUpdateInfo;
  }

  /**
   * Method to set extra information.
   *
   * @param { ExtraInfo } extraInfo - The extra information of type ExtraInfo.
   */
  setExtraInfo(extraInfo: ExtraInfo): void {
    HiAiLog.info(TAG, `setExtraInfo start`);
    this.extraInfo = extraInfo;
  }

  /**
   * Method to get the extra information.
   *
   * @returns { ExtraInfo } The extra information of type ExtraInfo.
   */
  getExtraInfo(): ExtraInfo {
    HiAiLog.info(TAG, `getExtraInfo start`);
    return this.extraInfo;
  }

  /**
   * Method to set the model size.
   *
   * @param { number } modelSize - The size of the model.
   */
  setModelSize(modelSize: number): void {
    this.extraInfo.modelSize = modelSize;
    HiAiLog.info(TAG, `setModelSize start modelSize is: ` + this.extraInfo.modelSize);
  }

  /**
   * Method to get the model size.
   * @returns { number } The size of the model.
   */
  getModelSize(): number {
    HiAiLog.info(TAG, `getModelSize start`);
    return this.extraInfo.modelSize;
  }

  /**
   * Method to set the model type.
   *
   * @param { string } modelType - The type of the model.
   */
  setModelType(modelType: string): void {
    this.modelUpdateInfo.pluginInfos[0].pluginType = modelType;
    HiAiLog.info(TAG, `setModelType start modelType is: ` + modelType);
  }

  /**
   * Method to get the model type.
   *
   * @param { string } modelType - The type of the model.
   */
  getModelType(): string {
    return this.modelUpdateInfo.pluginInfos[0].pluginType;
  }

  /**
   * Method to get the model ID.
   *
   * @returns { string } The model ID as a string.
   */
  getModelID(): string {
    HiAiLog.info(TAG, `getModelID start`);
    return this.extraInfo.modelID;
  }

  /**
   * Method to get the model version.
   *
   * @returns { string } The model version as a string.
   */
  getModelVersion(): string {
    HiAiLog.info(TAG, `getModelVersion start`);
    return this.extraInfo.modelVersion;
  }
}