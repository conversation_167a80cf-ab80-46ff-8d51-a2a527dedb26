/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import ModelBaseInfo from './ModelBaseInfo';
import CommonConstants from '../../utils/constants/CommonConstants';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import <PERSON><PERSON><PERSON>Log from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { ModelDomain, NetworkType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';

const TAG: string = "ModelDownloadInfo";

/**
 * Model download status management.
 *
 */
export type ModelDownloadStatus = {
  /**
   * If the model exists, the download progress dialog box is displayed. The initial value is false.
   */
  isExisted: boolean;
  /**
   * Determines whether to start downloading. The initial value is false.
   */
  isDownloadStart: boolean;
  /**
   * Determines whether the download is complete.
   * The options are as follows: download is complete, download is canceled, and download ends abnormally. The initial value is false.
   */
  isDownloadEnd: boolean;
  /**
   * Indicates whether the download is successful. The initial value is false.
   */
  isDownloadSuccess: boolean;
};

/**
 * Model information management.
 *
 */
export type ModelInfo = {
  /**
   * Model basic information object.
   */
  modelBaseInfo: ModelBaseInfo;
  /**
   * Model download callback object.
   */
  modelCallbackProxy: IModelDownloadCallback;
  /**
   * Model download status object.
   */
  modelDownloadStatus: ModelDownloadStatus;
  /**
   * Model download progress.
   */
  modelDownloadProgress: number;
  /**
   * Determine whether to download through the OTA platform.
   */
  isOTAPlatform: boolean;
  /**
   * Model download network type
   */
  networkType: NetworkType;
};

/**
 * Manages the information about the model to be downloaded, including adding, deleting, querying, and updating.
 * @class
 * <AUTHOR>
 */
export default class ModelInfoManager{
  private static modelDownloadInfoDict: Record<string, ModelInfo> = {};

  constructor() {
  }

  /**
   * Add a ModelInfo object to the ModelDownloadInfoDict using the modelName as the key.
   *
   * @param { string } modelName - The name of the model.
   * @param { ModelInfo } ModelDownloadInfo - The information about the model download of type ModelInfo.
   */
  public static addModelInfo(modelName: string, modelDownloadInfo: ModelInfo): void {
    ModelInfoManager.modelDownloadInfoDict[modelName] = modelDownloadInfo;
    let dictSize: number = Object.keys(ModelInfoManager.modelDownloadInfoDict).length;
    HiAiLog.info(TAG, 'addModelInfo start modelName is: ' + modelName + ' , ModelDownloadInfoDict length is: ' + dictSize);
  }

  /**
   * Retrieve the ModelInfo object associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { ModelInfo } The ModelInfo object if found, or null if not found.
   */
  public static getModelInfo(modelName: string): ModelInfo {
    return ModelInfoManager.modelDownloadInfoDict[modelName] || null;
  }

  /**
   * Delete the ModelInfo object associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { boolean } The value true indicates the deletion was successful, false indicates the modelName was not found in the dictionary.
   */
  public static deleteModelInfo(modelName: string): boolean {
    HiAiLog.info(TAG, `deleteModelInfo start modelName is: ` + modelName);
    if (modelName in ModelInfoManager.modelDownloadInfoDict) {
      delete ModelInfoManager.modelDownloadInfoDict[modelName];
      let dictSize: number = Object.keys(ModelInfoManager.modelDownloadInfoDict).length;
      HiAiLog.info(TAG, `deleteModelDownloadTask deleteModelInfo start ModelDownloadInfoDict length is: ` + dictSize);
      return true;
    }
    return false;
  }

  /**
   * Retrieve all the ModelInfo object from the ModelDownloadInfoDict.
   *
   * @returns { ModelInfo[] } The ModelInfo objects.
   */
  public static getAllModelInfos(): ModelInfo[] {
    return Object.values(ModelInfoManager.modelDownloadInfoDict);
  }

  /**
   * Retrieve all the ModelInfo object from the ModelDownloadInfoDict.
   *
   * @returns { string[] } The Model Ids.
   */
  public static getAllModelNames(): string[] {
    return Object.keys(ModelInfoManager.modelDownloadInfoDict);
  }

  /**
   * Retrieve the ModelBaseInfo object associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { ModelBaseInfo } The ModelBaseInfo object if found, or null if not found.
   */
  public static getModelBaseInfo(modelName: string): ModelBaseInfo {
    return ModelInfoManager.modelDownloadInfoDict[modelName].modelBaseInfo || null;
  }


  /**
   * Update the modelDownloadProgress associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @param { number } progress - The value of the modelDownloadProgress.
   */
  public static updateDownloadProgress(modelName: string, progress: number): void {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelDownloadInfo) {
      modelDownloadInfo.modelDownloadProgress = progress;
      HiAiLog.info(TAG, `updateDownloadProgress start modelName is: ` + modelName + 'progress is: ' + modelDownloadInfo.modelDownloadProgress);
    }
  }

  /**
   * Retrieve the modelDownloadProgress associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { number } The modelDownloadProgress if found, or 0.
   */
  public static getDownloadProgress(modelName: string): number {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    HiAiLog.info(TAG, `getDownloadProgress start modelName is: ` + modelName + 'progress is: ' + modelDownloadInfo.modelDownloadProgress);
    return modelDownloadInfo? modelDownloadInfo.modelDownloadProgress : CommonConstants.DEFAULT_INT;
  }

  /**
   * Update the platform associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @param { boolean } platform - The value of the platform.
   */
  public static updateDownloadPlatform(modelName: string, platform: boolean): void {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelDownloadInfo) {
      modelDownloadInfo.isOTAPlatform = platform;
      HiAiLog.info(TAG, `updateDownloadPlatform start modelName is: ` + modelName + 'isOTAPlatform is: ' + modelDownloadInfo.isOTAPlatform);
    }
  }

  /**
   * Retrieve the isOTAPlatform associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { boolean } The isOTAPlatform if found, or true.
   */
  public static getDownloadPlatform(modelName: string): boolean {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getDownloadPlatform not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.isOTAPlatform : true;
  }

  /**
   * Update the modelSize associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @param { number } modelSize - The value of the modelSize.
   */
  public static updateModelSize(modelName: string, modelSize: number): void {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelDownloadInfo) {
      modelDownloadInfo.modelBaseInfo.setModelSize(modelSize);
      HiAiLog.info(TAG, `updateModelSize modelName is: ` + modelName + ', modelSize is: ' + modelSize);
    }
  }

  /**
   * Retrieve the modelSize associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { number } The number if found, or true.
   */
  public static getModelSize(modelName: string): number {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getModelSize not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelBaseInfo.getExtraInfo().modelSize : null;
  }

  /**
   * Update the isDownloadSuccessStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @param { boolean } isDownloadSuccess - The value of the isDownloadStartStatus.
   */
  public static updateIsDownloadSuccessStatus(modelName: string, isDownloadSuccess: boolean): void {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelDownloadInfo) {
      modelDownloadInfo.modelDownloadStatus.isDownloadSuccess = isDownloadSuccess;
      HiAiLog.info(TAG, `updateIsDownloadSuccessStatus modelName is: ` + modelName + 'isDownloadSuccess is: ' + modelDownloadInfo.modelDownloadStatus.isDownloadSuccess);
    }
  }

  /**
   * Retrieve the isDownloadSuccessStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { boolean } The isDownloadSuccessStatus if found, or false.
   */
  public static getIsDownloadSuccessStatus(modelName: string): boolean {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getIsDownloadSuccessStatus not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelDownloadStatus.isDownloadSuccess : false;
  }

  /**
   * Update the isDownloadStartStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @param { boolean } isDownloadStart - The value of the isDownloadStartStatus.
   */
  public static updateIsDownloadStartStatus(modelName: string, isDownloadStart: boolean): void {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelDownloadInfo) {
      modelDownloadInfo.modelDownloadStatus.isDownloadStart = isDownloadStart;
      HiAiLog.info(TAG, `updateIsDownloadStartStatus isDownloadStart is: ` + modelDownloadInfo.modelDownloadStatus.isDownloadStart);
    }
  }

  /**
   * Retrieve the isDownloadStartStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { boolean } The isDownloadStartStatus if found, or false.
   */
  public static getIsDownloadStartStatus(modelName: string): boolean {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getIsDownloadStartStatus not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelDownloadStatus.isDownloadStart : false;
  }

  /**
   * Update the isDownloadEndStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @param { boolean } isDownloadEnd - The value of the isDownloadEndStatus.
   */
  public static updateIsDownloadEndStatus(modelName: string, isDownloadEnd: boolean): void {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelDownloadInfo) {
      modelDownloadInfo.modelDownloadStatus.isDownloadEnd = isDownloadEnd;
      HiAiLog.info(TAG, `updateIsDownloadEndStatus modelName is: ` + modelName + 'isDownloadEnd is: ' + modelDownloadInfo.modelDownloadStatus.isDownloadEnd);
    }
  }

  /**
   * Retrieve the isDownloadEndStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { boolean } The isDownloadEndStatus if found, or false.
   */
  public static getIsDownloadEndStatus(modelName: string): boolean {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getIsDownloadEndStatus not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelDownloadStatus.isDownloadEnd : true;
  }

  /**
   * Update the isExistedStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @param { boolean } isExisted - The value of the isExistedStatus.
   */
  public static updateIsExistedStatus(modelName: string, isExisted: boolean): void {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (modelDownloadInfo) {
      modelDownloadInfo.modelDownloadStatus.isExisted = isExisted;
      HiAiLog.info(TAG, `updateIsExistedStatus modelName is: ` + modelName + 'isExisted is: ' + modelDownloadInfo.modelDownloadStatus.isExisted);
    }
  }

  /**
   * Retrieve the isExistedStatus associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { boolean } The isExistedStatus if found, or false.
   */
  public static getIsExistedStatus(modelName: string): boolean {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getIsExistedStatus not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelDownloadStatus.isExisted : false;
  }

  /**
   * Retrieve the modelID associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { string } The modelID if found, or ''.
   */
  public static getModelId(modelName: string): string {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if (!modelDownloadInfo || !modelDownloadInfo.modelBaseInfo || !modelDownloadInfo.modelBaseInfo.getExtraInfo()) {
      HiAiLog.info(TAG, `getModelId: modelDownloadInfo or its properties are null for modelName: ${modelName}`);
      return CommonConstants.EMPTY_STRING;
    }
    return modelDownloadInfo.modelBaseInfo.getExtraInfo().modelID || CommonConstants.EMPTY_STRING;
  }

  /**
   * Retrieve the modelName associated with the given modelID from the ModelDownloadInfoDict.
   *
   * @param { string } modelId - The ID of the model.
   * @returns { string } The modelName if found, or ''.
   */
  public static getModelNameById(modelId: string): string {
    const allModelNames = Object.keys(ModelInfoManager.modelDownloadInfoDict);
    for (const modelName of allModelNames) {
      const currentModelId = ModelInfoManager.getModelId(modelName);
      if (currentModelId === modelId) {
        return modelName;
      }
    }
    return CommonConstants.EMPTY_STRING;
  }

  /**
   * Retrieve the isForegroundCall associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { boolean } The isForegroundCall if found, or true.
   */
  public static getIsForeGroundDownload(modelName: string): boolean {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getIsForeGroundDownload not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelBaseInfo.getCallerInfo().isForegroundCall : true;
  }

  /**
   * Retrieve the modelCallbackProxy associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { Promise<IModelDownloadCallback> } The modelCallbackProxy if found, or null.
   */
  public static async getCallbackProxy(modelName: string): Promise<IModelDownloadCallback> {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    return modelDownloadInfo? modelDownloadInfo.modelCallbackProxy : null;
  }

  /**
   * Retrieve the domain associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { string } The domain if found, or ''.
   */
  public static getDomain(modelName: string): string {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getDomain not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelBaseInfo.getModelUpdateInfo().pluginInfos[CommonConstants.DEFAULT_INT].packageName : CommonConstants.EMPTY_STRING;
  }

  /**
   * Retrieve the modelVersion associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { string } The modelVersion if found, or ''.
   */
  public static getModelVersion(modelName: string): string {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getModelVersion not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelBaseInfo.getExtraInfo().modelVersion : CommonConstants.EMPTY_STRING;
  }

  /**
   * Retrieve the modelType associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { string } The modelType if found, or '0'.
   */
  public static getModelType(modelName: string): string {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getModelVersion not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelBaseInfo.getModelType(): ModelDomain.MODEL_TYPE_AI;
  }

  /**
   * Retrieve the ModelDownloadNetworkType associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { string } The modelType if found, or '0'.
   */
  public static getModelDownloadNetworkType(modelName: string): NetworkType {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getModelVersion not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.networkType: NetworkType.WLAN;
  }

  /**
   * Retrieve the compatibleVersion associated with the given modelName from the ModelDownloadInfoDict.
   *
   * @param { string } modelName - The name of the model.
   * @returns { string } The compatibleVersion if found, or ''.
   */
  public static getModelCompatibleVersion(modelName: string): string {
    let modelDownloadInfo = ModelInfoManager.getModelInfo(modelName);
    if(modelDownloadInfo) {
      HiAiLog.info(TAG, `getModelCompatibleVersion not null`);
    }
    return modelDownloadInfo? modelDownloadInfo.modelBaseInfo.getModelUpdateInfo().pluginInfos[CommonConstants.DEFAULT_INT].compatibleVersion : CommonConstants.EMPTY_STRING;
  }
}