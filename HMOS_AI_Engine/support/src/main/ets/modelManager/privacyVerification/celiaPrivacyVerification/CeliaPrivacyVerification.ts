/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import SystemSetting from './SystemSetting';

import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import HiAIServiceAbility from '../../../framework/HiAIServiceAbility';
import { AsyncCallback } from '@kit.BasicServicesKit';
import ModelTaskManager from '../../modelDownloadOta/ModelTaskManager';
import JsonUtil, { JsonData } from '../../utils/JsonUtil';
import ModelDownloadInfo from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadInfo';

const TAG = "CeliaPrivacyVerification";

const WLAN_STATUS_KEY = 'fusion_assistant_wifi_auto_update_on';

const PRIVACY_AGREE = 'fusion_assistant_privacy_on';

/**
 * This class is used for  privacy verification
 */
export default class CeliaPrivacyVerification {
  private static instance: CeliaPrivacyVerification;

  /**
   * Gets the singleton instance of CeliaPrivacyVerification.
   *
   * @returns {ModelAgingManager} The singleton instance of CeliaPrivacyVerification.
   */
  public static getInstance(): CeliaPrivacyVerification {
    if (!CeliaPrivacyVerification.instance) {
      CeliaPrivacyVerification.instance = new CeliaPrivacyVerification();
    }
    return CeliaPrivacyVerification.instance;
  }

  private context = HiAIServiceAbility.globalContext;

  /**
   * check whether automatic updating is allowed
   *
   * @returns
   */
  public isAutomaticUpdatingAllowed(): boolean {
    let wlanStatus = SystemSetting.getValue(this.context, WLAN_STATUS_KEY, "0");
    HiAiLog.info(TAG, `The current WLAN status is ${wlanStatus}`);
    return wlanStatus === '1';
  }

  /**
   * Listens for WLAN status changes and manages model subscriptions accordingly.
   *
   * @returns
   */
  public listenWlanStatus(): void {
    // 监听订阅状态，如果是WLAN不通过，取消订阅 TaskManager
    const callback: AsyncCallback<void> = async () => {
      let wlanVal = SystemSetting.getValue(this.context, WLAN_STATUS_KEY, "get failed");
      HiAiLog.info(TAG, `WLAN config is :${wlanVal}`);
      try {
        const jsonContent = JsonUtil.getJsonContent();
        if (!jsonContent) {
          HiAiLog.error(TAG, 'No model data found in local storage');
          return;
        }
        const jsonData = JSON.parse(jsonContent) as JsonData;
        const modelDownloadInfos: Array<ModelDownloadInfo> = [];
        const isSubscribed = wlanVal === "1";
        // 处理所有模型
        for (const model of jsonData.downloadedModels) {
          // 更新本地模型的订阅状态
          model.isSubscribed = isSubscribed;
          await JsonUtil.updateModel(model);
          HiAiLog.info(TAG, `Updated subscription status to ${isSubscribed} for model ${model.modelName}`);
          // 创建 ModelDownloadInfo 对象
          const modelInfo = new ModelDownloadInfo();
          modelInfo.domain = model.domain;
          modelInfo.resId = model.modelName;
          modelInfo.resCompatibleVersion = model.resCompatibleVersion;
          // 使用最新版本作为当前版本
          if (model.versionInfos && model.versionInfos.length > 0) {
            modelInfo.resVersion = model.versionInfos[0].version;
          }
          modelDownloadInfos.push(modelInfo);
        }

        if (modelDownloadInfos.length === 0) {
          HiAiLog.info(TAG, 'No models found');
          return;
        }
        if (isSubscribed) {
          // WLAN 开启，重新订阅所有模型
          HiAiLog.info(TAG, 'WLAN enabled, resubscribing all models');
          ModelTaskManager.getInstance().allModelSubscribe(modelDownloadInfos);
        } else {
          // WLAN 关闭，取消订阅所有模型
          HiAiLog.info(TAG, 'WLAN disabled, unsubscribing all models');
          ModelTaskManager.getInstance().allModelUnSubscribe(modelDownloadInfos);
        }
      } catch (error) {
        HiAiLog.error(TAG, `Failed to process model subscription: ${error}`);
      }
    }
    // start listening
    SystemSetting.on(this.context, WLAN_STATUS_KEY, callback);
  }
}