/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import Settings from '@ohos.settings';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import common from '@ohos.app.ability.common';
import settings from '@ohos.settings';
import osAccount from '@ohos.account.osAccount';
import process from '@ohos.process';
import { AsyncCallback, BusinessError } from '@ohos.base';

const TAG = "SystemSetting";
const SETTINGS_URI: string = "datashare:///com.ohos.settingsdata/entry/settingsdata/SETTINGSDATA?Proxy=true&key=";
const SETTINGS_URI_USER: string =
  "datashare:///com.ohos.settingsdata/entry/settingsdata/USER_SETTINGSDATA_LOCALUSERID?Proxy=true&key=";

// This is an observer class that continuously watches for Celia setting changes
export namespace SystemSetting {

  /**
   * Gets the value of a setting.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {string} defValue - The default value if setting is not found.
   * @returns {string} The value of the setting or default value.
   */
  export function getValue(context: common.Context, name: string, defValue: string): string {
    HiAiLog.info(TAG, "getValue:" + name);
    if (!context) {
      HiAiLog.warn(TAG, `getValue, context invalid`);
      return defValue;
    }
    let result: string = Settings.getValueSync(context, name, defValue);
    HiAiLog.debug(TAG, `getValue, name=${name}, value=${result}`);
    return result;
  }

  /**
   * Gets the value of a setting asynchronously.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {string} defValue - The default value if setting is not found.
   * @returns {Promise<string>} A promise that resolves with the setting value.
   */
  export async function getValueAsync(context: common.Context, name: string, defValue: string): Promise<string> {
    HiAiLog.info(TAG, "getValueAsync:" + name);
    if (!context) {
      HiAiLog.warn(TAG, `getValueAsync, context invalid`);
      return defValue;
    }
    return Settings.getValue(context, name, defValue);
  }

  /**
   * Sets a global setting value.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {string} value - The value to set.
   * @returns {boolean} True if setting was set successfully.
   */
  export function setValueGlobal(context: common.Context, name: string, value: string): boolean {
    HiAiLog.info(TAG, "setValue:" + name);
    let globalResult = Settings.setValueSync(context, name, value);
    HiAiLog.debug(TAG, `setValue, name=${name}, value=${value}, globalResult=${globalResult}`);
    return globalResult;
  }

  /**
   * Sets a setting value with domain name.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {string} value - The value to set.
   * @param {string} domainName - The domain name for the setting.
   * @returns {boolean} True if setting was set successfully.
   */
  export function setSettingsValue(context, name: string, value: string, domainName: string): boolean {
    HiAiLog.info(TAG, "setSettingsValue called");
    let result: boolean = Settings.setValueSync(context, name, value, domainName);
    HiAiLog.debug(TAG, `setSettingsValue, name=${name}, value=${value}, result=${result}`);
    return result;
  }

  /**
   * Sets a setting value asynchronously with domain name.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {string} value - The value to set.
   * @param {string} domainName - The domain name for the setting.
   * @returns {Promise<boolean>} A promise that resolves with the operation result.
   */
  export function setSettingsValueAsync(context, name: string, value: string, domainName: string): Promise<boolean> {
    HiAiLog.info(TAG, "setSettingsValueAsync called");
    HiAiLog.debug(TAG, `setSettingsValue, name=${name}, value=${value}`);
    return Settings.setValue(context, name, value, domainName);
  }

  /**
   * Gets a setting value with domain name.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {string} defValue - The default value if setting is not found.
   * @param {string} domainName - The domain name for the setting.
   * @returns {string} The value of the setting or default value.
   */
  export function getSettingsValue(context, name: string, defValue: string, domainName: string): string {
    HiAiLog.info(TAG, "getSettingsValue called");
    if (!context) {
      HiAiLog.warn(TAG, `getSettingsValue, context invalid`);
      return defValue;
    }
    let result: string = Settings.getValueSync(context, name, defValue, domainName);
    HiAiLog.debug(TAG, `getSettingsValue, name=${name}, value=${result}`);
    return result;
  }

  /**
   * Registers a callback for setting changes.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting to monitor.
   * @param {Function} callback - The callback function to be called when setting changes.
   */
  export function on(context, name, callback): void {
    let listenUri = SETTINGS_URI + name;
    import('@ohos.data.dataShare').then(dataShare => {
      dataShare.default.createDataShareHelper(context, listenUri).then((dataHelper) => {
        HiAiLog.info(TAG, "createDataShareHelper on success:" + name);
        dataHelper.on("dataChange", listenUri, callback);
      });
    })
  }

  /**
   * Unregisters a callback for setting changes.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {Function} callback - The callback function to remove.
   */
  export function off(context, name, callback): void {
    let listenUri = SETTINGS_URI + name;
    import('@ohos.data.dataShare').then(dataShare => {
      dataShare.default.createDataShareHelper(context, listenUri).then((dataHelper) => {
        HiAiLog.info(TAG, "createDataShareHelper off success:" + name);
        dataHelper.off("dataChange", listenUri, callback);
      });
    })
  }

  /**
   * Registers a callback for local user setting changes.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting to monitor.
   * @param {Function} callback - The callback function to be called when setting changes.
   */
  export function onOfLocalUser(context, name, callback): void {
    let accountManager: osAccount.AccountManager = osAccount.getAccountManager();
    let listenUri: string = SETTINGS_URI + name;
    try {
      let localId: number = accountManager.getOsAccountLocalIdForUidSync(process.uid);
      let localUri: string = SETTINGS_URI_USER.replace('LOCALUSERID', localId.toString());
      listenUri = localUri + name;
    } catch (err) {
      HiAiLog.info(this.TAG, 'getOsAccountLocalIdForUidSync exception: ' + JSON.stringify(err));
    }
    HiAiLog.debug(TAG, 'listenUri ' + listenUri);
    import('@ohos.data.dataShare').then(dataShare => {
      dataShare.default.createDataShareHelper(context, listenUri).then((dataHelper) => {
        HiAiLog.info(TAG, "createDataShareHelper on success:" + name);
        dataHelper.on("dataChange", listenUri, callback);
      });
    })
  }

  /**
   * Unregisters a callback for local user setting changes.
   *
   * @param {common.Context} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {Function} callback - The callback function to remove.
   */
  export function offOfLocalUser(context, name, callback): void {
    let accountManager: osAccount.AccountManager = osAccount.getAccountManager();
    let listenUri: string = SETTINGS_URI + name;
    try {
      let localId: number = accountManager.getOsAccountLocalIdForUidSync(process.uid);
      let localUri: string = SETTINGS_URI_USER.replace('LOCALUSERID', localId.toString());
      listenUri = localUri + name;
    } catch (err) {
      HiAiLog.info(this.TAG, 'getOsAccountLocalIdForUidSync exception: ' + JSON.stringify(err));
    }
    HiAiLog.debug(TAG, 'listenUri ' + listenUri);
    import('@ohos.data.dataShare').then(dataShare => {
      dataShare.default.createDataShareHelper(context, listenUri).then((dataHelper) => {
        HiAiLog.info(TAG, "createDataShareHelper off success:" + name);
        dataHelper.off("dataChange", listenUri, callback);
      });
    })
  }

  /**
   * Registers an observer for setting key changes.
   *
   * @param {common.UIAbilityContext | common.ExtensionContext} context - The context object.
   * @param {string} name - The name of the setting to observe.
   * @param {string} domainName - The domain name for the setting.
   * @param {AsyncCallback<void>} observer - The callback function to be called when setting changes.
   */
  export function registerKeyObserver(context: common.UIAbilityContext | common.ExtensionContext, name: string,
    domainName: string, observer: AsyncCallback<void>): void {
    HiAiLog.info(TAG, 'registerKeyObserver');
    if (!context) {
      HiAiLog.error(TAG, 'registerKeyObserver context is invalid');
      return;
    }
    settings.registerKeyObserver(context, name, domainName, () => {
      HiAiLog.info(TAG, 'do observer');
      observer({
        code: 0,
        message: "callBack"
      } as BusinessError);
    });
  }

  /**
   * Unregisters an observer for setting key changes.
   *
   * @param {common.UIAbilityContext | common.ExtensionContext} context - The context object.
   * @param {string} name - The name of the setting.
   * @param {string} domainName - The domain name for the setting.
   */
  export function unregisterKeyObserver(context: common.UIAbilityContext | common.ExtensionContext, name: string,
    domainName: string): void {
    HiAiLog.info(TAG, 'unregisterKeyObserver');
    if (!context) {
      HiAiLog.error(TAG, 'unregisterKeyObserver context is invalid');
      return;
    }
    settings.unregisterKeyObserver(context, name, domainName);
  }

  /**
   * Gets privacy setting from OS account.
   *
   * @param {common.Context} context - The context object.
   * @param {string} key - The key of the privacy setting.
   * @returns {[string, boolean]} A tuple containing the setting value and whether it needs to be migrated.
   */
  export function getPrivacyFormOsAccount(context: common.Context, key: string): [string, boolean] {
    let domainValue = SystemSetting.getSettingsValue(context, key, '', settings.domainName.USER_PROPERTY);
    try {
      if (domainValue === '') {
        let accountManager: osAccount.AccountManager = osAccount.getAccountManager();
        let localId: number = accountManager.getOsAccountLocalIdForUidSync(process.uid);
        HiAiLog.info(TAG, 'getOsAccountLocalIdForUidSync successfully, localId: ' + localId);
        let globalValue = SystemSetting.getValue(context, key, '');
        if (localId === 100) {
          if (globalValue !== '') {
            SystemSetting.setSettingsValueAsync(context, key, globalValue, settings.domainName.USER_PROPERTY);
            return [globalValue, true];
          }
          return [globalValue, false];
        }
        return [domainValue, false];
      } else {
        return [domainValue, false];
      }
    } catch (err) {
      HiAiLog.info(TAG, 'getOsAccountLocalIdForUidSync exception: ' + JSON.stringify(err));
      return [domainValue, false];
    }
  }

  /**
   * Moves system setting to main OS account.
   *
   * @param {common.Context} context - The context object.
   * @param {string} key - The key of the setting to move.
   */
  export function moveSystemSettingToMainOsAccount(context: common.Context, key: string): void {
    SystemSetting.getValueAsync(context, key, settings.domainName.DEVICE_SHARED)?.then(globalValue => {
      SystemSetting.setSettingsValueAsync(context, key, globalValue, settings.domainName.USER_PROPERTY);
    }).catch(() => {
      HiAiLog.error(TAG, 'result is null');
    });
  }

  /**
   * Sets a value with multi-account support.
   *
   * @param {common.Context} context - The context object.
   * @param {string} key - The key of the setting.
   * @param {string} value - The value to set.
   * @returns {boolean} True if setting was set successfully.
   */
  export function setValueWithMultiAccount(context: common.Context, key: string, value: string): boolean {
    return SystemSetting.setSettingsValue(context, key, value, settings.domainName.USER_PROPERTY);
  }

  /**
   * Gets a value with multi-account support.
   *
   * @param {common.Context} context - The context object.
   * @param {string} key - The key of the setting.
   * @param {string} defValue - The default value if setting is not found.
   * @returns {string} The value of the setting or default value.
   */
  export function getValueWithMultiAccount(context: common.Context, key: string, defValue: string): string {
    return SystemSetting.getSettingsValue(context, key, defValue, settings.domainName.USER_PROPERTY);
  }
}

export default SystemSetting