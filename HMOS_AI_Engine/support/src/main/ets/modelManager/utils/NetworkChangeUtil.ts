/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import ModelDownloadManager from '../modelDownloadIds/modelDownloadIds/ModelDownloadIds';
import DownloadWindowManager from '../modelDownloadOta/modelDownloadPages/DownloadWindowManager';
import WindowConstants from './constants/WindowConstants';
import ModelDownloadCapability from '../ModelDownloadCapability';
import ModelInfoManager from '../modelDownloadOta/modelDownloadInfo/ModelInfoManager';
import { ModelDownloadResultCode, ReportResultCode } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { NetWorkUtil } from '@hms-ai/pdkfull/src/main/ets/utils/NetWorkUtil';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { connection } from '@kit.NetworkKit';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { ModelDownloadReportUtil } from './ModelManagerReportUtil';
import { ModelDownloadType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';
import ModelDownloadOta from '../modelDownloadOta/ModelDownloadOta';
import {
  NetworkType as ModelDownloadNetworkType
} from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant'
import NoToastConstants from '../utils/constants/NoToastConstants';

const TAG = 'NetworkChangeUtil';

/**
 * This network type.
 *
 */
export enum NetworkType {
  UNKNOWN_TYPE = -1,
  WLAN_TYPE = 0,
  CELLULAR_TYPE = 1
}

/**
 * This class is the network change tool class, including WLAN/Cellular network.
 *
 * <AUTHOR>
 */
export class NetworkChangeUtil {
  private static instance: NetworkChangeUtil;
  private static netCon = connection.createNetConnection()
  private static initNetworkType: NetworkType = NetworkType.UNKNOWN_TYPE;
  private static beforeNetworkType: NetworkType = NetworkType.UNKNOWN_TYPE;
  private static currentNetworkType: NetworkType = NetworkType.UNKNOWN_TYPE;
  public static networkExceptionToast: boolean = false;

  private constructor() {
  }

  /**
   * get single instance.
   *
   * @returns { NetworkChangeUtil } the instance of NetworkChangeUtil.
   */
  public static getInstance(): NetworkChangeUtil {
    if (NetworkChangeUtil.instance === undefined) {
      NetworkChangeUtil.instance = new NetworkChangeUtil();
    }
    return NetworkChangeUtil.instance;
  }

  /**
   * Register the network change listening.
   *
   */
  public registerNetworkChange(): void {
    HiAiLog.info(TAG, 'registerNetworkChange');
    NetworkChangeUtil.initNetworkType = NetworkChangeUtil.getNetworkType();
    NetworkChangeUtil.beforeNetworkType = NetworkChangeUtil.initNetworkType;

    NetworkChangeUtil.netCon.register(function (error) {
      HiAiLog.info(TAG, 'NetworkChangeUtil register error is ' + JSON.stringify(error));
      NetworkChangeUtil.networkExceptionToast = false;
      if (error === undefined || error.code === 2101008) {
        NetworkChangeUtil.netCon.on('netCapabilitiesChange', (data) => {
          if (!data || !data.netCap) {
            HiAiLog.error(TAG, 'NetworkChangeUtil netCapabilitiesChange data is invalid');
            return;
          }
          HiAiLog.info(TAG, 'NetworkChangeUtil netCapabilitiesChange is ' + JSON.stringify(data));
          NetworkChangeUtil.currentNetworkType = NetworkChangeUtil.getNetworkType();
          if (NetworkChangeUtil.beforeNetworkType === NetworkType.WLAN_TYPE && NetworkChangeUtil.currentNetworkType ===
          NetworkType.CELLULAR_TYPE && NetworkChangeUtil.initNetworkType === NetworkType.WLAN_TYPE) {
            HiAiLog.info(TAG, 'NetworkChangeUtil the network type is change.');
            NetworkChangeUtil.beforeNetworkType = NetworkChangeUtil.currentNetworkType;
            if (!NetworkChangeUtil.networkExceptionToast) {
              if (ModelDownloadCapability.modelDownloadType) {
                NetworkChangeUtil.networkExceptionToast = true;
                let modelNames: string[] = ModelInfoManager.getAllModelNames();
                for (let modelName of modelNames) {
                  if (ModelInfoManager.getDomain(modelName) !== 'TTS' && modelName !== NoToastConstants.IMAGE_CAPTION) {
                    DownloadWindowManager.createWindow(WindowConstants.TOAST_WINDOW_NAME,
                      WindowConstants.TOAST_WINDOW_PATH);
                  }
                }
              }
              NetworkChangeUtil.getInstance()
                .networkExceptHandle(ModelDownloadResultCode.NO_NETWORK_STATUS, "The network type is change.");
            }
          } else if (!data.netCap.networkCap?.some(num => (num === 16))) {
            HiAiLog.error(TAG, 'NetworkChangeUtil the network is unavailable')
            if (!NetworkChangeUtil.networkExceptionToast) {
              if (ModelDownloadCapability.modelDownloadType) {
                NetworkChangeUtil.networkExceptionToast = true;
                let modelNames: string[] = ModelInfoManager.getAllModelNames();
                for (let modelName of modelNames) {
                  if (ModelInfoManager.getDomain(modelName) !== 'TTS' && modelName !== NoToastConstants.IMAGE_CAPTION) {
                    DownloadWindowManager.createWindow(WindowConstants.TOAST_WINDOW_NAME,
                      WindowConstants.TOAST_WINDOW_PATH);
                  }
                }
              }
              NetworkChangeUtil.getInstance()
                .networkExceptHandle(ModelDownloadResultCode.NO_NETWORK_STATUS, "The network is unavailable");
            }
          }
        })
      }
    })
  }

  /**
   * Unregister network change listening.
   *
   */
  public unregisterNetworkChange(): void {
    HiAiLog.info(TAG, 'NetworkChangeUtil unregisterNetworkChange');
    NetworkChangeUtil.initNetworkType = NetworkType.UNKNOWN_TYPE;
    NetworkChangeUtil.beforeNetworkType = NetworkType.UNKNOWN_TYPE;
    NetworkChangeUtil.currentNetworkType = NetworkType.UNKNOWN_TYPE;
    NetworkChangeUtil.networkExceptionToast = false;
    try {
      NetworkChangeUtil.netCon.unregister(function (error) {
        HiAiLog.error(TAG, 'unregister error is ' + JSON.stringify(error))
      })
    } catch (e) {
      HiAiLog.error(TAG, 'unregister catch error is ' + JSON.stringify(e))
    }

  }

  /**
   * Handle the network exception.
   *
   * @param { ModelDownloadResultCode } errorCode - The error code.
   * @param { string } errorMessage - The error description.
   */
  public async networkExceptHandle(errorCode: number, errorMessage: string): Promise<void> {
    HiAiLog.info(TAG, 'NetworkChangeUtil networkExceptHandle');
    let modelNames: string[] = ModelInfoManager.getAllModelNames();
    for (let modelName of modelNames) {
      let modelDownloadNetworkType: ModelDownloadNetworkType =
        await ModelInfoManager.getModelDownloadNetworkType(modelName);
      if (modelDownloadNetworkType === ModelDownloadNetworkType.WLAN) {
        let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(modelName);
        downloadCallbackProxy.onError(modelName, errorCode, errorMessage);
        ModelDownloadManager.cancelModelDownload(modelName);

        let modelDomain: string = ModelInfoManager.getDomain(modelName);
        let interfaceName: string;
        switch (ModelDownloadCapability.modelDownloadType) {
          case ModelDownloadType.MODEL_BACK_DOWNLOAD: {
            interfaceName = 'BackgroundDownload';
            break;
          }
          case ModelDownloadType.MODEL_FORE_DOWNLOAD: {
            interfaceName = 'ForegroundDownload';
            break;
          }
          case ModelDownloadType.MODEL_UPGRADE: {
            interfaceName = 'Upgrade';
            break;
          }
          default:
            interfaceName = undefined;
        }
        let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(modelName, interfaceName, modelDomain);
        let startTime: Date = new Date()
        ModelDownloadReportUtil.updateBaseInfo(baseInfo, errorCode, errorMessage, startTime.getTime());
        ModelDownloadReportUtil.setReportInfoByBundleName(baseInfo);
      }
    }
  }


  /**
   * Get the network type.
   *
   * @returns { NetworkType } The network type.
   */
  static getNetworkType(): NetworkType {
    HiAiLog.info(TAG, 'NetworkChangeUtil getNetworkType');
    if (NetWorkUtil.isConnectNetwork()) {
      if (NetWorkUtil.isCellularNetwork()) {
        return NetworkType.CELLULAR_TYPE;
      } else {
        return NetworkType.WLAN_TYPE;
      }
    }
    return NetworkType.UNKNOWN_TYPE;
  }
}