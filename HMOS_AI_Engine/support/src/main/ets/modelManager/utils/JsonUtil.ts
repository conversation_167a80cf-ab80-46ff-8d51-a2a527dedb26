/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import fs from '@ohos.file.fs';
import { buffer, JSON } from '@kit.ArkTS';
import { ReadOptions } from '@kit.CoreFileKit';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';

const TAG: string = "JsonUtil";

// the standard stored model infomation in json file
export class StoredModelInfo {
  domain: string;
  modelName: string;
  resCompatibleVersion: string;
  modelType: string;
  isSubscribed: boolean;
  lastQueryVersion: string;
  lastQueryTime: number;
  versionInfos: VersionInfo[];
}

// model version infomation include version、stored time etc...
export class VersionInfo {
  version: string;
  storedTime: string;
  modelSize: number;
  status: UsageStatus;
  deleteTime: number;
  sourcePath: string;
}

// model usage status
export enum UsageStatus {
  UNUSED = 0,
  BEING_USED = 1,
  OUTDATED = 2
}

export type JsonData = {
  downloadedModels: StoredModelInfo[];
}

/**
 * This class is used to operate local json file
 */
export default class JsonUtil {
  public static jsonPath: string = '/data/storage/el2/base/haps/support/files/data/downloadedModels.json';
  public static el1JsonPath: string = '/data/storage/el1/base/haps/support/files/data/downloadedModels.json';
  private static fd: number = -1;
  private static el1Fd: number = -1;
  // local data map cache

  /**
   * Initializes the JSON file and map cache. Creates directory and file if they don't exist.
   *
   * @returns {Promise<void>} A promise that resolves when initialization is complete.
   */
  public static async initJsonFile(): Promise<void> {
    HiAiLog.info(TAG, 'initJsonFile');
    try {
      // Initialize EL2 directory and file
      const el2DirPath = '/data/storage/el2/base/haps/support/files/data/';
      try {
        if (!fs.accessSync(el2DirPath)) {
          fs.mkdirSync(el2DirPath, true);
        }
      } catch (e) {
        HiAiLog.error(TAG, `Failed to create EL2 directory: ${e}`);
      }

      if (!fs.accessSync(this.jsonPath)) {
        HiAiLog.info(TAG, 'Creating new EL2 JSON file');
        const jsonFile = fs.openSync(this.jsonPath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
        const initialContent = JSON.stringify({
          downloadedModels: []
        });
        fs.writeSync(jsonFile.fd, initialContent);
        fs.closeSync(jsonFile.fd);
      }

      // Initialize EL1 directory and file
      const el1DirPath = '/data/storage/el1/base/haps/support/files/data/';
      try {
        if (!fs.accessSync(el1DirPath)) {
          fs.mkdirSync(el1DirPath, true);
        }
      } catch (e) {
        HiAiLog.error(TAG, `Failed to create EL1 directory: ${e}`);
      }

      if (!fs.accessSync(this.el1JsonPath)) {
        HiAiLog.info(TAG, 'Creating new EL1 JSON file');
        const jsonFile = fs.openSync(this.el1JsonPath, fs.OpenMode.READ_WRITE | fs.OpenMode.CREATE);
        const initialContent = JSON.stringify({
          downloadedModels: []
        });
        fs.writeSync(jsonFile.fd, initialContent);
        fs.closeSync(jsonFile.fd);
      }
    } catch (e) {
      HiAiLog.error(TAG, `Failed to initialize jsonFile: ${e}`);
    }
  }

  /**
   * Retrieves the content of the JSON file.
   *
   * @param {boolean} isEl1 - Whether to read from EL1 level file
   * @returns {string} The content of the JSON file as a string.
   */
  public static getJsonContent(isEl1: boolean = false): string {
    const path = isEl1 ? this.el1JsonPath : this.jsonPath;
    if (!fs.accessSync(path)) {
      this.initJsonFile();
    }
    try {
      const jsonFile = fs.openSync(path);
      const fd = isEl1 ? this.el1Fd : this.fd;
      this.fd = jsonFile.fd;
    } catch (e) {
      HiAiLog.error(TAG, `Failed to open json file: ${e}`);
      return '';
    }
    const fileStats = fs.statSync(this.fd);
    let arrayBuffer = new ArrayBuffer(fileStats.size);
    let readOptions: ReadOptions = {
      offset: 0,
      length: arrayBuffer.byteLength
    };
    let readLen = fs.readSync(this.fd, arrayBuffer, readOptions);
    let buf = buffer.from(arrayBuffer, 0, readLen);
    fs.close(this.fd);
    return buf.toString('utf-8');
  }

  /**
   * Generates a unique key for the model map.
   *
   * @param {string} domain - The domain of the model.
   * @param {string} modelName - The name of the model.
   * @param {string} resCompatibleVersion - The compatible version of the model.
   * @returns {string} A unique key string combining the parameters.
   */
  private static getModelKey(domain: string, modelName: string, resCompatibleVersion: string): string {
    return `${domain}_${modelName}_${resCompatibleVersion}`;
  }

  /**
   * Writes JSON data to file.
   *
   * @param {JsonData} jsonData - The JSON data to write to file.
   * @param {boolean} isEl1 - Whether to write to EL1 level file
   * @returns {Promise<void>} A promise that resolves when writing is complete.
   */
  private static async writeJsonData(jsonData: JsonData, isEl1: boolean = false): Promise<void> {
    const path = isEl1 ? this.el1JsonPath : this.jsonPath;
    const content = JSON.stringify(jsonData);
    const file = fs.openSync(path, fs.OpenMode.READ_WRITE | fs.OpenMode.TRUNC);
    try {
      fs.writeSync(file.fd, content);
    } finally {
      fs.closeSync(file.fd);
    }
  }

  /**
   * Retrieves a model from the memory map.
   *
   * @param {string} domain - The domain of the model to retrieve.
   * @param {string} modelName - The name of the model to retrieve.
   * @param {string} resCompatibleVersion - The compatible version of the model to retrieve.
   * @param {boolean} isEl1 - Whether to read from EL1 level file
   * @returns {StoredModelInfo | null} The found model information or null if not found.
   */
  public static getModel(domain: string, modelName: string, resCompatibleVersion: string, isEl1: boolean = false): StoredModelInfo | null {
    try {
      const jsonContent = this.getJsonContent(isEl1);
      if (jsonContent) {
        const jsonData = JSON.parse(jsonContent) as JsonData;
        const model = jsonData.downloadedModels.find(
          m => m.domain === domain &&
            m.modelName === modelName &&
            m.resCompatibleVersion === resCompatibleVersion
        );
        return model || null;
      }
    } catch (error) {
      HiAiLog.error(TAG, `Failed to read model from JSON file: ${error}`);
      // Delete and reinitialize the JSON file when error occurs
      const path = isEl1 ? this.el1JsonPath : this.jsonPath;
      try {
        if (fs.accessSync(path)) {
          fs.unlinkSync(path);
        }
        this.initJsonFile();
      } catch (e) {
        HiAiLog.error(TAG, `Failed to reinitialize JSON file: ${e}`);
      }
    }
    return null;
  }

  /**
   * Adds a new model to both JSON file and memory map.
   *
   * @param {StoredModelInfo} model - The model information to add.
   * @param {boolean} isEl1 - Whether to write to EL1 level file
   */
  public static addModel(model: StoredModelInfo, isEl1: boolean = false): void {
    const jsonContent = this.getJsonContent(isEl1);
    const jsonData: JsonData = jsonContent ? JSON.parse(jsonContent) as JsonData : {
      downloadedModels: []
    };
    jsonData.downloadedModels.push(model);
    this.writeJsonData(jsonData, isEl1);
  }

  /**
   * Updates an existing model in both JSON file and memory map.
   *
   * @param {StoredModelInfo} model - The updated model information.
   * @param {boolean} isEl1 - Whether to write to EL1 level file
   * @returns {Promise<void>} A promise that resolves when the update is complete.
   */
  public static async updateModel(model: StoredModelInfo, isEl1: boolean = false): Promise<void> {
    const jsonContent = this.getJsonContent(isEl1);
    const jsonData: JsonData = JSON.parse(jsonContent) as JsonData;
    const index = jsonData.downloadedModels.findIndex(
      m => m.domain === model.domain &&
        m.modelName === model.modelName &&
        m.resCompatibleVersion === model.resCompatibleVersion
    );
    if (index !== -1) {
      jsonData.downloadedModels[index] = model;
      await this.writeJsonData(jsonData, isEl1);
    }
  }

  /**
   * Deletes a model or specific version of a model from both JSON file and memory map.
   *
   * @param {string} domain - The domain of the model to delete.
   * @param {string} modelName - The name of the model to delete.
   * @param {string} [resCompatibleVersion] - Optional compatible version to delete.
   * @param {string} [modelVersion] - Optional specific version to delete.
   * @param {boolean} isEl1 - Whether to write to EL1 level file
   * @returns {Promise<boolean>} A promise that resolves to true if deletion was successful.
   */
  public static async deleteModel(domain: string, modelName: string, resCompatibleVersion?: string,
    modelVersion?: string, isEl1: boolean = false): Promise<boolean> {
    const jsonContent = this.getJsonContent(isEl1);
    const jsonData: JsonData = JSON.parse(jsonContent) as JsonData;
    const modelIndex = jsonData.downloadedModels.findIndex(
      m => m.domain === domain && m.modelName === modelName
    );
    if (modelIndex === -1) {
      return false;
    }

    const model = jsonData.downloadedModels[modelIndex];
    if (resCompatibleVersion && modelVersion) {
      const versionIndex = model.versionInfos.findIndex(vi => vi.version === modelVersion);
      if (versionIndex === -1) {
        return false;
      }
      model.versionInfos.splice(versionIndex, 1);
      if (model.versionInfos.length === 0) {
        jsonData.downloadedModels.splice(modelIndex, 1);
      }
    } else {
      jsonData.downloadedModels.splice(modelIndex, 1);
    }

    await this.writeJsonData(jsonData, isEl1);
    return true;
  }
}

