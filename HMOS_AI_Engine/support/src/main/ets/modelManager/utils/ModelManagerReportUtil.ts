/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import HiAIServiceAbility from '../../framework/HiAIServiceAbility';
import { CallerInfo, BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { MaintenanceEventID } from '@hms-ai/pdkfull/src/main/ets/report/ReportNameSpace';
import MaintenanceReportInfo from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import ReportCoreManager from '@hms-ai/pdkfull/src/main/ets/report/ReportCoreManager';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import bundleManager from '@ohos.bundle.bundleManager';
import { rpc } from '@kit.IPCKit';

'use strict';

const TAG = "ModelDownloadReportUtil";

/**
 * This class is the dotting tool.
 *
 * <AUTHOR>
 */
export class ModelDownloadReportUtil {

  /**
   * Dotting information and reporting
   *
   * @param { BaseInfo } baseInfo - The object of base information.
   * @param { string } extraInfo - The extra information of reporting.
   */
  public static async setReportInfoByBundleName(baseInfo: BaseInfo, extraInfo?: string): Promise<void> {
    HiAiLog.info(TAG, "start to setReportInfoByBundleName");
    let maintenanceReportInfo = new MaintenanceReportInfo();

    maintenanceReportInfo.eventId = MaintenanceEventID.BASE_EVENT_ID;
    maintenanceReportInfo.baseInfo = baseInfo;
    let bundleFlag = bundleManager.BundleFlag.GET_BUNDLE_INFO_DEFAULT;
    let uid: number = rpc.IPCSkeleton.getCallingUid();
    let bundleName = await bundleManager.getBundleNameByUidSync(uid);
    let bundleInfo =  await bundleManager.getBundleInfoSync(bundleName, bundleFlag);
    HiAiLog.info(TAG,`setReportInfoByBundleName bundleInfo is ${JSON.stringify(bundleInfo)}`)
    let callInfo = new CallerInfo();
    callInfo.appName = bundleName;
    callInfo.appVersion = bundleInfo.versionName;
    maintenanceReportInfo.callerInfo = callInfo;
    if(extraInfo) {
      maintenanceReportInfo.extraInfo = extraInfo; // modelVersion
    }
    HiAiLog.info(TAG,
      `download maintenanceReportInfo: ${JSON.stringify([maintenanceReportInfo.eventId, maintenanceReportInfo.callerInfo, maintenanceReportInfo.baseInfo, maintenanceReportInfo.extraInfo])}`);
    ReportCoreManager.getInstance(HiAIServiceAbility.globalContext).then((manager) => {
      manager.onMaintenanceReport(maintenanceReportInfo);
    });
  }

  /**
   * Setting basic information about a dotting object.
   *
   * @param { string } interfaceName - The name of interface.
   * @returns { BaseInfo } The object of base information.
   */
  public static setBaseLogInfo(modelName: string, interfaceName: string, domain: string): BaseInfo {
    let baseInfo: BaseInfo = new BaseInfo();
    baseInfo.moduleName = modelName; // model name
    baseInfo.interfaceName = interfaceName as string; // interfaceName， including foreDownload、backDownload、subscribe、unSubscribe、delete、query
    baseInfo.serviceName = domain; //domain
    HiAiLog.info(TAG,`setBaseLogInfo interfaceName is ${baseInfo.interfaceName}`)
    return baseInfo;
  }

  /**
   * Update dotting information.
   *
   * @param { BaseInfo } baseInfo - The object of base information.
   * @param { number } statusCode - Status code.
   * @param { string } msg - Detailed description.
   * @param { number } startTime - Time when the interface starts to be executed.
   */
  public static updateBaseInfo(baseInfo: BaseInfo, statusCode: number, msg: string, startTime: number): void {
    baseInfo.resultCode = statusCode.toString();
    baseInfo.detailMessage = msg;
    baseInfo.runtime = (new Date().getTime() - startTime).toString();
  }
}