/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import ConfigurationConstant from '@ohos.app.ability.ConfigurationConstant';
import display from '@ohos.display';

const TAG = 'ScreenChangeUtil';

/**
 * This class is the screen change tool class, including landscape/portrait orientation change and folding machine state change.
 *
 * <AUTHOR>
 */
export class ScreenChangeUtil {
  private static instance: ScreenChangeUtil;
  private currentDirection: ConfigurationConstant.Direction = ConfigurationConstant.Direction.DIRECTION_NOT_SET;
  private foldDisplayMode: display.FoldDisplayMode = display.FoldDisplayMode.FOLD_DISPLAY_MODE_UNKNOWN;
  public static dialogWidth: number = 328;
  public static progressTextWidth: number = 184;

  private constructor() {
  }

  /**
   * get single instance.
   *
   * @returns { ScreenChangeUtil } the instance of ScreenChangeUtil.
   */
  public static getInstance(): ScreenChangeUtil {
    if (ScreenChangeUtil.instance === undefined) {
      ScreenChangeUtil.instance = new ScreenChangeUtil();
    }
    return ScreenChangeUtil.instance;
  }

  /**
   * Register the screen change listening.
   *
   */
  public registerScreenChange(): void {
    HiAiLog.info(TAG, 'registerScreenChange');
    this.registerOrientation();
    this.registerFoldableChange();
    this.updateDialogWidth();
  }

  /**
   * Unregister screen change listening.
   *
   */
  public unregisterScreenChange(): void {
    HiAiLog.info(TAG, 'unregisterScreenChange');
    this.unregisterOrientation();
    this.unregisterFoldableChange();
  }

  /**
   * Register screen landscape/portrait orientation change listening.
   *
   */
  private registerOrientation(): void {
    HiAiLog.info(TAG, 'ScreenOrientationUtil register');
    try {
      display.on('change', (data: number) => {
        let beforeDirection: ConfigurationConstant.Direction = this.currentDirection;
        this.updateDirection();
        if (beforeDirection !== this.currentDirection) {
          this.updateDialogWidth();
        }
      });
    } catch (error) {
      HiAiLog.error(TAG, `register exception, error is ` + JSON.stringify(error));
    }
    this.updateDirection();
  }

  /**
   *  Unregister screen landscape/portrait orientation change listening.
   *
   */
  private unregisterOrientation(): void {
    HiAiLog.info(TAG, 'ScreenOrientationUtil unregister');
    try {
      display.off('change');
    } catch (error) {
      HiAiLog.error(TAG, `unregister exception, error is ` + JSON.stringify(error));
    }
  }

  /**
   * Register the folding state change listening of the folding machine.
   *
   */
  private registerFoldableChange(): void {
    if (canIUse('SystemCapability.Window.SessionManager')) {
      try {
        if (display.isFoldable()) {
          this.foldDisplayMode = display.getFoldDisplayMode();
          HiAiLog.info(TAG, 'ScreenOrientationUtil register foldDisplayMode is ' + this.foldDisplayMode);
          display.on('foldDisplayModeChange', (foldDisplayMode: display.FoldDisplayMode) => {
            HiAiLog.info(TAG, 'foldDisplayModeChange ' + foldDisplayMode);
            this.foldDisplayMode = foldDisplayMode;
            this.updateDialogWidth();
          });
        }
      } catch (error) {
        HiAiLog.error(TAG, `registerFold isFoldable exception, error is ` + JSON.stringify(error));
      }
    }
  }

  /**
   * Unregister the folding state change listening of the folding machine.
   *
   */
  private unregisterFoldableChange(): void {
    if (canIUse('SystemCapability.Window.SessionManager')) {
      try {
        if (display.isFoldable()) {
          HiAiLog.info(TAG, 'ScreenOrientationUtil unregister');
          display.off('foldDisplayModeChange');
        }
      } catch (error) {
        HiAiLog.error(TAG, `unRegisterFold isFoldable exception, error is ` + JSON.stringify(error));
      }
    }
  }

  /**
   * Update the screen landscape/portrait mode.
   *
   */
  private updateDirection(): void {
    let displayInfo = display.getDefaultDisplaySync();
    let displayOrientation: display.Orientation = displayInfo.orientation;
    if (displayOrientation === display.Orientation.PORTRAIT || displayOrientation === display.Orientation.PORTRAIT_INVERTED) {
      this.currentDirection = ConfigurationConstant.Direction.DIRECTION_VERTICAL;
    } else {
      this.currentDirection = ConfigurationConstant.Direction.DIRECTION_HORIZONTAL;
    }
  }

  /**
   * Update dialog size when screen state changes.
   *
   */
  private updateDialogWidth(): void {
    const displayObj: display.Display = display.getDefaultDisplaySync();
    let screenWidth = displayObj.width / displayObj.densityPixels;
    HiAiLog.info(TAG, `ScreenChangeUtil screenWidth is ` + screenWidth);
    if(screenWidth < 600 ) {
        AppStorage.setOrCreate('dialogWidth', 328);
        AppStorage.setOrCreate('progressTextWidth', 184);
    } else {
        AppStorage.setOrCreate('dialogWidth', 400);
        AppStorage.setOrCreate('progressTextWidth', 253);
    }
  }
}

