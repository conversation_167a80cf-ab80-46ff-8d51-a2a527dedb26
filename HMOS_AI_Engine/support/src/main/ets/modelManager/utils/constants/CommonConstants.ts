/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * Define common constants.
 *
 * <AUTHOR>
 */
export default class CommonConstants {
    /**
     * Bundle name.
     */
    public static HIAI_PACKAGE_NAME: string = "com.huawei.hmsapp.hiai"

    /**
     * Ability name.
     */
    public static HIAI_ABILITY_NAME: string = "HiAIServiceAbility"

    /**
     * Initial value of the compatible model version
     */
    public static MODEL_INIT_COMPATIBLE_VERSION: string = "1001001001"

    /**
     * Initial value of the model version
     */
    public static MODEL_INIT_VERSION: string = "1001001001"

    /**
     * Empty string.
     */
    public static EMPTY_STRING: string = ""

    /**
     * Default int value.
     */
    public static DEFAULT_INT: number = 0

    /**
     * Constant int value.
     */
    public static CONSTANT_INT_ONE: number = 1

    /**
     * Constant int value.
     */
    public static CONSTANT_INT_BYTES: number = 1024

    /**
     * Default int value of the max progress.
     */
    public static MAX_PROGRESS: number = 100

    /**
     * The time of network disconnection waiting.
     */
    public static NO_NETWORK_TIMEOUT: number = 60000

    /**
     * The time of onResult waiting when download completed.
     */
    public static NO_RESULT_TIMEOUT: number = 1000

    /**
     * Delay for closing the pop-up window after the download is complete.
     */
    public static DOWNLOADED_DELAY: number = 1000

    /**
     * Delay for toast.
     */
    public static TOAST_DELAY: number = 2500

    /**
     * The space multiplier of evaluating available space when download a model
     */
    public static SPACE_MULTIPLIER: number = 2.5

    /**
     * The max concurrent download tasks number
     */
    public static MAX_CONCURRENT_FOREGROUND = 3

    /**
     * The base id of notification window
     */
    public static BASE_ID: number = 12345678

    /**
     * The base width of PC download notification window
     */
    public static PC_DIALOG_WIDTH: number = 400

    /**
     * The base width of PC download progress window
     */
    public static PC_PROGRESS_WIDTH: number = 253
}
