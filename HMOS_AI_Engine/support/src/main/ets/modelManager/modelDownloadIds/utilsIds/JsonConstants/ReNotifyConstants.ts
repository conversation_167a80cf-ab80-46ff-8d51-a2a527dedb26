/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

/**
 * Model download progress callback information received from the IDS platform.
 *
 * <AUTHOR>
 */
export interface NotifyReceiveParams extends Object {
  session: Session;
  content: Content;
}

export interface Session extends Object {
  serviceName: string;
  messageName: string;
  senderName: string;
  traceId: string;
  messageVersion: string;
  extra?: any
}

export interface Content extends Object {
  contentData: Array<ContentData>
}

export interface ContentData extends Object {
  header: Header;
  payload: Payload;
}

export interface Header extends Object {
  namespace: string;
  name: string;
}

export interface Payload extends Object {
  dataItems: Array<DataItems>;
  domain: string;
  downloadedSize: number;
  progress: number;
  resId: string;
}

export interface DataItems extends Object {
  subscribeId: string;
}


