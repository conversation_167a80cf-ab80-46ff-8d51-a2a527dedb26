/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

/**
 * Query cloud callback information received from the IDS platform.
 *
 * <AUTHOR>
 */
export interface QueryCloudReceiveParams extends Object {
  description: resource;
  failedList: string;
  resultCode: number;
  succeedList: string
}

export interface resource  {
    resId: string;
    resVersion: string;
    domain: string;
    resSize: number;
    resUrl: string;
    resDigest: string;
    resType: string;
    supportSubRes: boolean;
    resPriority: string;
    realMachineTestRes: string;
}


