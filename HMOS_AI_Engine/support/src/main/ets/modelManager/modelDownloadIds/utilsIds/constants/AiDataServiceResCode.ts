/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

/**
 * Define common constants of AiDataService result code.
 *
 * <AUTHOR>
 */
export default class AiDataServiceResCode {
    /**
     * AiDataService success result.
     */
    public static AI_DATA_SERVICE_SUCCESS_RESULT: number = 1

    /**
     * No permission to use AiDataService.
     */
    public static APPLICATION_NO_PERMISSION: number = -103

    /**
     * Incorrect network type, for example, cellular data query IDS test network/development network data.
     */
    public static NETWORK_TYPE_ERROR: number = -117

    /**
     * Silent upgrade is unavailable because the user has not agreed to the privacy policy of <PERSON><PERSON> and has not enabled
     * the WLAN automatic download function.
     */
    public static NO_UPGRADE_PERMISSION: number = -200
}
