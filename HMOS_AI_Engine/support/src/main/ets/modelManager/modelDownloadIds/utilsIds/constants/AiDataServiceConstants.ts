/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

/**
 * Define common constants about AiDataService.
 *
 * <AUTHOR>
 */
export default class AiDataServiceConstants {
    /**
     * AiDataService name.
     */
    public static AI_DATA_SERVICE_NAME: string = "AiDataService"

    /**
     * AiDataService name list.
     */
    public static  AI_DATA_SERVICE_NAME_LIST: string = '["AiDataService"]';

    /**
     * AiDataService namespace.
     */
    public static IDS_NAMESPACE: string = "ResourcePackage";

    /**
     * The name of downloading message.
     */
    public static IDS_MESSAGE_NAME: string = "DownloadResourcePackage";

    /**
     * The name of IDS.
     */
    public static IDS_NAME: string = "Execute";

    /**
     * The name of cancelling message.
     */
    public static IDS_CANCEL_NAME: string = "Cancel";

    /**
     * The name of querying cloud message.
     */
    public static IDS_QUERY_CLOUD_NAME: string = "QueryCloud";

    /**
     * AiDataService common.
     */
    public static COMMON: string = "common";

    /**
     * AiDataService trace ID.
     */
    public static TRACE_ID: string = "123456789";

    /**
     * AiDataService message version.
     */
    public static MESSAGE_VERSION: string = "1.0.0";
}
