/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

/**
 * Information about the model packets to be downloaded sent to the IDS platform.
 *
 * <AUTHOR>
 */
export interface DispatchParams extends Object {
  session: Session;
  content: Content;
}

export interface Session extends Object {
  serviceName: string;
  messageName: string;
  senderName: string;
  traceId: string;
  messageVersion: string;
  extra?: any
}

export interface Content extends Object {
  contentData: Array<ContentData>
}

export interface ContentData extends Object {
  header: Header;
  payload: Payload
}

export interface Header extends Object {
  namespace: string;
  name: string;
}

export interface Payload extends Object {
  subscribeParams?: SubscribeParams;
  callingBundleName?: string;
  resources: Array<Resources>
}

export interface Resources extends Object {
  resId: string;
  domain: string;
  deviceType: string;
  resVersion: string;
  productName: string;
  romVersion: string;
  osVersion: string;
  needShareRes?: boolean;
  realMachineTestRes?: string;
}

export interface SubscribeParams extends Object {
  subscribeId?: string;
}
