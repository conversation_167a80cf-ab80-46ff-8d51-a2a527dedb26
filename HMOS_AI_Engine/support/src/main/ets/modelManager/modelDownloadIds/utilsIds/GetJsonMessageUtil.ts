/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import * as DownloadConstants from "./JsonConstants/DownloadConstants";
import AiDataServiceConstants from './constants/AiDataServiceConstants';
import CommonConstants from '../../utils/constants/CommonConstants';
import { Constants } from '@hms-ai/aiappbase/Index';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';

const TAG: string = "GetJsonMessageUtil";

/**
 * Obtains information about packets transmitted to the IDS platform.
 * @class
 * <AUTHOR>
 */
export default class GetJsonMessageUtil{
  /**
   * Obtains the sent packets of IDS resources, which is used to obtain query and cancel messages.
   *
   * @param { UpgradeConstants.SubscribeInfo } queryInfo - information about the model.
   * @returns { UpgradeConstants.DispatchParams } information about the model.
   */
  public static getMessage(queryInfo: Constants.SubscribeInfo, name: string): DownloadConstants.DispatchParams {
    HiAiLog.info(TAG, `get query or cancel message`);
    let downloadMessage: DownloadConstants.DispatchParams = {
      session: {
        serviceName: AiDataServiceConstants.AI_DATA_SERVICE_NAME,
        messageName: AiDataServiceConstants.IDS_MESSAGE_NAME,
        senderName: queryInfo.bundleName,
        traceId: AiDataServiceConstants.TRACE_ID,
        messageVersion: AiDataServiceConstants.MESSAGE_VERSION
      },
      content: {
        contentData: [
          {
            header: {
              namespace: AiDataServiceConstants.IDS_NAMESPACE,
              name: name
            },
            payload: {
              callingBundleName: CommonConstants.HIAI_PACKAGE_NAME,
              resources: [{
                resId: queryInfo.resId,
                domain: queryInfo.domain,
                deviceType: AiDataServiceConstants.COMMON,
                resVersion: queryInfo.resVersion,
                productName: AiDataServiceConstants.COMMON,
                romVersion: AiDataServiceConstants.COMMON,
                osVersion: AiDataServiceConstants.COMMON,
              }]
            }
          }
        ]
      }
    };
    return downloadMessage;
  }

  /**
   * Obtains the sent packets of IDS download resources.
   *
   * @param { UpgradeConstants.SubscribeInfo } queryInfo - information about the model.
   * @returns { UpgradeConstants.DispatchParams } information about the model to be queried.
   */
  public static getDownloadMessage(queryInfo: Constants.SubscribeInfo): DownloadConstants.DispatchParams {
    HiAiLog.info(TAG, `getDownloadMessage`);
    let downloadMessage: DownloadConstants.DispatchParams = {
      session: {
        serviceName: AiDataServiceConstants.AI_DATA_SERVICE_NAME,
        messageName: AiDataServiceConstants.IDS_MESSAGE_NAME,
        senderName: queryInfo.bundleName,
        traceId: AiDataServiceConstants.TRACE_ID,
        messageVersion: AiDataServiceConstants.MESSAGE_VERSION
      },
      content: {
        contentData: [
          {
            header: {
              namespace: AiDataServiceConstants.IDS_NAMESPACE,
              name: AiDataServiceConstants.IDS_NAME
            },
            payload: {
              subscribeParams: [{
                subscribeId: queryInfo.resId,
              }],
              resources: [{
                resId: queryInfo.resId,
                domain: queryInfo.domain,
                deviceType: AiDataServiceConstants.COMMON,
                resVersion: queryInfo.resVersion,
                productName: AiDataServiceConstants.COMMON,
                romVersion: AiDataServiceConstants.COMMON,
                osVersion: AiDataServiceConstants.COMMON,
                needShareRes: queryInfo.originId ? true : false,
              }]
            }
          }
        ]
      }
    };
    return downloadMessage;
  }
}