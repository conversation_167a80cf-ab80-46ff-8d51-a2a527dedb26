/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import ModelInfoManager, { ModelInfo } from '../../modelDownloadOta/modelDownloadInfo/ModelInfoManager';
import HiAIServiceAbility from '../../../framework/HiAIServiceAbility';
import ModelUpgradeManagerImpl from './ModelDownloadIdsImpl';
import CommonConstants from '../../utils/constants/CommonConstants';
import GetJsonMessageUtil from '../utilsIds/GetJsonMessageUtil';
import AiDataServiceConstants from '../utilsIds/constants/AiDataServiceConstants';
import ModelQuery from '../modelManagerIds/ModelQueryIds';
import { Constants } from '@hms-ai/aiappbase/Index';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { BusinessError } from '@kit.BasicServicesKit';
import ModelTaskManager from '../../modelDownloadOta/ModelTaskManager';
import ModelOperate from '../../modelAgingManager/ModelOperate';
import { ModelDownloadResultCode, ReportResultCode,
    ReportResultCodeMessage } from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { ModelDownloadReportUtil } from '../../utils/ModelManagerReportUtil';
import ModelBaseInfo from '../../modelDownloadOta/modelDownloadInfo/ModelBaseInfo';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';

const TAG: string = "ModelDownloadManagerIds";

/**
 * Manages the task of downloading models through the IDS platform.
 * @class
 * <AUTHOR>
 */
export default class ModelDownloadManagerIds {
    /**
     * ID of the model being downloaded.
     */
    public static resId: string = '';

    /**
     * Information of the model being downloaded.
     */
    public static list: Array<Constants.SubscribeInfo> = new Array();

    /**
     * start download model.
     *
     * @param { string } resId - the model ID.
     * @param { string } domain - the model domain.
     * @param { string } resVersion - the model current version.
     * @param { ModelInfo } modelInfo - the model information.
     */
    static modelDownload(resId: string, domain: string, resVersion: string, modelInfo: ModelInfo): void {
        HiAiLog.info(TAG, `ModelDownloadManager modelDownload start`);
        let isSupportMobileNet: number = 1;
        ModelDownloadManagerIds.resId = resId;
        ModelDownloadManagerIds.list.length = CommonConstants.DEFAULT_INT;
        let newResVersion = resVersion.slice(0, -4) + '0000'; // Initializes the version code. The least significant four bits are 0000.
        ModelDownloadManagerIds.list.push({
            bundleName: CommonConstants.HIAI_PACKAGE_NAME,
            resId: resId,
            domain: domain,
            resVersion: newResVersion,
            isSupportMobileNet: isSupportMobileNet,
            serviceName: CommonConstants.HIAI_ABILITY_NAME
        })

        ModelUpgradeManagerImpl.getInstance(HiAIServiceAbility.globalContext).init(ModelDownloadManagerIds.list)
            .then((res: boolean) => {
                let initResult: boolean = res;
                HiAiLog.info(TAG, `ModelDownloadManager init success :` + initResult)

                ModelUpgradeManagerImpl.getInstance(HiAIServiceAbility.globalContext).subscribe()
                    .then(async (subscribeResult: boolean) => {
                        if(subscribeResult) {
                            HiAiLog.info(TAG, `ModelDownloadManager subscribe success :` + subscribeResult);
                            for (let subscribeInfo of ModelDownloadManagerIds.list) {
                                let queryCloudMessage: string = JSON.stringify(GetJsonMessageUtil.getMessage(subscribeInfo, AiDataServiceConstants.IDS_QUERY_CLOUD_NAME));
                                ModelQuery.queryIdsCloudModel(resId, HiAIServiceAbility.globalContext, queryCloudMessage).then(async (queryResult: boolean)=>{
                                    if(queryResult) {
                                        ModelInfoManager.addModelInfo(resId, modelInfo);
                                        ModelInfoManager.updateDownloadPlatform(resId, false)
                                        let modelBaseInfo: ModelBaseInfo = ModelInfoManager.getModelBaseInfo(resId);
                                        let storePath: string = HiAIServiceAbility.globalContext?.filesDir;
                                        let modelCallbackProxy: IModelDownloadCallback =
                                            await ModelInfoManager.getCallbackProxy(resId);
                                        let checkResult: boolean = await ModelOperate.getInstance()
                                            .checkStorageSpace(modelBaseInfo, storePath);
                                        HiAiLog.info(TAG,`space check result is ${checkResult}`);
                                        if (!checkResult) {
                                            HiAiLog.info(TAG,`Start callback space model is ${resId}`);
                                            modelCallbackProxy.onError(resId, ModelDownloadResultCode.OUT_OF_MEMORY, ReportResultCodeMessage.SPACE_INSUFFICIENT);
                                            let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(resId, 'Space insufficient, failed to download', domain);
                                            let startTime: Date = new Date()
                                            ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.OUT_OF_MEMORY, ReportResultCodeMessage.SPACE_INSUFFICIENT, startTime.getTime());
                                            ModelTaskManager.getInstance().deleteModelDownloadTask(resId);
                                            return;
                                        }
                                        ModelUpgradeManagerImpl.getInstance(HiAIServiceAbility.globalContext).modelDownload(subscribeInfo);
                                    }else {
                                        ModelTaskManager.handleForegroundTaskComplete(resId);
                                    }
                                }).catch(async (e)=>{
                                    ModelTaskManager.getInstance().deleteModelDownloadTask(resId);
                                    let modelCallbackProxy: IModelDownloadCallback =
                                        await ModelInfoManager.getCallbackProxy(resId);
                                    modelCallbackProxy.onError(resId, ModelDownloadResultCode.NO_MODEL, 'There is no model in ids cloud');
                                    HiAiLog.error(TAG, `queryIdsCloudModel error is: ` + JSON.stringify(e));
                                });
                            }
                        }
                    }).catch(async (result: BusinessError) => {
                    ModelInfoManager.updateIsDownloadEndStatus(resId, true)
                    HiAiLog.error(TAG, `ModelDownloadManager subscribe error:` + JSON.stringify(result));
                    if(result.code === 14800028 || 14800029){
                        let modelCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(resId);
                        modelCallbackProxy.onError(resId, ModelDownloadResultCode.OUT_OF_MEMORY, ReportResultCodeMessage.SPACE_INSUFFICIENT);
                        let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(resId, 'Space insufficient, failed to download', domain);
                        let startTime: Date = new Date()
                        ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.OUT_OF_MEMORY, ReportResultCodeMessage.SPACE_INSUFFICIENT, startTime.getTime());
                    }
                    ModelTaskManager.getInstance().deleteModelDownloadTask(resId);
                    return false;
                });
            }).catch(async (result: BusinessError) => {
            HiAiLog.error(TAG, `ModelDownloadManager init error:` + JSON.stringify(result))
            let modelCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(resId);
            modelCallbackProxy.onError(resId, ModelDownloadResultCode.DOWNLOAD_EXCEPTION, result.message);
            ModelTaskManager.getInstance().deleteModelDownloadTask(resId);
            return false;
        });
    }

    /**
     * cancel download model.
     *
     * @param { string } resId - the model ID.
     */
    static cancelModelDownload(resId: string): void {
        HiAiLog.info(TAG, `cancelodelDownload start`);
        ModelInfoManager.updateIsDownloadStartStatus(resId, false)
        ModelUpgradeManagerImpl.getInstance(HiAIServiceAbility.globalContext)
            .cancelModelDownload(ModelDownloadManagerIds.list);
    }
}

