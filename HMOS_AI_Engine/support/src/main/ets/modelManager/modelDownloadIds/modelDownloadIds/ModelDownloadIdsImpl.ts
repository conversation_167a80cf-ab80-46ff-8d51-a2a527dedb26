/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import { NetworkChangeUtil } from '../../utils/NetworkChangeUtil';
import HiAIServiceAbility from '../../../framework/HiAIServiceAbility';
import * as NotifyConstants from "../utilsIds/JsonConstants/ReNotifyConstants";
import ModelDownloadCapability from '../../ModelDownloadCapability';
import AiDataServiceConstants from '../utilsIds/constants/AiDataServiceConstants';
import GetJsonMessageUtil from '../utilsIds/GetJsonMessageUtil';
import ModelQuery from '../modelManagerIds/ModelQueryIds';
import ModelCopy from '../modelManagerIds/ModelCopyIds';
import CommonConstants from '../../utils/constants/CommonConstants';
import ModelDownloadManager from './ModelDownloadIds';
import AiDataServiceResCode from '../utilsIds/constants/AiDataServiceResCode';
import ModelInfoManager from '../../modelDownloadOta/modelDownloadInfo/ModelInfoManager';
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { BaseSdkPresenter } from "@hms-ai/aiBaseSdk/src/main/ets/presenter/BaseSdkPresenter";
import { NetWorkUtil } from '@hms-ai/pdkfull/src/main/ets/utils/NetWorkUtil';
import { ModelDownloadResultCode, ModelUpgradeResultCode, ReportResultCode, ReportResultCodeMessage} from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import { UpgradeManagerBase, Constants, commonContext } from '@hms-ai/aiappbase/Index';
import * as UpgradeConstants from "@hms-ai/aiappbase/src/main/ets/framework/ommanager/upgrademanager/UpgradeConstants";
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import { ModelDownloadType,NetworkType } from '@hms-ai/pdkfull/src/main/ets/modelDownload/ModelDownloadConstant';
import ModelDownloadManagerIds from './ModelDownloadIds';
import { ModelDownloadReportUtil } from '../../utils/ModelManagerReportUtil';
import ModelTaskManager from '../../modelDownloadOta/ModelTaskManager';

const TAG: string = "HiAI_ModelUpgradeImpl";

/**
 * Indicates the implements of model download from IDS platform.
 * @class
 * <AUTHOR>
 */
export default class ModelUpgradeImpl extends UpgradeManagerBase {
  private static domain: number = 0x0000;
  private static context: commonContext;
  private static instance: ModelUpgradeImpl;
  private static networkTimeId: number | undefined;

  /**
   * get single instance.
   *
   * @param { commonContext } context - the ability context.
   * @returns { ModelUpgradeImpl } the instance of ModelUpgradeImpl.
   */
  public static getInstance(context: commonContext): ModelUpgradeImpl {
    ModelUpgradeImpl.instance = ModelUpgradeImpl.instance ?? new ModelUpgradeImpl(context);
    return ModelUpgradeImpl.instance;
  }

  /**
   * get TAG.
   *
   * @returns { string } the TAG information.
   */
  protected getTag(): string{
    return TAG;
  }

  /**
   * get domain.
   *
   * @returns { number } the domain information.
   */
  protected getDomain(): number{
    return ModelUpgradeImpl.domain;
  }

  /**
   * get ability context.
   *
   * @returns { commonContext } the ability context.
   */
  protected getContext(): commonContext{
    return ModelUpgradeImpl.context;
  }

  /**
   * the callback of upgrade.
   *
   * @param { Constants.updateMessage } message - the update message.
   */
  protected onUpdate(message: Constants.updateMessage): void{
    HiAiLog.info(TAG, `onUpdate: message info ===> ${JSON.stringify(message)}`);
  }

  /**
   * start to download model from IDS platform.
   *
   * @params { Constants.SubscribeInfo } subscribeInfoList - the model information list.
   */
  async modelDownload(subscribeInfo: Constants.SubscribeInfo): Promise<void>{
    HiAiLog.info(TAG, `ModelUpgradeImpl modelDownload start`);
    let message: string = JSON.stringify(GetJsonMessageUtil.getDownloadMessage(subscribeInfo));
    let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(subscribeInfo.resId);
    this.downloadThroughSdk(subscribeInfo.resId, HiAIServiceAbility.globalContext, message).then(async (downloadResult: boolean)=>{
      if(downloadResult) { // The download is successful from the IDS platform.
        ModelQuery.queryIdsLocalModel(subscribeInfo).then((idsLocalVersion)=>{
          if (idsLocalVersion) { // The downloaded model information is queried on the IDS.
            let filePath: string = HiAIServiceAbility.globalContext?.filesDir + '/' + subscribeInfo.domain
                + '/' + idsLocalVersion.resId + '/' + idsLocalVersion.resVersion + '.zip';
            ModelCopy.copyIdsLocalModel(subscribeInfo, filePath).then(async (isCopySuccess: boolean)=>{
              HiAiLog.info(TAG,`ids isCopySuccess status is ${isCopySuccess}`)
              if(isCopySuccess) { // Succeeded in copying model from the IDS.
                downloadCallbackProxy.onComplete(idsLocalVersion.resId, idsLocalVersion.resVersion, filePath);
                ModelDownloadReportUtil.updateBaseInfo(ModelDownloadCapability.foreReportBaseInfo,
                  ReportResultCode.SUCCESS, ReportResultCodeMessage.SUCCESS,
                  ModelDownloadCapability.foreDownloadStartTime.getTime());
                ModelDownloadReportUtil.setReportInfoByBundleName(ModelDownloadCapability.foreReportBaseInfo);

                ModelInfoManager.updateIsDownloadSuccessStatus(idsLocalVersion.resId, true);
                ModelInfoManager.updateIsDownloadStartStatus(idsLocalVersion.resId, false);
                ModelInfoManager.updateIsDownloadEndStatus(idsLocalVersion.resId, true);
                ModelTaskManager.handleForegroundTaskComplete(idsLocalVersion.resId);

                NetworkChangeUtil.getInstance().unregisterNetworkChange();
                let isUnSubscribe: boolean = await this.unSubscribe(ModelDownloadManager.list, true);
                HiAiLog.info(TAG, `copy complete isUnsubcribe is: ` + isUnSubscribe);
              } else { // Failed in copying model from the IDS.
                ModelTaskManager.handleForegroundTaskComplete(idsLocalVersion.resId);
                downloadCallbackProxy.onError(idsLocalVersion.resId, ModelDownloadResultCode.COPY_FILE_FAILED, 'copy file failed');
              }
            }).catch((e)=>{
              HiAiLog.error(TAG, `copyIdsLocalModel error is: ` + JSON.stringify(e.message));
              ModelTaskManager.handleForegroundTaskComplete(idsLocalVersion.resId);
            });
          }
        }).catch((e)=>{
          HiAiLog.error(TAG, `queryIdsLocalModel error is: ` + JSON.stringify(e));
        });
      } else { // The download is fail from the IDS platform.
        let isUnSubscribe: boolean = await this.unSubscribe(ModelDownloadManager.list, true);
        HiAiLog.info(TAG, `download failed isUnsubcribe is: ` + isUnSubscribe);
      }
    }).catch((e)=>{
      HiAiLog.error(TAG, `downloadThroughSdk error is: ` + JSON.stringify(e));
    });
  }

  /**
   * start to cancel download from IDS platform.
   *
   * @params { Constants.SubscribeInfo } subscribeInfoList - the model information list.
   */
  async cancelModelDownload(subscribeInfoList: Array<Constants.SubscribeInfo>): Promise<void> {
    HiAiLog.info(TAG, `cancelModelDownload`);

    for (let subscribeInfo of subscribeInfoList){
      ModelInfoManager.updateIsDownloadEndStatus(subscribeInfo.resId, true);
      let isUnSubscribeNew: boolean = await this.unSubscribe(subscribeInfoList, true);
      HiAiLog.info(TAG, `cancel isUnsubcribe is: ` + isUnSubscribeNew);

      let list: Array<Constants.SubscribeInfo> = new Array();
      list.push({
        bundleName: subscribeInfo.bundleName,
        resId: subscribeInfo.resId,
        domain: subscribeInfo.domain,
        resVersion: ModelQuery.cloudVersion,
        isSupportMobileNet: CommonConstants.DEFAULT_INT,
        serviceName: CommonConstants.HIAI_ABILITY_NAME
      })
      let cancelMessage: string = JSON.stringify(GetJsonMessageUtil.getMessage(list[0], AiDataServiceConstants.IDS_CANCEL_NAME))
      HiAiLog.info(TAG, `ModelUpgradeImpl cancel message :` + JSON.stringify(cancelMessage))
      this.cancelDownloadThroughSdk(subscribeInfo.resId, HiAIServiceAbility.globalContext, cancelMessage);
      NetworkChangeUtil.getInstance().unregisterNetworkChange();
    }
  }

  /**
   * Send a request to the IDS platform for cancelling download.
   *
   * @params { string } resId - the model ID.
   * @params { commonContext } context - the ability context.
   * @params { string } dispatchParams - information about the model whose download needs to be canceled.
   * @returns { Promise<boolean> } the value true indicates that the cancel download is successful, and the value false indicates that the cancel download fails.
   */
  cancelDownloadThroughSdk(resId: string, context: commonContext, dispatchParams: string): Promise<boolean> {
    HiAiLog.info(TAG, `cancelDownloadThroughSdk`);

    let cancelDownloadPromise =  new Promise<boolean>(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();

      let connectResult = await ModelQuery.connectAiDataService(resId, context);
      HiAiLog.info(TAG, `connect AiDataService Result: ` + connectResult);
      upgradePresenter.dispatchMessage(AiDataServiceConstants.AI_DATA_SERVICE_NAME, dispatchParams, {
        onResult: (retCode: number, callbackResult: string) => {
          HiAiLog.info(TAG, `cancelDownloadThroughSdk onResult: ${callbackResult}`);
          if (retCode === AiDataServiceResCode.AI_DATA_SERVICE_SUCCESS_RESULT) {
            HiAiLog.info(TAG, `cancelDownloadThroughSdk onResult success`);
            resolve(true);
          } else {
            resolve(false);
          }
        },
        onNotify: async (eventResult: string) => {
          HiAiLog.info(TAG, `ModelUpgradeImpl onNotify`+ eventResult);
        }
      });
    })
    return cancelDownloadPromise;
  }

  /**
   * Send a request to the IDS platform for downloading cloud resources.
   *
   * @params { string } resId - the model ID.
   * @params { commonContext } context - the ability context.
   * @params { string } dispatchParams - information about the model to be downloaded.
   * @returns { Promise<boolean> } the value true indicates that the download is successful, and the value false indicates that the download fails.
   */
  downloadThroughSdk(resId: string, context: commonContext, dispatchParams: string): Promise<boolean> {
    HiAiLog.info(TAG, `DownloadThroughSdk`);

    let downloadPromise =  new Promise<boolean>(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();

      let connectResult = await ModelQuery.connectAiDataService(resId, context);
      HiAiLog.info(TAG, `download connect AiDataService Result: ` + connectResult);
      if(!connectResult) {
        resolve(false);
      }

      upgradePresenter.dispatchMessage(AiDataServiceConstants.AI_DATA_SERVICE_NAME, dispatchParams, {
        onResult: async (resCode: number, callbackResult: string) => {
          let downloadResultHandle: boolean = await this.downloadResultHandle(resCode, callbackResult);
          HiAiLog.info(TAG, `downloadResultHandle : ` + downloadResultHandle);
          resolve(downloadResultHandle);
        },
        onNotify: async (eventResult: string) => {
          HiAiLog.info(TAG, 'onNotify eventResult is: ' + eventResult);
          try {
            let notifyResult: NotifyConstants.NotifyReceiveParams = JSON.parse(eventResult);
            let resId: string = notifyResult.content.contentData[CommonConstants.DEFAULT_INT].payload.resId;
            let scheduleInfo: number =  notifyResult.content.contentData[CommonConstants.DEFAULT_INT].payload.progress;
            let formatScheduleInfo = Math.round(scheduleInfo);

            let isDownloadEnd: boolean = ModelInfoManager.getIsDownloadEndStatus(resId);
            let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(resId);
            if(!isDownloadEnd) {
              ModelInfoManager.updateIsDownloadStartStatus(resId, true);
              downloadCallbackProxy.onSchedule(resId, formatScheduleInfo + "%");
              let isForegroundDownload: boolean = ModelInfoManager.getIsForeGroundDownload(resId);
              if(isForegroundDownload) { // when the foreground downloads
                ModelInfoManager.updateDownloadProgress(resId, formatScheduleInfo)
              }

              if(ModelUpgradeImpl.networkTimeId) {
                clearTimeout(ModelUpgradeImpl.networkTimeId);
                ModelUpgradeImpl.networkTimeId = undefined;
              }

              switch(formatScheduleInfo) {
                case CommonConstants.MAX_PROGRESS : { // download completed
                  ModelInfoManager.updateIsDownloadEndStatus(resId, true);
                  if (ModelUpgradeImpl.networkTimeId) {
                    try {
                      HiAiLog.info(TAG,`Try clear networkTimeId`)
                      clearTimeout(ModelUpgradeImpl.networkTimeId);
                      ModelUpgradeImpl.networkTimeId = undefined;
                    } catch (e) {
                      HiAiLog.error(TAG,`Failed to clear networkTimeId cause :${e.message}`)
                    }
                  }
                  let timeId = setTimeout(() => {
                    clearTimeout(timeId);
                    HiAiLog.info(TAG, `DownloadWindowManager onResult time end`);
                    resolve(true);
                  }, CommonConstants.NO_RESULT_TIMEOUT);
                  HiAiLog.info(TAG,`isDownloadEnd max progress status is ${ModelInfoManager.getIsDownloadEndStatus(resId)}`)
                  return
                }
                default:
                  if (ModelDownloadCapability.modelDownloadType === ModelDownloadType.MODEL_BACK_DOWNLOAD && !isDownloadEnd) { // when the background downloads and not completed
                    if(ModelUpgradeImpl.networkTimeId) {
                      clearTimeout(ModelUpgradeImpl.networkTimeId);
                      ModelUpgradeImpl.networkTimeId = undefined;
                    }
                    ModelUpgradeImpl.networkTimeId = setTimeout(() => {
                      HiAiLog.info(TAG, `Network disconnect, time out is 60000`);
                      clearTimeout(ModelUpgradeImpl.networkTimeId);
                      ModelUpgradeImpl.networkTimeId = undefined;
                      this.cancelModelDownload(ModelDownloadManager.list);
                      const isDownloadEndNow = ModelInfoManager.getIsDownloadEndStatus(resId);
                      if(!this.checkNetworkType(ModelDownloadCapability.modelDownloadConfig.networkType ?? NetworkType.WLAN, ModelDownloadCapability.backgroundModelDownloadInfo.resId, downloadCallbackProxy)) {
                        downloadCallbackProxy.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "The network is not connected.");
                      } else if(!isDownloadEndNow){
                        HiAiLog.info(TAG,`isDownloadEnd status is ${isDownloadEndNow}`)
                        downloadCallbackProxy.onError(resId, ModelDownloadResultCode.DOWNLOAD_EXCEPTION, "Download exception.");
                      }
                      resolve(false);
                    }, CommonConstants.NO_NETWORK_TIMEOUT);
                  }
              }
            }
          } catch (e) {
            HiAiLog.error(TAG, 'IDS notify, download eventResult cannot be parsed, eventResult is :' + eventResult);
          }
        }
      });
    })
    return downloadPromise;
  }

  /**
   * Check if the current network type matches the required network type for download
   *
   * @param { number } networkType - The required network type (0 for WLAN only, 1 for any network)
   * @param { string } resId - The model ID for error reporting
   * @param { IModelDownloadCallback } downloadCallback - The download callback
   * @returns { boolean } true if network type is valid, false otherwise
   */
  private checkNetworkType(networkType: number, resId: string, downloadCallback: IModelDownloadCallback): boolean {
    if (!NetWorkUtil.isConnectNetwork()) {
      // downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "No network connection available.");
      return false;
    }

    if (networkType === NetworkType.WLAN) {
      if (!NetWorkUtil.isWifiNetwork()) {
        // downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "WiFi is not connected. Only WiFi is allowed for this download.");
        return false;
      }
    } else if (networkType === NetworkType.ANY) {
      if (!NetWorkUtil.isWifiNetwork() && !NetWorkUtil.isCellularNetwork()) {
        // downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "Neither WiFi nor cellular network is connected.");
        return false;
      }
    }
    return true;
  }

  /**
   * Handle the callback information of the download result.
   *
   * @params { number } resCode - the callback result code.
   * @params { string } callbackResult - the detail information of callback.
   * @returns { Promise<boolean> } the value true indicates that the model exists on the IDS cloud.
   */
  async downloadResultHandle(resCode: number, callbackResult: string): Promise<boolean> {
    HiAiLog.info(TAG, `downloadResultHandle resCode is: ` + resCode + ", callbackResult is: " + callbackResult);
    let resId: string = ModelDownloadManagerIds.resId;
    let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(resId);
    let isDownloadSuccess: boolean = ModelInfoManager.getIsDownloadSuccessStatus(resId);

    switch (resCode) {
      case AiDataServiceResCode.AI_DATA_SERVICE_SUCCESS_RESULT: {
        try {
          let result: UpgradeConstants.Reply = JSON.parse(callbackResult);
          let resultCode = result?.resultCode;
          if (resultCode === AiDataServiceResCode.AI_DATA_SERVICE_SUCCESS_RESULT) {
            ModelInfoManager.updateIsDownloadStartStatus(resId, true);
            return true;
          } else {
            let errorMessage: string = result.description;
            ModelInfoManager.updateIsDownloadEndStatus(resId, true);
            NetworkChangeUtil.getInstance().unregisterNetworkChange();
            downloadCallbackProxy.onError(resId, resultCode, errorMessage)
            return false;
          }
        } catch (e) {  // JSON parsing exception
          HiAiLog.error(TAG, 'IDS result, download callbackResult cannot be parsed, callbackResult is :' + callbackResult);
          ModelInfoManager.updateIsDownloadEndStatus(resId, true);
          NetworkChangeUtil.getInstance().unregisterNetworkChange();
          downloadCallbackProxy.onError(resId, resCode, callbackResult);
          return false;
        }
      }
      default:
        ModelInfoManager.updateIsDownloadEndStatus(resId, true);
        if(!isDownloadSuccess) {
          NetworkChangeUtil.getInstance().unregisterNetworkChange();
          downloadCallbackProxy.onError(resId, ModelUpgradeResultCode.APPLICATION_NO_PERMISSION, 'No permission for the current operation')
        }
        return false;
    }
  }
}