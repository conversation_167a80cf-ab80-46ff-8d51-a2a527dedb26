/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import HiAIServiceAbility from '../../../framework/HiAIServiceAbility';
import CommonConstants from '../../utils/constants/CommonConstants';
import { Constants } from '@hms-ai/aiappbase/Index';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import UpgradeSdkManager from '@hms-ai/aiappbase/src/main/ets/framework/ommanager/upgrademanager/UpgradeSdkManager';
import MessageUtil from '@hms-ai/aiappbase/src/main/ets/framework/ommanager/upgrademanager/MessageUtil';
import { BusinessError } from '@kit.BasicServicesKit';
import fs from '@ohos.file.fs';

const TAG: string = "ModelCopyIds";

/**
 * Indicates the implements of copying a model from IDS platform.
 * @class
 * <AUTHOR>
 */
export default class ModelCopyIds {
  /**
   * Copy model from the IDS platform.
   *
   * @param { Constants.SubscribeInfo } subscribeInfo - information about the model to be subscribed to.
   * @param { string } filePath - information about the model to be subscribed to.
   * @returns { Promise<boolean> }  the value true indicates that the copying is successful, and the value false indicates that the copying fails.
   */
  public static async copyIdsLocalModel(subscribeInfo: Constants.SubscribeInfo, filePath: string): Promise<boolean> {
    HiAiLog.info(TAG, `copy resource file from IDS`);
    let result: boolean = false;
    let fd: number;
    await UpgradeSdkManager.CopyThroughSdk(HiAIServiceAbility.globalContext, JSON.stringify(MessageUtil.getCopyMessage(subscribeInfo))).then((res:number) => {
      try {
        let isMakeFile: boolean = ModelCopyIds.makefile(filePath);
        if(isMakeFile) {
          fs.copyFileSync(res, filePath);
          fd = res;
          result = true;
          HiAiLog.info(TAG, `copy resource file success`);
        }
      } catch(error) {
        let err: BusinessError = error as BusinessError;
        HiAiLog.error(TAG, `copy file failed with err code: ${err.code}, err message:${err.message}`);
      }
    }).catch((err:BusinessError) => {
      HiAiLog.error(TAG, `get file descriptor from IDS failed with err code: ${err.code}. err message: ${err.message}`);
    }).finally(() => {
      if (fd) {
        fs.closeSync(fd);
      }
    })
    return result;
  }

  /**
   * Create a directory in the framework sandbox path to store the downloaded model file.
   *
   * @param { string } filePath - path of the file to be created.
   * @returns { boolean } the value true indicates that the creation is successful, and the value false indicates that the creation fails.
   */
  public static makefile(filePath: string): boolean {
    HiAiLog.info(TAG, `makefile start`);
    let filePathArray = filePath.split('/');
    let subFilePathArray = filePathArray.slice(CommonConstants.DEFAULT_INT, filePathArray.length - CommonConstants.CONSTANT_INT_ONE).join('/');
    HiAiLog.info(TAG, `makefile subFilePathArray is: ` + subFilePathArray);
    try {
      if(!fs.accessSync(subFilePathArray)){
        fs.mkdirSync(subFilePathArray, true);
        HiAiLog.info(TAG, `makefile create directory success`);
      }
    } catch (error) {
      let err: BusinessError =error as BusinessError;
      HiAiLog.error(TAG, `makefile create directory failed with err code: ${err.code}, err message:${err.message}`);
      return false;
    }
    return true;
  }
}