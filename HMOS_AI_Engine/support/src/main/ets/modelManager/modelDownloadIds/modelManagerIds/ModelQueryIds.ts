/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import HiAIServiceAbility from '../../../framework/HiAIServiceAbility';
import * as QueryCloudConstants from "../utilsIds/JsonConstants/ReQueryCloudConstants";
import AiDataServiceConstants from '../utilsIds/constants/AiDataServiceConstants';
import AiDataServiceResCode from '../utilsIds/constants/AiDataServiceResCode';
import CommonConstants from '../../utils/constants/CommonConstants';
import ModelInfoManager from '../../modelDownloadOta/modelDownloadInfo/ModelInfoManager';
import ModelDownloadManagerIds from '../modelDownloadIds/ModelDownloadIds';
import { Constants, commonContext } from '@hms-ai/aiappbase/Index'
import { IModelDownloadCallback } from '@hms-ai/pdkfull/src/main/ets/modelDownload/IModelDownloadCallback';
import { BaseSdkPresenter } from "@hms-ai/aiBaseSdk/src/main/ets/presenter/BaseSdkPresenter";
import { ModelDownloadResultCode, ReportResultCode} from '@hms-ai/pdkfull/src/main/ets/utils/ResCode';
import HiAiLog from '@hms-ai/pdkfull/src/main/ets/utils/log/HiAiLog';
import UpgradeSdkManager from '@hms-ai/aiappbase/src/main/ets/framework/ommanager/upgrademanager/UpgradeSdkManager';
import * as UpgradeConstants from "@hms-ai/aiappbase/src/main/ets/framework/ommanager/upgrademanager/UpgradeConstants";
import MessageUtil from '@hms-ai/aiappbase/src/main/ets/framework/ommanager/upgrademanager/MessageUtil';
import { BusinessError } from '@kit.BasicServicesKit';
import { ExtraInfo } from '../../modelDownloadOta/modelDownloadInfo/ModelBaseInfo';
import ModelTaskManager from '../../modelDownloadOta/ModelTaskManager';
import ModelDownloadCapability from '../../ModelDownloadCapability';
import { BaseInfo } from '@hms-ai/pdkfull/src/main/ets/report/MaintenanceReportInfo';
import { ModelDownloadReportUtil } from '../../utils/ModelManagerReportUtil';

const TAG: string = "ModelQueryIds";

/**
 * Indicates the implements of model querying from IDS platform.
 * @class
 * <AUTHOR>
 */
export default class ModelQueryIds {
  /**
   * Latest model version information queried on the IDS cloud.
   */
  public static cloudVersion: string = '';

  /**
   * Send a request to the IDS for query cloud resources.
   *
   * @params { string } resId - the model ID.
   * @params { commonContext } context - the ability context.
   * @returns { Promise<string> } whether the connection is successful, the value true indicates that the connection is successful, and the value false indicates that the connection fails.
   */
  public static connectAiDataService(resId: string, context: commonContext): Promise<boolean> {
    HiAiLog.info(TAG, `connect AiDataService`);

    let connectPromise =  new Promise<boolean>(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();
      await upgradePresenter.create(context, AiDataServiceConstants.AI_DATA_SERVICE_NAME_LIST, {
        onResult: (retCode: number, callbackResult: string) => {
          HiAiLog.info(TAG, `connect AiDataService onResult: ${retCode}, callbackResult: ${callbackResult}`);
          if (retCode !== AiDataServiceResCode.AI_DATA_SERVICE_SUCCESS_RESULT) {
            ModelInfoManager.updateIsDownloadEndStatus(resId, true);
            resolve(false);
          }
          resolve(true);
        },
        onNotify: (eventResult: string) => {
          HiAiLog.info(TAG, `connect AiDataService onNotify: ${eventResult}`);
        }
      });
    })
    return connectPromise;
  }

  /**
   * Send a request to the IDS for query cloud resources.
   *
   * @params { string } resId - the model ID.
   * @params { commonContext } context - the ability context.
   * @params { string } dispatchParams - information about the model to be queried.
   * @returns { Promise<string> } Whether the query is successful, the value true indicates that the query is successful, and the value false indicates that the download fails.
   */
  public static queryIdsCloudModel(resId: string, context: commonContext, dispatchParams: string): Promise<boolean> {
    HiAiLog.info(TAG, `query IDS cloud resource info`);
    let downloadPromise =  new Promise<boolean>(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();
      let connectResult = await ModelQueryIds.connectAiDataService(resId, context);
      HiAiLog.info(TAG, `connect AiDataService Result: ` + connectResult);
      upgradePresenter.dispatchMessage(AiDataServiceConstants.AI_DATA_SERVICE_NAME, dispatchParams, {
        onResult: async (resCode: number, callbackResult: string) => {
          let queryResultHandle: boolean = await ModelQueryIds.queryResultHandle(resCode, callbackResult);
          HiAiLog.info(TAG, `queryResultHandle : ` + queryResultHandle);
          resolve(queryResultHandle);
        },
        onNotify: async (eventResult: string) => {
          HiAiLog.info(TAG, `ModelQueryIds onNotify`+ eventResult);
        }
      });
    })
    return downloadPromise;
  }

  /**
   * Query version code of the model downloaded from the IDS platform.
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo - information about the model to be query.
   * @returns { Promise<UpgradeConstants.resVersionInfo | void> } the latest version code of model is returned.
   */
  public static async queryIdsLocalModel(subscribeInfo: Constants.SubscribeInfo): Promise<UpgradeConstants.resVersionInfo | void> {
    HiAiLog.info(TAG, `query IDS local resource info`);
    let resVersion: UpgradeConstants.resVersionInfo | undefined = undefined;
    await UpgradeSdkManager.QueryLocalThroughSdk(HiAIServiceAbility.globalContext,
      JSON.stringify(MessageUtil.getQueryLocalMessage(subscribeInfo))).then((res: UpgradeConstants.QueryData[]) => {
      if (res.length) {
        let resData: UpgradeConstants.QueryData = res[CommonConstants.DEFAULT_INT]
        resVersion = {
          resId: resData.resId,
          resVersion: resData.resVersion,
          domain: resData.domain,
          updateTime: resData.updateTime,
          bundleName: subscribeInfo.bundleName,
          originId: subscribeInfo.originId,
          version: resData.metaDataVersion,
          realMachineTestRes: (resData.realMachineTestRes === "true") ? true : false
        }
        HiAiLog.info(TAG, `query local resource success: ${JSON.stringify(resVersion)}`);
      } else {
        ModelTaskManager.getInstance().deleteModelDownloadTask(subscribeInfo.resId);
        ModelTaskManager.handleForegroundTaskComplete(subscribeInfo.resId);
        let modelCallbackProxy: IModelDownloadCallback = ModelDownloadCapability.foreDownloadCallbackProxy;
        let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(subscribeInfo.resId, `Query IDS local resource`, subscribeInfo.domain);
        let startTime: Date = new Date()
        ModelDownloadReportUtil.updateBaseInfo(baseInfo, ReportResultCode.QUERY_LOCAL_FAILED, `Query IDS local resource failed`, startTime.getTime());
        modelCallbackProxy.onError(subscribeInfo.resId,ModelDownloadResultCode.QUERY_LOCAL_FAILED,`There is no IDS local resource.`)
        HiAiLog.error(TAG, `no resource info in IDS, please subscribe first`);
      }
    }).catch((err: BusinessError) => {
      let modelCallbackProxy: IModelDownloadCallback = ModelDownloadCapability.foreDownloadCallbackProxy;
      let baseInfo: BaseInfo = ModelDownloadReportUtil.setBaseLogInfo(subscribeInfo.resId, `Query IDS local resource`, subscribeInfo.domain);
      let startTime: Date = new Date();
      ModelDownloadReportUtil.updateBaseInfo(baseInfo, err.code, `${err.message}`, startTime.getTime());
      modelCallbackProxy.onError(subscribeInfo.resId,err.code,`${err.message}`)
      HiAiLog.error(TAG, `query local failed with err code:${err.code}, err message: ${err.message}`);
      ModelTaskManager.getInstance().deleteModelDownloadTask(subscribeInfo.resId);
      ModelTaskManager.handleForegroundTaskComplete(subscribeInfo.resId);
    })
    return resVersion;
  }

  /**
   * Handle the callback information of the query.
   *
   * @params { number } resCode - the callback result code.
   * @params { string } callbackResult - the detail information of callback.
   * @returns { Promise<boolean> } whether the query result is successful, the value true indicates that the model exists on the IDS cloud.
   */
  public static async queryResultHandle(resCode: number, callbackResult: string): Promise<boolean> {
    HiAiLog.info(TAG, `queryResultHandle resCode is: ` + resCode + ", callbackResult is: " + callbackResult);
    let downloadCallbackProxy: IModelDownloadCallback = await ModelInfoManager.getCallbackProxy(ModelDownloadManagerIds.resId);
    let isExisted: boolean = ModelInfoManager.getIsExistedStatus(ModelDownloadManagerIds.resId);
    switch (resCode) {
      case AiDataServiceResCode.NETWORK_TYPE_ERROR: { // error code is -117
        HiAiLog.error(TAG, 'network type error');
        downloadCallbackProxy.onError(ModelDownloadManagerIds.resId, resCode, callbackResult);
        ModelInfoManager.updateIsDownloadEndStatus(ModelDownloadManagerIds.resId, true);
        return false;
      }
      case AiDataServiceResCode.AI_DATA_SERVICE_SUCCESS_RESULT: { // success
        try{
          let parsedData = JSON.parse(callbackResult);
          if (parsedData.resultCode !== AiDataServiceResCode.AI_DATA_SERVICE_SUCCESS_RESULT) {
            downloadCallbackProxy.onError(ModelDownloadManagerIds.resId, parsedData.resultCode, parsedData.description)
            return false;
          }
          if (parsedData.description && parsedData.description.trim() !== "{}") {
            HiAiLog.info(TAG, 'description not empty');
            let description = JSON.parse(parsedData.description);
            let dynamicKey: string = Object.keys(description)[CommonConstants.DEFAULT_INT];
            let resource: QueryCloudConstants.resource = description[dynamicKey];
            this.cloudVersion = resource.resVersion
            HiAiLog.info(TAG, `cloudVersion: ` + this.cloudVersion);

            if (!isExisted) {
              downloadCallbackProxy.onStart(resource.resId);
              let modelSize: number = Math.round(resource.resSize / (CommonConstants.CONSTANT_INT_BYTES * CommonConstants.CONSTANT_INT_BYTES));
              let modelExtraInfo: ExtraInfo = {
                modelSize: modelSize,
                modelID: resource.resId,
                modelVersion: resource.resVersion
              };
              let modelExtraInfoArray: Array<ExtraInfo> = [];
              modelExtraInfoArray.push(modelExtraInfo);
              let modelBaseInfo = ModelInfoManager.getModelBaseInfo(resource.resId);
              modelBaseInfo.setExtraInfo(modelExtraInfoArray[CommonConstants.DEFAULT_INT]);
              ModelInfoManager.updateIsExistedStatus(resource.resId, true);
            }
            return true;
          } else {
            HiAiLog.error(TAG, 'description is empty');
            ModelInfoManager.updateIsDownloadEndStatus(ModelDownloadManagerIds.resId, true);
            downloadCallbackProxy.onError(ModelDownloadManagerIds.resId, ModelDownloadResultCode.NO_MODEL, "The resource does not exist in cloud.")
            return false;
          }
        } catch (error) {
            HiAiLog.error(TAG, 'IDS query result, callbackResult cannot be parsed, callbackResult is :' + callbackResult);
            downloadCallbackProxy.onError(ModelDownloadManagerIds.resId, resCode, callbackResult);
            ModelInfoManager.updateIsDownloadEndStatus(ModelDownloadManagerIds.resId, true);
            return false;
        }
      }
      default: // error code is -103、-200、-121
        try {
          let parsedData = JSON.parse(callbackResult);
          downloadCallbackProxy.onError(ModelDownloadManagerIds.resId, ModelDownloadResultCode.NO_NETWORK_STATUS, parsedData.description);
          ModelInfoManager.updateIsDownloadEndStatus(ModelDownloadManagerIds.resId, true);
          return false;
        } catch (error) {
          HiAiLog.error(TAG, 'IDS query failed, callbackResult cannot be parsed, callbackResult is :' + callbackResult);
          downloadCallbackProxy.onError(ModelDownloadManagerIds.resId, ModelDownloadResultCode.NO_NETWORK_STATUS, callbackResult);
          ModelInfoManager.updateIsDownloadEndStatus(ModelDownloadManagerIds.resId, true);
          return false;
        }
    }
  }
}