{"module": {"name": "support", "type": "feature", "srcEntrance": "./ets/Application/HiAiServiceStage.ts", "description": "$string:entry_desc", "process": "com.huawei.hmsapp.hiai.core", "deviceTypes": ["default", "tablet", "2in1", "wearable"], "requestPermissions": [{"name": "ohos.permission.INTERNET", "reason": "$string:INTERNET_permission_reason"}, {"name": "ohos.permission.GET_NETWORK_INFO", "reason": "$string:GET_NETWORK_INFO_permission_reason"}, {"name": "ohos.permission.SYSTEM_FLOAT_WINDOW", "reason": "$string:GET_SYSTEM_FLOAT_WINDOW_permission_reason"}, {"name": "ohos.permission.PLUGIN_UPDATE", "reason": "$string:GET_SYSTEM_FLOAT_WINDOW_permission_reason"}, {"name": "ohos.permission.RECEIVE_UPDATE_MESSAGE", "reason": "$string:GET_SYSTEM_FLOAT_WINDOW_permission_reason"}, {"name": "ohos.permission.GET_BUNDLE_INFO", "reason": "$string:GET_BUNDLE_INFO_permission_reason"}, {"name": "ohos.permission.START_SYSTEM_DIALOG", "reason": "$string:START_SYSTEM_DIALOG_permission_reason"}], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "extensionAbilities": [{"name": "HiAIServiceAbility", "srcEntrance": "./ets/framework/HiAIServiceAbility.ts", "type": "service", "exported": true, "permissions": ["ohos.permission.ACCESS_AI_ABILITY"], "metadata": [{"name": "ai_capability_id", "value": "6000"}]}, {"name": "staticSubscriber", "srcEntry": "./ets/modelManager/modelDownloadOta/StaticSubscriber.ts", "description": "boot start common event subscriber extension ability", "icon": "$media:icon", "label": "$string:MainAbility_label", "type": "staticSubscriber", "exported": true, "metadata": [{"name": "ohos.extension.staticSubscriber", "resource": "$profile:subscribe"}]}, {"name": "ModelDownloadUIExtensionAbility", "srcEntry": "./ets/framework/ability/ModelDownloadUIExtensionAbility.ts", "label": "$string:UIExtAbility_label", "description": "need use UIExtAbility to create download window", "type": "sys/commonUI", "exported": true, "extensionProcessMode": "bundle"}]}}