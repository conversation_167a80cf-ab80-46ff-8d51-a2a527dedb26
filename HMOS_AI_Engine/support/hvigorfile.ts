import { getHvigorNode } from '@ohos/hvigor'
import { hapTasks } from '@ohos/hvigor-ohos-plugin'
import * as path from 'path'
import { executeOnlineSign } from '../hm_sign/sign.js'

const mModule = getHvigorNode(__filename)
const ohosPlugin = hapTasks(mModule)

const curTargetName = "default"
const targetNames = ['default', 'ohosTest'];
const mModuleName = mModule.getName()
const projectRootPath = process.cwd()

// 若是feature模块签名，此处填写依赖的entry模块名称
const entryName = 'entry';

function onlineSignTask(targetName): void {
    const onlineSignHapTaskName = `${targetName}@onlineSignHap`;
    // 注册在线签名任务和创建任务依赖
    const onlineSignTask = mModule.task(() => {
        // 构建的未签名的hap的输出根目录
        const moduleBuildOutputDir = path.resolve(projectRootPath, mModuleName, 'build/default/outputs/' + targetName + '/')

        // 未签名的hap包路径
        const inputFile = path.resolve(moduleBuildOutputDir, `${mModuleName}${entryName ? '-' + entryName : ''}-${targetName}-unsigned.hap`)
        // 签名后的hap包路径
        const outputFile = path.resolve(moduleBuildOutputDir, `${mModuleName}${entryName ? '-' + entryName : ''}-${targetName}-signed.hap`)

        executeOnlineSign(inputFile, outputFile)
    }, onlineSignHapTaskName).dependsOn(`${targetName}@PackageHap`)

    // 使用在线签名,可以把离线签名任务disable掉
    if (onlineSignTask.getEnabled()) {
        mModule.getTaskByName(`${targetName}@SignHap`).setEnabled(false)
    }
    return onlineSignHapTaskName;
}

targetNames.forEach((targetName) => {
    // 如果存在签名任务,则创建在线签名任务并挂接在assembleHap任务上
    if (mModule.getTaskByName(`${targetName}@SignHap`)) {
        // 将在线签名任务挂接在assembleHap任务上,如果需要在IDE上使用,assembleHap任务是固定的
        mModule.getTaskByName('assembleHap').dependsOn(onlineSignTask(targetName));
    }
})

module.exports = {
    ohos: ohosPlugin
}