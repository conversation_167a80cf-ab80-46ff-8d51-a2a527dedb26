{
  "apiType": 'stageMode',
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": [
              "./obfuscation-rules.txt"
            ]
          }
        }
      }
    },
  ],
  "buildOption": {
    "napiLibFilterOption": {
      "pickFirsts": [
        "**/libcurl.so",
        "**/libpaf-adapter.so",
        "**/libnetwork-common.so",
        "**/libnetwork-restclient-service.so",
        "**/libnetwork-api.so",
        "**/libnetwork-restclient-service.so",
        "**/libnetworkkit-grs.so",
        "**/libnetworkkit-common.so",
        "**/libhuawei-securec.so",
        "**/libgrs-service.so",
        "**/libnetworkkit-grs.so",
        "**/libhuawei-securec.so",
        "**/libbidl.so",
        "**/libxpsf-service-manager.so",
        "**/libxpsf-cpp-core.so",
        "**/libsecurec.so",
        "**/libprotobuf-lite.so",
        "**/libnapi-adapter.so",
        "**/libxpsf-service-manager.so",
        "**/libxpsf-cpp-core.so",
        "**/libsecurec.so",
        "**/libprotobuf-lite.so",
        "**/libnapi-adapter.so",
        "**/libha_api.so",
        "**/libpaf_trace.so",
        "**/libha_api.so"
      ]
    }
  },
  "entryModules": [
    "entry"
  ],
  "targets": [
    {
      "name": "default"
    },
    {
      "name": "ohosTest",
    }
  ]
}