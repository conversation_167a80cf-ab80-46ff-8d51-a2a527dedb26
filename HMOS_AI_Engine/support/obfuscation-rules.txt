# Define project specific obfuscation rules here.
# You can include the obfuscation configuration files in the current module's build-profile.json5.
#
# For more details, see
#   https://gitee.com/openharmony/arkcompiler_ets_frontend/blob/master/arkguard/README.md

# Obfuscation options:
# -disable-obfuscation: disable all obfuscations
# -enable-property-obfuscation: obfuscate the property names
# -enable-toplevel-obfuscation: obfuscate the names in the global scope
# -compact: remove unnecessary blank spaces and all line feeds
# -remove-log: remove all console.* statements
# -print-namecache: print the name cache that contains the mapping from the old names to new names
# -apply-namecache: reuse the given cache file

# Keep options:
# -keep-property-name: specifies property names that you want to keep
# -keep-global-name: specifies names that you want to keep in the global scope

-enable-toplevel-obfuscation
-enable-property-obfuscation
-enable-export-obfuscation
-remove-log
-remove-comments

-keep-property-name
onRemoteRequestHandle
getModuleInfoList
setReportInfoByBundleName
operCommonPropertyDeviceType
operCommonPropertyDeviceTypeArea
operCommonPropertyDeviceTypeEnv
maintCommonPropertyDeviceType
maintCommonPropertyDeviceTypeArea
maintCommonPropertyDeviceTypeEnv
live_url
detailMessage
baseInfo
BaseSdkPresenter
reportBaseValue
device_type
device_model
interface_name
module_name
result_code
os_version
run_duration
service_name
message
app_id
app_ver_code
app_name
app_ver
extra_info
hw_button_accessibility_text
hw_customer_accessibility_text
pluginInfos
versionIds
downloadedModels
domain
modelName
resCompatibleVersion
modelType
isSubscribed
lastQueryVersion
lastQueryTime
versionInfos
version
storedTime
modelSize
status
deleteTime
sourcePath
ModelDownloadStatus
isExisted
isDownloadStart
isDownloadEnd
isDownloadSuccess
resultCode
session
serviceName
messageName
senderName
traceId
messageVersion
content
contentData
header
namespace
name
payload
request
dataEntities
resId
domain
isSupportMobileNet
resVersion
needShareRes
options
deviceType
productName
romVersion
osVersion
realMachineTestRes
args
subscribeId
subscribeType
notifyParams
action
mode
subscribeParams
params
pattern
queryOption
copyOption
option
updateTime
retCode
description
data
bundleName
resPath
resources
callingBundleName
failedList
succeedList
resSize
resUrl
resDigest
resType
supportSubRes
resPriority
extra
dataItems
downloadedSize
progress
UNUSED
BEING_USED
OUTDATED
UNKNOWN_TYPE
WLAN_TYPE
CELLULAR_TYPE
NO_NETWORK_STATUS
netAvailable
netBlockStatusChange
netCapabilitiesChange
netConnectionPropertiesChange
netLost
netUnavailable
BUSINESS_NETWORK
TEST_NETWORK

# 所有三方SDK都不混淆
-keep
../oh_modules
./oh_modules
../src/main/ets/modelManager/utils/ModelManagerReportUtil.ts
../src/main/ets/modelManager/utils/JsonUtil.ts

-keep-global-name
MaintenanceEventID
MaintenanceReportInfo
CallerInfo
BaseInfo
ReportCoreManager
bundleManager
jsonContent
JsonData
UpgradeSdkManager
UpgradeManagerBase
MessageUtil
QueryCloudReceiveParams