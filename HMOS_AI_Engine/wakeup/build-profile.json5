{
  "apiType": "stageMode",
  "buildOption": {
    "externalNativeOptions": {
      "path": "./src/main/cpp/CMakeLists.txt",
      "arguments": "",
      "cppFlags": ""
    },
    "nativeLib": {
      "debugSymbol": { // 可通过此配置对cpp编译产物so执行strip，移除so中的调试信息与符号表等
        "strip": true, // 执行strip
        "exclude": [] //执行strip的过滤正则表达式规则
      },
    }
  },
  "targets": [
    {
      "name": "default"
    }
  ]
}
