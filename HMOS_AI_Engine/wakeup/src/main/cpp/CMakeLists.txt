# the minimum version of CMake.
cmake_minimum_required(VERSION 3.5)
project(DEMO_MODULE)

# 定义一个变量，并赋值为当前模块cpp目录
set(NATIVERENDER_ROOT_PATH ${CMAKE_CURRENT_SOURCE_DIR})

# 添加头文件.h目录，包括cpp，cpp/include，告诉cmake去这里找到代码引入的头文件
include_directories(${NATIVERENDER_ROOT_PATH}
                    ${NATIVERENDER_ROOT_PATH}/include)

# 声明一个产物libentry.so，SHARED表示产物为动态库，hello.cpp为产物的源代码
add_library(wakeupnapi SHARED hello.cpp
${CMAKE_CURRENT_SOURCE_DIR}/include/memcpy_s.c
${CMAKE_CURRENT_SOURCE_DIR}/include/wb_aes_decrypt.c
${CMAKE_CURRENT_SOURCE_DIR}/include/wb_aes_util.c
${CMAKE_CURRENT_SOURCE_DIR}/include/com_huawei_wakeup_coordination_utils_CoordinationSecurity.h)

# 明确指定包含哪些源文件
#add_library(application hello.cpp memcpy_s.c wb_aes_decrypt.c)

# 声明产物entry链接时需要的三方库libace_napi.z.so
# 这里直接写三方库的名称是因为它是在ndk中，已在链接寻址路径中，无需额外声明
target_link_libraries(wakeupnapi PUBLIC libace_napi.z.so libhilog_ndk.z.so)


# 添加当前目录所有的源文件
