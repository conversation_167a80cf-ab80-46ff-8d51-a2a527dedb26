/*******************************************************************************
* Copyright (c) Huawei Technologies Co., Ltd. 1998-2014. All rights reserved.
* File name: securec.h
* Decription:
*             the user of this secure c library should include this header file
*             in you source code. This header file declare all supported API
*             prototype of the library, such as memcpy_s, strcpy_s, wcscpy_s,
*             strcat_s, strncat_s, sprintf_s, scanf_s, and so on.
* History:
*     1. Date:
*         Author:
*         Modification:
********************************************************************************
*/

#ifndef SECUREC_H__5D13A042_DC3F_4ED9_A8D1_882811274C27
#define SECUREC_H__5D13A042_DC3F_4ED9_A8D1_882811274C27

/* If you need high performance, enable the WITH_PERFORMANCE_ADDONS macro! */
#define WITH_PERFORMANCE_ADDONS

#include "securectype.h"    /*lint !e537*/
#include <stdarg.h>

/* If stack size on some embedded platform is limited, you can define the following macro
*  which will put some variables on heap instead of stack.
#define STACK_SIZE_LESS_THAN_1K
*/

/* for performance consideration, the following macro will call the corresponding API
* of libC for memcpy, memmove and memset
*/
#define CALL_LIBC_COR_API

/* codes should run under the macro COMPATIBLE_LINUX_FORMAT in unknow system on default,
   and strtold. The function
   strtold is referenced first at ISO9899:1999(C99), and some old compilers can
   not support these functions. Here provides a macro to open these functions:

       SECUREC_SUPPORT_STRTOLD  -- if defined, strtold will   be used
*/

/*define error code*/
#ifndef errno_t
typedef int errno_t;
#endif

/* success */
#define EOK (0)

/* invalid parameter */
#ifdef EINVAL
#undef EINVAL
#endif
#define EINVAL (22)
#define EINVAL_AND_RESET (22 | 0X80)
/* invalid parameter range */
#ifdef ERANGE
#undef ERANGE  /* to avoid redefinition */
#endif
#define ERANGE (34)
#define ERANGE_AND_RESET  (34 | 0X80)

#ifdef EOVERLAP_AND_RESET
#undef EOVERLAP_AND_RESET
#endif
/*Once the buffer overlap is detected, the dest buffer must be reseted! */
#define EOVERLAP_AND_RESET (54 | 0X80)

/*if you need export the function of this library in Win32 dll, use __declspec(dllexport) */

#ifdef __cplusplus
extern "C"
{
#endif




    /* return SecureC Version */
    void GET_HW_SECURE_C_VERSION(char* verStr, int bufSize, unsigned short* verNumber);

    /* wmemcpy */
    errno_t WMEMCPY_S(wchar_t* dest, size_t destMax, const wchar_t* src, size_t count);

    /* memmove */
    errno_t MEMMOVE_S(void* dest, size_t destMax, const void* src, size_t count);

    errno_t WMEMMOVE_S(wchar_t* dest, size_t destMax, const wchar_t* src, size_t count);

    errno_t WCSCPY_S(wchar_t* strDest, size_t destMax, const wchar_t* strSrc);

    errno_t WCSNCPY_S(wchar_t* strDest, size_t destMax, const wchar_t* strSrc, size_t count);

    errno_t WCSCAT_S(wchar_t* strDest, size_t destMax, const wchar_t* strSrc);

    errno_t WCSNCAT_S(wchar_t* strDest, size_t destMax, const wchar_t* strSrc, size_t count);

    /* strtok */
    char* STRTOK_S(char* strToken, const char* strDelimit, char** context);

    wchar_t* WCSTOK_S(wchar_t* strToken, const wchar_t* strDelimit, wchar_t** context);

    /* sprintf */
    int SPRINTF_S(char* strDest, size_t destMax, const char* format, ...) SECUREC_ATTRIBUTE(3,4);

    int SWPRINTF_S(wchar_t* strDest, size_t destMax, const wchar_t* format, ...);

    /* vsprintf */
    int VSPRINTF_S(char* strDest, size_t destMax, const char* format, va_list argptr) SECUREC_ATTRIBUTE(3,0);

    int VSWPRINTF_S(wchar_t* strDest, size_t destMax, const wchar_t* format, va_list argptr);

    int VSNPRINTF_S(char* strDest, size_t destMax, size_t count, const char* format, va_list arglist) SECUREC_ATTRIBUTE(4,0);

    /* snprintf */
    int SNPRINTF_S(char* strDest, size_t destMax, size_t count, const char* format, ...) SECUREC_ATTRIBUTE(4,5);

    /* scanf */
    int SCANF_S(const char* format, ...);

    int WSCANF_S(const wchar_t* format, ...);

    /* vscanf */
    int VSCANF_S(const char* format, va_list arglist);

    int VWSCANF_S(const wchar_t* format, va_list arglist);

    /* fscanf */
    int FSCANF_S(FILE* stream, const char* format, ...);

    int FWSCANF_S(FILE* stream, const wchar_t* format, ...);

    /* vfscanf */
    int VFSCANF_S(FILE* stream, const char* format, va_list arglist);

    int VFWSCANF_S(FILE* stream, const wchar_t* format, va_list arglist);

    /* sscanf */
    int SSCANF_S(const char* buffer, const char* format, ...);

    int SWSCANF_S(const wchar_t* buffer, const wchar_t* format, ...);

    /* vsscanf */
    int VSSCANF_S(const char* buffer, const char* format, va_list argptr);

    int VSWSCANF_S(const wchar_t* buffer, const wchar_t* format, va_list arglist);

    /* gets */
    char* GETS_S(char* buffer, size_t destMax);

    /* memset function*/
    errno_t MEMSET_S(void* dest, size_t destMax, int c, size_t count);
    /* memcpy function*/
    errno_t MEMCPY_SD(void* dest, size_t destMax, const void* src, size_t count);

    /* strcpy */
    errno_t STRCPY_S(char* strDest, size_t destMax, const char* strSrc);
    /* strncpy */
    errno_t STRNCPY_S(char* strDest, size_t destMax, const char* strSrc, size_t count);

    /* strcat */
    errno_t STRCAT_S(char* strDest, size_t destMax, const char* strSrc);
    /* strncat */
    errno_t STRNCAT_S(char* strDest, size_t destMax, const char* strSrc, size_t count);

    errno_t STRNCPY_ERROR(char* strDest, size_t destMax, const char* strSrc, size_t count);
    errno_t STRCPY_ERROR(char* strDest, size_t destMax, const char* strSrc);

#if defined(WITH_PERFORMANCE_ADDONS)
    /* those functions are used by macro */
    errno_t MEMSET_SOPTASM(void* dest, size_t destMax, int c, size_t count);
    errno_t MEMSET_SOPTTC(void* dest, size_t destMax, int c, size_t count);
    errno_t MEMCPY_SOPTASM(void* dest, size_t destMax, const void* src, size_t count);
    errno_t MEMCPY_SOPTTC(void* dest, size_t destMax, const void* src, size_t count);
    
    /* STRCPY_SP is a macro, NOT a function in performance optimization mode. */
#define STRCPY_SP(dest, destMax, src)  /*lint -save -e506 -e1055  -e650 -e778 -e802 */ (( __builtin_constant_p((destMax)) && __builtin_constant_p((src))) ?  \
    STRCPY_SM((dest), (destMax), (src)) : strcpy_s((dest), (destMax), (src)) ) /*lint -restore */

    /* STRNCPY_SP is a macro, NOT a function in performance optimization mode. */
#define STRNCPY_SP(dest, destMax, src, count)  /*lint -save -e506 -e1055 -e666  -e650 -e778 -e802 */ ((__builtin_constant_p((count)) &&  __builtin_constant_p((destMax)) && __builtin_constant_p((src))) ?  \
    STRNCPY_SM((dest), (destMax), (src), (count)) : strncpy_s((dest), (destMax), (src), (count)) ) /*lint -restore */

    /* STRCAT_SP is a macro, NOT a function in performance optimization mode. */
#define STRCAT_SP(dest, destMax, src)  /*lint -save -e506 -e1055  -e650 -e778 -e802 */ (( __builtin_constant_p((destMax)) && __builtin_constant_p((src))) ?  \
    STRCAT_SM((dest), (destMax), (src)) : strcat_s((dest), (destMax), (src)) ) /*lint -restore */

    /* STRNCAT_SP is a macro, NOT a function in performance optimization mode. */
#define STRNCAT_SP(dest, destMax, src, count)  /*lint -save -e506 -e1055 -e666  -e650 -e778 -e802 */ ((__builtin_constant_p((count)) &&  __builtin_constant_p((destMax)) && __builtin_constant_p((src))) ?  \
    STRNCAT_SM((dest), (destMax), (src), (count)) : strncat_s((dest), (destMax), (src), (count)) ) /*lint -restore */

    /* MEMCPY_SP is a macro, NOT a function in performance optimization mode. */
#define MEMCPY_SP(dest, destMax, src, count)  /*lint -save -e506 -e1055 -e650 -e778 -e802 */ (__builtin_constant_p((count)) ? (MEMCPY_SM((dest), (destMax),  (src), (count))) :  \
       (__builtin_constant_p((destMax)) ? (((size_t)(destMax) > 0 && (((UINT64T)(destMax) & (UINT64T)(-2)) < SECUREC_MEM_MAX_LEN)) ? MEMCPY_SOPTTC((dest), (destMax), (src), (count)) : ERANGE ) :  MEMCPY_SOPTASM((dest), (destMax), (src), (count)))) /*lint -restore */

    /* MEMSET_SP is a macro, NOT a function in performance optimization mode. */
#define MEMSET_SP(dest, destMax, c, count)  /*lint -save -e506 -e1055 -e650 -e778 -e802 */ (__builtin_constant_p((count)) ? (MEMSET_SM((dest), (destMax),  (c), (count))) :  \
       (__builtin_constant_p((destMax)) ? (((size_t)(destMax) > 0 && (((UINT64T)(destMax) & (UINT64T)(-2)) < SECUREC_MEM_MAX_LEN)) ? MEMSET_SOPTTC((dest), (destMax), (c), (count)) : ERANGE ) :  MEMSET_SOPTASM((dest), (destMax), (c), (count)))) /*lint -restore */

#endif

    int FscanfSOp(SEC_FILE_STREAM *pfStr, const char* format, ...);

    int FwscanfSOp(SEC_FILE_STREAM *pfStr, const char* format, ...);

    SEC_FILE_STREAM *SecFopenOp(char *pfilePath);

    SEC_FILE_STREAM *SecFopenOpEx(FILE *fp);

    void SecFcloseOp(SEC_FILE_STREAM *pfStr);
	void SecFcloseOpEx(SEC_FILE_STREAM *pfStr);

#ifdef __cplusplus
}
#endif  /* __cplusplus */

#endif/* __SECUREC_H__5D13A042_DC3F_4ED9_A8D1_882811274C27 */


