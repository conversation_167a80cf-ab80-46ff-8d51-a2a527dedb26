/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

#include"wb_aes_util.h"

u16 ROTL(u16 x, int n, int m)
{
    u16 vmask = (1 << m) - 1;
    int rot = n % m;
    u16 ret = (x << rot) | (x >> (m - rot));
    return ret & vmask;
}

void XOrBlock128(const u8 *tar, const u8 *src)
{
    u32 *t = (u32 *) tar;
    u32 *s = (u32 *) src;

    *t++ ^= *s++;
    *t++ ^= *s++;
    *t++ ^= *s++;
    *t++ ^= *s++;
}

void XOrBlock(u8 *tar, const u8 *src, const u32 len)
{
    u32 i;
    for (i = 0; i < len; ++i) {
        tar[i] ^= src[i];
    }
}
