/*
 * 版权所有 (c) 华为技术有限公司 2024-2025
 * wb_aes_decrypt.h
 *
 */
#ifndef WB_AES_DECRYPT_H_
#define WB_AES_DECRYPT_H_
#include"wb_aes_util.h"

void WbAesDecrypt(const u8* input, u8* output);
int WbAesDecryptCbc(
        const u8* iv,
        const u8* inPut, u32 inLen,
        u8* outPut, u32* outLen
        );

#include "com_huawei_wakeup_coordination_utils_CoordinationSecurity.h"

#endif /* WB_AES_DECRYPT_H_ */
