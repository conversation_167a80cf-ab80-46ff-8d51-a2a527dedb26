/*
 * 版权所有 (c) 华为技术有限公司 2024-2025
 * wb_aes_util.h
 *
 */

#ifndef WB_AES_UTIL_H_
#define WB_AES_UTIL_H_

#include<stdio.h>
#include<string.h>

typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;
typedef unsigned long long u64;

#define TB(tb,r,col,row,i)        (tb)[(r)<<(s+4)|(col)<<(s+2)|(row)<<s|(i)]

/*
 * 32-bit integer manipulation macros (little endian)
 */
#define GET_UINT32_LE(n,b,i)                    \
do{                                             \
    (n) = ( (u32) ((b)[(i)    ] & 0xff)       ) \
        | ( (u32) ((b)[(i) + 1] & 0xff) <<  8 ) \
        | ( (u32) ((b)[(i) + 2] & 0xff) << 16 ) \
        | ( (u32) ((b)[(i) + 3] & 0xff) << 24 );\
}while(0)

#define PUT_UINT32_LE(n,b,i)                 \
do{                                          \
    (b)[(i)    ] = (u8) ( (n)       );       \
    (b)[(i) + 1] = (u8) ( (n) >>  8 );       \
    (b)[(i) + 2] = (u8) ( (n) >> 16 );       \
    (b)[(i) + 3] = (u8) ( (n) >> 24 );       \
}while(0)


u16 ROTL(u16 x, int n, int m);
void XOrBlock128(const u8* tar, const u8* src);
void XOrBlock(u8* tar, const u8* src, const u32 len);

#endif /* WB_AES_UTIL_H_ */
