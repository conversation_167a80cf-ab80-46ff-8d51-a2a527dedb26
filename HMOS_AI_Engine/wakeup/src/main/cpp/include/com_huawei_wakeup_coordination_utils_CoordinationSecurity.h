/*
 * 版权所有 (c) 华为技术有限公司 2024-2025
 * DO NOT EDIT THIS FILE - it is machine generated 
 * Header for class com_huawei_security_WhiteboxCipher 
*/

#ifndef INCLUDED_COM_HUAWEI_WAKEUP_COORDINATION_UTILS_COORDINATIONSECURITY
#define INCLUDED_COM_HUAWEI_WAKEUP_COORDINATION_UTILS_COORDINATIONSECURITY
#include "napi/native_api.h"
#ifdef __cplusplus
extern "C" {
#endif


/*
 * Class:     com_huawei_wakeup_coordination_utils_CoordinationSecurity
 * Method:    CoordinationSecurity
 * Signature: ([B[B)[B
 */
napi_value CoordinationSecurity(napi_env env, napi_callback_info info);

#ifdef __cplusplus
}
#endif
#endif