# Key size of AES can only be 128,192 and 256
keysize = 128

# Key string in hex
#key = c2a65f6c06df9c9e4129756ae7b0b829
#key = 00000000000000000000000000000003
key = 4f14f53bdfedcf0b3b45f4394e75da25

# Expansion size can only be in [8,16]
# Larger expansion size leads to higher security but requires more storage 
expsize = 8

# Random seed
#seed = 0123cdef
seed = c8f9eac0

# Generate binary or not. 1=yes  0=no
binary = 1

# Generate c source or not. 1=yes  0=no
# !!!!!Could be very slow with big expsize!!!!!
source = 1
