/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */
#include "wb_aes_decrypt.h"
#include "securec.h"
#include "table2.h"
#include "wb_const.h"
#include "securectype.h"
#include "napi/native_api.h"
#include <bits/alltypes.h>
#define TB2 TABLE2
const int LENGTH = 16;
const int I_FOUR = 4;
const int I_EIGHT = 8;
const int I_TWELVE = 12;
const int R_SIZE_TWO = 2;
const int R_SIZE_THREE = 3;
const int R_SIZE_FOUR = 4;
const int R_SIZE_FIVE = 5;
const int R_SIZE_SIX = 6;
const int R_SIZE_SEVEN = 7;
const int R_SIZE_EIGHT = 8;
const int R_SIZE_NINE = 9;
const int SIXTEEN = 16;
const int FIFTEEN = 15;
void WbAesDecrypt(const u8 *input, u8 *output) 
{
    int i;
    u8 s = WB_EXP_SIZE2;
    u16 vmask = (1 << s) - 1;
    u16 t16;

    // input transformation
    u16 temp[16];
    for (i = 0; i < LENGTH; i++) {
        t16 = input[i];
        temp[i] = ROTL(t16, IN_ROT1 * i, s) ^ ROTL(t16, IN_ROT2 * i, s) ^ ROTL(t16, IN_ROT3 * i, s) ^ ((uint16_t)i * IN_CONST);
    }

    // put transformed ciphertext into Xs
    u32 xZero, xOne, xTwo, xThree, yZero, yOne, yTwo, yThree;
    GET_UINT32_LE(xZero, temp, 0);
    GET_UINT32_LE(xOne, temp, I_FOUR);
    GET_UINT32_LE(xTwo, temp, I_EIGHT);
    GET_UINT32_LE(xThree, temp, I_TWELVE);

    // decryption
    WB_AES_BROUND(TB2, y, x, 0);
    WB_AES_BROUND(TB2, x, y, 1);
    WB_AES_BROUND(TB2, y, x, R_SIZE_TWO);
    WB_AES_BROUND(TB2, x, y, R_SIZE_THREE);
    WB_AES_BROUND(TB2, y, x, R_SIZE_FOUR);
    WB_AES_BROUND(TB2, x, y, R_SIZE_FIVE);
    WB_AES_BROUND(TB2, y, x, R_SIZE_SIX);
    WB_AES_BROUND(TB2, x, y, R_SIZE_SEVEN);
    WB_AES_BROUND(TB2, y, x, R_SIZE_EIGHT);
    WB_AES_BROUND(TB2, x, y, R_SIZE_NINE);

    // output transformation
    PUT_UINT32_LE(xZero, temp, 0);
    PUT_UINT32_LE(xOne, temp, I_FOUR);
    PUT_UINT32_LE(xTwo, temp, I_EIGHT);
    PUT_UINT32_LE(xThree, temp, I_TWELVE);
    for (i = 0; i < LENGTH; i++) {
        t16 = temp[i] & vmask;
        output[i] =
            ROTL(t16, OUT_ROT1 * i, s) ^ ROTL(t16, OUT_ROT2 * i, s) ^ ROTL(t16, OUT_ROT3 * i, s) ^ ((uint16_t)i * OUT_CONST);
    }
}

int WbAesDecryptCbc(const u8 *iv, const u8 *input, u32 inLen, u8 *output, u32 *outLen) 
{
    // not accept input (ciphertext) length without length of multiples of 16 bytes
    if (inLen == 0 || (inLen % SIXTEEN) != 0) {
        return -1;
    }
    u32 count = inLen / SIXTEEN;

    u32 i, j;
    const u8 *ptrIn = input;
    u8 *ptrOut = output;
    u8 buf[16], temp[16];
    u8 pad;
    // cbc-decryption
    if (MEMCPY_SD(buf, SIXTEEN, iv, SIXTEEN) != EOK) {
        return -1;
    }

    for (i = 0; i < count; ++i) {
        if (i != count - 1) {
            WbAesDecrypt(ptrIn, ptrOut);
            XOrBlock(ptrOut, buf, SIXTEEN);
            if (MEMCPY_SD(buf, SIXTEEN, ptrIn, SIXTEEN) != EOK) {
                return -1;
            }
            ptrIn += SIXTEEN;
            ptrOut += SIXTEEN;
            continue;
        }
        WbAesDecrypt(ptrIn, temp);
        XOrBlock(temp, buf, SIXTEEN);
        // check pkcs7 padding
        pad = temp[FIFTEEN];
        if (pad > SIXTEEN) {
            return -1;
        }
        for (j = 1; j < pad; ++j) {
            if (temp[FIFTEEN - j] != pad) {
                return -1;
            }
        }
        *outLen = inLen - pad;
        if (MEMCPY_SD(ptrOut, SIXTEEN, temp, SIXTEEN - pad) != EOK) {
            return -1;
        }
        if (MEMCPY_SD(buf, SIXTEEN, ptrIn, SIXTEEN) != EOK) {
            return -1;
        }
        ptrIn += SIXTEEN;
        ptrOut += SIXTEEN;
    }
    return 0;
}
napi_value CoordinationSecurity(napi_env env, napi_callback_info info) 
{
    size_t argc = 2;
    napi_value args[2];
    napi_get_cb_info(env, info, &argc, args, NULL, NULL);
    napi_value IV_ARRAY  = args[0];
    napi_value SOURCE_ARRAY = args[1];

    napi_typedarray_type type;
    napi_value INPUT_BUFFER;
    size_t BYTE_OFFSET;
    size_t IV_LEN;
    napi_get_typedarray_info(env, IV_ARRAY , &type, &IV_LEN, NULL, &INPUT_BUFFER, &BYTE_OFFSET);

    // 对source数据做处理
    napi_value SOURCE_BUFFER;
    size_t SOURCE_OFFSET;
    size_t INPUT_LEN;
    napi_get_typedarray_info(env, SOURCE_ARRAY, &type, &INPUT_LEN, NULL, &SOURCE_BUFFER, &SOURCE_OFFSET);

    u32 MAX_OUTPUT_LEN = INPUT_LEN;
    u32 OUTPUT_LEN;
    
    uint8_t* output_tmp = (uint8_t*)malloc(MAX_OUTPUT_LEN);
    napi_value OUTPUT_TMP = (napi_value)output_tmp;

    if (output_tmp == NULL) {
        return NULL;
    }
    void *data;
    size_t BYTE_LENGTH;
    napi_get_arraybuffer_info(env, INPUT_BUFFER, &data, &BYTE_LENGTH);

    void *sourcedata;
    size_t SOURCE_LENGTH;
    napi_get_arraybuffer_info(env, SOURCE_BUFFER, &sourcedata, &SOURCE_LENGTH);
    int ret = WbAesDecryptCbc((const u8 *)data, (const u8 *)sourcedata, INPUT_LEN, output_tmp, &OUTPUT_LEN);
    if (ret < 0 || OUTPUT_LEN > MAX_OUTPUT_LEN) {
        free(output_tmp);
        return NULL;
    }
    
    void *dest;
    size_t RESULT_LENGTH;
    napi_get_arraybuffer_info(env, OUTPUT_TMP, &dest, &RESULT_LENGTH);
    uint8_t *input_bytes = (uint8_t *)(dest);
    // 创建outPutBuffer
    napi_value outPutBuffer;
    void *outPutPtr = NULL;
    napi_create_arraybuffer(env, OUTPUT_LEN, &outPutPtr, &outPutBuffer);
    napi_value outPutArray;
    napi_create_typedarray(env, napi_uint8_array, OUTPUT_LEN, outPutBuffer, 0, &outPutArray);
    uint8_t *output_bytes = (uint8_t *)outPutPtr;
    for (size_t i = 0; i < OUTPUT_LEN; i++) {
        output_bytes[i] = (uint8_t)input_bytes[i];
    }
    return outPutArray;
}

