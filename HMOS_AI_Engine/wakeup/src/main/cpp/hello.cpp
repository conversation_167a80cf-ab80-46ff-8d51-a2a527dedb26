/*
 * 版权所有 (c) 华为技术有限公司 2024-2025
 *
*/
#include "napi/native_api.h"
#include "include/wb_aes_decrypt.h"

#include <stdlib.h>
// 全局tag宏，标识模块日志tag
static napi_value Add(napi_env env, napi_callback_info info) 
{
   
    return CoordinationSecurity(env, info);
}


EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports) 
{
    napi_property_descriptor desc[] = {{"add", nullptr, Add, nullptr, nullptr, nullptr, napi_default, nullptr}};
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module DEMO_MODULE = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "wakeupnapi",
    .nm_priv = ((void *)0),
    .reserved = {0},
};

extern "C" __attribute__((constructor)) void RegisterWakeupNAPIModule(void) { napi_module_register(&DEMO_MODULE); }