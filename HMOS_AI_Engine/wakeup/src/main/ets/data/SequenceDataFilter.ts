/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import List from '@ohos.util.List';
import { DeviceData } from '../entity/DeviceData';

const TAG = "SequenceDataFilter";

/**
 * 唤醒数据容错数据校验
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-21
 */
export class SequenceDataFilter {

  private static TAG: string = "SequenceDataFilter";

  // 防重时间差值，为了防止手机设备之间的时钟跳变，阈值设置为5s
  private static SEQUENCE_TIME_THRESHOLD: number = 5;

  private static MAX_CACHED_SEQUENCE_SIZE: number = 50;
  // 用于放置顺序号信息
/*
  private static devSequenceNumbers: SparseArray<String>  = new SparseArray<>();

  public static filterDeviceData(deviceDatas: List<DeviceData>):List<DeviceData> {
  if (this.devSequenceNumbers.size() > SequenceDataFilter.MAX_CACHED_SEQUENCE_SIZE) {
  this.devSequenceNumbers.clear();
}
if (deviceDatas == null || deviceDatas.isEmpty()) {
  Logger.info(TAG, "Device data list is null or empty!");
  return new CopyOnWriteArrayList<>();
}
SparseIntArray devIdsToReserve = filterMostRecentData(deviceDatas);
return filterValidSequnceDatas(devIdsToReserve, deviceDatas);
}

*/

}