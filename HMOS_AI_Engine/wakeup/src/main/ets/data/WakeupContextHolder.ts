/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

const TAG = "WakeupContextHolder";

/**
 * 协同唤醒上下文持有单例
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-21
 */
export class WakeupContextHolder {
  private mContext: Context | undefined = undefined;
  private static instance: WakeupContextHolder = new WakeupContextHolder();

  private constructor() {
  };

  /**
   * 获取上下文持有单例
   *
   * @return 返回单例
   */
  public static getInstance(): WakeupContextHolder {
    return WakeupContextHolder.instance;
  }

  /**
   * 设置全局context
   *
   * @param context
   */
  public setContext(context: Context): void {
    this.mContext = context;
  }

  /**
   * 获取全局context
   *
   * @returns context用于进行部分系统接口调用
   */
  public getContext(): Context | undefined {
    return this.mContext;
  }
}