/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { BasicDeviceParameter } from '../entity/BasicDeviceParameter';
import { DeviceData } from '../entity/DeviceData';
import { InnerDeviceParameter } from '../entity/InnerDeviceParameter';
import { SceneInfo } from '../entity/SceneInfo';
import { Constants } from '../utils/Constants';
import { DataFormatter } from '../utils/DataFormatter';
import WakeUpLog from '../utils/WakeUpLog';

const TAG = "WakeupDataParser";

const INDEX_OF_ELEVENTH_BYTE = 10;

const OFFSET_OF_VOICE_LEVEL = 6;

const MIN_MSG_ID = 0;

const MAX_MSG_ID = 8;

const LOW_SIX_BITS_MASK = 0x3F;

const LOW_SEVEN_BITS_MASK = 0x7F;

const HIGH_TWO_BITS_MASK = 0xC0;

const HIGH_PRIOR_MASK = 0x1E;

const LOW_FOUR_BITS_MASK = 0x0F;

/**
 * 协同唤醒数据解析函数，当前有规则0和规则1两种解析方式
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-03
 */
export class WakeupDataParser {
  /**
   * 把字节数组初步把消息ID、设备类型等解析出来和原始字节数组放入DeviceData
   *
   * @param originData 原始字节数组
   * @return 返回对应DeviceData
   */
  public byteToDeviceData(originData: Uint8Array): DeviceData {
    if ((originData == null) || (originData.length < Constants.DATA_LEN_DEVICE_INFO)) {
      WakeUpLog.e(TAG, "parseResponse::response is error");
      return new DeviceData();
    }

    // 加入了探测包类型判断
    let msgIdValue = originData[0];
    if (msgIdValue < MIN_MSG_ID || msgIdValue > MAX_MSG_ID) {
      WakeUpLog.e(TAG, "byteToDeviceData::Invalid data");
      return new DeviceData();
    }

    let offset = 0;

    // 第1字节：消息ID
    let msgId = originData[offset++];

    // 第2字节：设备类型
    let deviceType = originData[offset++] & Constants.LOW_EIGHT_MASK;

    let seqNumAndDeviceIdSize = 4;

    // 第3-6字节：顺序号
    let sequenceNumbers = new Uint8Array(seqNumAndDeviceIdSize);
    sequenceNumbers[0] = originData[offset++];
    sequenceNumbers[1] = originData[offset++];
    sequenceNumbers[2] = originData[offset++];
    sequenceNumbers[3] = originData[offset++];
    //System.arraycopy(originData, offset, sequenceNumbers, 0, sequenceNumbers.length);
    //offset += sequenceNumbers.length;

    // 第7-10字节：设备UDID哈希值
    let deviceIds = new Uint8Array(seqNumAndDeviceIdSize);
    deviceIds[0] = originData[offset++];
    deviceIds[1] = originData[offset++];
    deviceIds[2] = originData[offset++];
    deviceIds[3] = originData[offset++];
    //System.arraycopy(originData, offset, deviceIds, 0, deviceIds.length);
    //offset += deviceIds.length;

    let wakeupWord = originData[offset++];

    // 预留位
    offset++;

    let ipLength = 3;
    let ips = new Uint8Array(ipLength);
    ips[0] = originData[offset++];
    ips[1] = originData[offset++];
    ips[2] = originData[offset++];
    //System.arraycopy(originData, offset, ips, 0, ips.length);

    let sequenceNumber = DataFormatter.byteArray2Int(sequenceNumbers);
    WakeUpLog.i(TAG, "deviceId:" + deviceIds);
    let deviceData = new DeviceData(msgId, deviceIds, originData, sequenceNumber, deviceType);
    if (msgId === Constants.MSG_ID_DEVICE_ENTER_NETWORK || msgId === Constants.MSG_ID_DEVICE_ENTER_NETWORK_RESPONSE) {
      deviceData.setWakeupWord(wakeupWord);
      deviceData.setIps(ips);
    }
    return deviceData;
  }

  /**
   * 把字节数组解析成可供排序规则使用的数据结构
   *
   * @param originData 待发送或者接收到的数据包字节数组
   * @param ruleVersion 排序规则版本号，根据不同版本号按照对应方式解析数据
   * @return 返回对应方式的解析结果
   */
  public byteToSceneInfo(originData: Uint8Array, ruleVersion: number): SceneInfo {
    if (originData == null) {
      return new SceneInfo(new BasicDeviceParameter());
    }
    let resultInfo: SceneInfo;
    if (ruleVersion === Constants.RULE_VERSION_ZERO) {
      resultInfo = this.byteToZeroVersionInfo(originData);
    } else {
      // 现阶段版本2和版本1的数据格式相同
      resultInfo = this.byteToOneVersionInfo(originData);
    }
    return resultInfo;
  }

  /**
   * 按照规则0的数据格式解析
   *
   * @param originData 原始字节数组
   * @return 返回对应解析结果
   */
  private byteToZeroVersionInfo(originData: Uint8Array): SceneInfo {
    if (originData.length < Constants.DATA_LEN_DEVICE_INFO) {
      WakeUpLog.i(TAG, "parseResponse version 0 origin data length is invalid");
      return new SceneInfo(new BasicDeviceParameter());
    }
    WakeUpLog.i(TAG, "parseResponse version 0::" + originData);

    // 第2字节：设备类型
    let deviceTypeIndex = 1;
    let deviceType = originData[deviceTypeIndex] & Constants.LOW_EIGHT_MASK;

    // 第11字节：当前设备语音助手最近使用时间
    let offset = INDEX_OF_ELEVENTH_BYTE;
    let time = originData[offset++] & Constants.LOW_EIGHT_MASK;

    // 第12字节：声强大小
    let energyLevel = Math.abs(originData[offset++] >> OFFSET_OF_VOICE_LEVEL);

    // 预留位
    let reservedColumn = originData[offset] & LOW_SIX_BITS_MASK;

    // 第13-15个字节：低中高优先级设备状态
    let highPriority = originData[offset++];
    let normalPriority = originData[offset++];
    let negativePriority = originData[offset];

    let param = new InnerDeviceParameter();
    param.setVoiceEnergyLevel(energyLevel)
      .setLastUseTimeFromNow(time)
      .setDeviceType(deviceType);
    param.setHighPriority(highPriority)
      .setNormalPriority(normalPriority)
      .setNegativePriority(negativePriority)
      .setReservedColumn(reservedColumn);
    let sceneInfo = new SceneInfo(param, true);
    WakeUpLog.i(TAG, "rule0 parseResponse::" + sceneInfo);
    return sceneInfo;
  }

  /**
   * 按照规则1的数据格式解析字节数组
   *
   * @param originData 原始字节数组
   * @return 返回对应解析结果
   */
  private byteToOneVersionInfo(originData: Uint8Array): SceneInfo {
    if (originData.length < Constants.DATA_LEN_DEVICE_INFO) {
      WakeUpLog.i(TAG, "parseResponse version 1 origin data length is invalid");
      return new SceneInfo(new BasicDeviceParameter());
    }
    WakeUpLog.i(TAG, "parseResponse version 1::" + originData);

    // 第2字节：设备类型
    let deviceTypeIndex = 1;
    let deviceType = originData[deviceTypeIndex] & Constants.LOW_EIGHT_MASK;

    // 第11字节：声强绝对值大小
    let offset = INDEX_OF_ELEVENTH_BYTE;
    let energyAbsoluteValue: number;
    if (deviceType === Constants.DEVICE_TYPE_LCD) {
      energyAbsoluteValue = originData[offset++] & Constants.LOW_EIGHT_MASK;
    } else {
      energyAbsoluteValue = originData[offset++] & LOW_SEVEN_BITS_MASK;
    }

    // 第12字节：声强等级2 bit位 + 当前设备语音助手最近使用时间后6位bit
    let energyAndTimeByte = originData[offset++];
    let energyLevel = (energyAndTimeByte & HIGH_TWO_BITS_MASK) >> OFFSET_OF_VOICE_LEVEL;
    let lastUseTime = energyAndTimeByte & LOW_SIX_BITS_MASK;

    // 第13字节3bit保留位+4bit高优先级分数+1bit无负面状态
    let highPriorityAndNotNegative = originData[offset++];
    let highPriority = highPriorityAndNotNegative & HIGH_PRIOR_MASK;
    highPriority = highPriority >> 1;
    let notNegative = highPriorityAndNotNegative & Constants.LOW_ONE_MASK;

    // 第14字节声纹置信度
    let voiceConfidence: number;
    if (deviceType === Constants.DEVICE_TYPE_TV || deviceType === Constants.DEVICE_TYPE_BLOOM) {
      voiceConfidence = originData[offset++] & Constants.LOW_EIGHT_MASK;
    } else {
      voiceConfidence = originData[offset++];
    }

    // 第15字节规则版本号+普通优先级
    let ruleVersionAndNormalPriority = originData[offset];
    let normalPriorityScore = ruleVersionAndNormalPriority & LOW_FOUR_BITS_MASK;

    let param = new InnerDeviceParameter();
    param.setVoiceEnergyLevel(energyLevel)
      .setLastUseTimeFromNow(lastUseTime)
      .setDeviceType(deviceType)
      .setVoiceConfidence(voiceConfidence)
      .setVoiceAbsoluteEnergy(energyAbsoluteValue);
    param.setHighPriority(highPriority).setNormalPriority(normalPriorityScore).setNegativePriority(notNegative);
    let sceneInfo = new SceneInfo(param, true);

    WakeUpLog.i(TAG, "rule1 parseResponse::" + sceneInfo);
    return sceneInfo;
  }
}