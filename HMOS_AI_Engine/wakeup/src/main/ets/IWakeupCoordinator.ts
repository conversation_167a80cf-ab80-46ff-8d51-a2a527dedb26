/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

import { SceneInfo } from './entity/SceneInfo';
import IResultListener from './IResultListener';

/**
 * 对外暴露的唤醒协同接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-02
 */
export interface IWakeupCoordinator {
    /**
     * 二级唤醒成功流程尝试开始唤醒协同
     *
     * @param context 上下文
     * @param responseListener 唤醒协同结束后执行的回调接口
     * @param sceneInfo 执行唤醒时的使用场景信息
     *
     * @see IResultListener
     * @see SceneInfo
     */
    start(context:Context ,resultListener:IResultListener ,sceneInfo:SceneInfo):void;

    /**
     * 一级唤醒后开始启动和停止扫描和广播接口，优化手机单设备场景下不参与协同的响应时延
     *
     * @param deviceType 设备类型   int
     * @return true:启动或者停止扫描广播成功；false:启动或者停止扫描广播失败
     */
    notifyOnWakeupDetected(deviceType:number):Promise<boolean>;

    /**
     * 无旧版本，不需要这个接口了
     * 一级唤醒后开始启动和停止扫描和广播接口，优化手机单设备场景下不参与协同的响应时延
     *
     * @param deviceName 设备名称
     * @return true:启动或者停止扫描广播成功；false:启动或者停止扫描广播失败
     */
    //notifyOnWakeupDetected(deviceName:string):boolean;

    /**
     * 二级唤醒失败后，切换发送唤醒失败包
     *
     * @param deviceType 设备类型   int
     * @return 返回是否成功切换发送失败包
     */
    notifyOnWakeupFailed(deviceType:number):boolean;

    /**
     * 无旧版本，不需要这个接口了
     * 二级唤醒失败后，切换发送唤醒失败包
     *
     * @param deviceName 设备名称
     * @return 返回是否成功切换发送失败包
     */
    // notifyOnWakeupFailed(deviceName:string):boolean;

    /**
     * 停止协同
     */
    stop():void;

    /**
     * 释放资源
     */
    release():void;

    /**
     * 判断nearby是否初始化完成
     *
     * @return 返回对应判断结果
     */
    isNearbyInitialed():boolean;

    /**
     * 判断900ms延时是否结束
     *
     * @return 返回从发现包接口调用开始的900ms是否定时结束
     */
    getLongTimeout():boolean;

    /**
     * 判断协同唤醒是否可用
     *
     * @param isSecondWakeup 二级唤醒标识
     * @return 返回是否满足协同唤醒条件
     */
    isWakeupCoordinationAvailable(isSecondWakeup:boolean):boolean;

    /**
     * 判断协同唤醒是否可用
     *
     * @param isOpenOld 开关的旧值
     * @param isOpenNew 开关的新值
     * @param wakeupWord 唤醒词    byte
     * @param deviceType 设备类型   int
     */
    setCoordinatorOn(isOpenOld:boolean, isOpenNew:boolean, wakeupWord:number, deviceType:number):void;

    /**
     * 无旧版本，不需要这个接口了
     * 判断协同唤醒是否可用
     *
     * @param isOpenOld 开关的旧值
     * @param isOpenNew 开关的新值
     * @param wakeupWord 唤醒词    byte
     * @param deviceName 设备名称
     */
    //setCoordinatorOn(isOpenOld:boolean, isOpenNew:boolean, wakeupWord:number, deviceName:string):void;

    /**
     * 在语音助手退出的时候
     *
     * @param state int 1：退出语音助手； -1：默认值； 其它值：保留
     */
    sendVoiceAssistantState(state:number):void;
}