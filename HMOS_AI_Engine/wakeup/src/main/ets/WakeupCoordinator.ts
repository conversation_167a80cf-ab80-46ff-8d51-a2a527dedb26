/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { CoordinatorListenerImp } from './CoordinatorListenerImp';
import { WakeupContextHolder } from './data/WakeupContextHolder';
import { BasicDeviceParameter } from './entity/BasicDeviceParameter';
import { SceneInfo } from './entity/SceneInfo';
import { State } from './entity/State';
import { StateMachine } from './entity/StateMachine';
import IResultListener from './IResultListener';
import { IStateChangeCallback } from './IStateChangeCallback';
import { IWakeupCoordinator } from './IWakeupCoordinator';
import { CommonUtil } from './utils/CommonUtil';
import { Constants } from './utils/Constants';
import { ReportUtil } from './utils/ReportUtil';
import WakeUpLog from './utils/WakeUpLog';
import access from '@ohos.bluetooth.access';
import { BusinessError, Callback } from '@ohos.base';
import { HiAnalyticsReport } from './report/HiAnalyticsReport';
import { BleProxyImp } from './ble/BleProxyImp';
import { CoordinationUtil } from './utils/CoordinationUtil';
import { CoordinatorResult } from './entity/CoordinatorResult';
import List from '@ohos.util.List';
import { Context } from '@kit.AbilityKit';

const TAG: string = "WakeupCoordinator";

const VOICE_ASSIS_EXITED = 1;

/**
 * 3.0 停止发送抑制包的最晚时间
 */
const LATEST_COORDINATION_TIMER_VALUE = 2000;

/**
 * 如果当前已经唤醒，超过5s认为是第二次唤醒，允许重入
 */
const LIMIT_OF_SAME_WAKE_MAX_GAP = 5000;

/**
 * 手机一级唤醒后开始发周围设备探测包日志标志
 */
const TAG_DETECTED = "WakeupCoordinatorDetacted";

/**
 * 软总线共享库的名字
 */
const DEPENDENT_LIBRARAY_HISOFTBUS = "hisoftbus";

type numOrString = string | number;

////////////////////////////////////////////////////////////////////
//                            _ooOoo_                             //
//                           o8888888o                            //
//                           88" . "88                            //
//                           (| ^_^ |)                            //
//                           O\  =  /O                            //
//                        ____/`---'\____                         //
//                      .'  \\|     |//  `.                       //
//                     /  \\|||  :  |||//  \                      //
//                    /  _||||| -:- |||||-  \                     //
//                    |   | \\\  -  /// |   |                     //
//                    | \_|  ''\---/''  |   |                     //
//                    \  .-\__  `-`  ___/-. /                     //
//                  ___`. .'  /--.--\  `. . ___                   //
//                ."" '<  `.___\_<|>_/___.'  >'"".                //
//              | | :  `- \`.;`\ _ /`;.`/ - ` : | |               //
//              \  \ `-.   \_ __\ /__ _/   .-` /  /               //
//        ========`-.____`-.___\_____/___.-`____.-'========       //
//                             `=---='                            //
//        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^      //
//        佛祖保佑             永无BUG              永不修改          //
////////////////////////////////////////////////////////////////////

/**
 * 唤醒协同对外接口类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-02
 */
export default class WakeupCoordinator implements IWakeupCoordinator {
  /**
   * 唤醒协同监听
   */
  private mCoordinatorListener: CoordinatorListenerImp | undefined = undefined;
  private startCoordinationStamp = 0;
  private deviceMap: Map<string, number> = new Map<string, number>();
  private timeoutMap: Map<number, number> = new Map<number, number>();
  checkBluetoothBuildCar: boolean;
  checkSoftBusBuildCar: boolean;

  /**
   * 判断系统是否有软总线共享库
   * 背景：手机语音助手应用市场升级，可以在非鸿蒙系统上安装（例：EMUI11.0）,纯鸿蒙开始都支持3.0和2.0
   *
   * @param context 协同唤醒组件上下文
   * @return 系统中是否有软总线共享库
   */
  public constructor(context: Context, stateChangeCallback: IStateChangeCallback) {
    WakeUpLog.i(TAG, 'WakeupCoordinator constructor 3.0');
    if (context === undefined || stateChangeCallback === undefined) {
      WakeUpLog.w(TAG, "start:: context or stateChangeCallback is null")
      return;
    }
    //  1.0-3.0均为常连接，即使蓝牙关闭也会进行服务绑定
    this.init(context);
    StateMachine.setNewState(State.STANDBY);
    if (this.getIsThirdVersionOk()) {
      this.mCoordinatorListener = new CoordinatorListenerImp(context);
      CommonUtil.setIsThirdVersionOk(true);
    } else {
      // 1.0版本只进行BLE广播发送
      this.mCoordinatorListener = new CoordinatorListenerImp(null);
      CommonUtil.setIsThirdVersionOk(false);
    }
    this.mCoordinatorListener.setStateChangeCallback(stateChangeCallback);
  }

  public getIsThirdVersionOk(): boolean {
    return true;
  }

  private init(context: Context): void {
    WakeupContextHolder.getInstance().setContext(context);
    this.registerBluetoothReceiver();
  }

  private onReceiveEvent(data: access.BluetoothState): void {
    WakeUpLog.i(TAG, "BluetoothState: " + JSON.stringify(data));
    //监听蓝牙开关关闭关闭扫描
    if (data === access.BluetoothState.STATE_OFF) {
      WakeUpLog.i(TAG, "BluetoothState.STATE_OFF");
      if (this.mCoordinatorListener === undefined) {
        return;
      }
      this.mCoordinatorListener.doStopAdvertise();
      this.mCoordinatorListener.doStopScan();
    }
  }

  /**
   * 注册蓝牙广播，监听蓝牙关闭时解绑nearby,需要修改适配一下，当蓝牙关闭，BLE广播无法使用
   */
  private registerBluetoothReceiver(): void {
    // 进行蓝牙状态获取和监听
    try {
      // 获取蓝牙开关状态
      if (!BleProxyImp.isBluetoothOn()) {
        WakeUpLog.i(TAG, "WakeupCoordinator bluetooth close.");
        return;
      }
      // 订阅蓝牙设备开关状态事件
      access.on('stateChange', this.onReceiveEvent);
    } catch (err) {
      console.error('errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
  }

  /**
   * 一级唤醒后开始启动和停止扫描和广播接口，优化手机单设备场景下不参与协同的响应时延
   *
   * @param deviceType 设备类型
   * @return true:启动或者停止扫描广播成功；false:启动或者停止扫描广播失败
   */
  public async notifyOnWakeupDetected(deviceType: number): Promise<boolean> {
    if (this.mCoordinatorListener === undefined) {
      WakeUpLog.i(TAG, "mCoordinatorListener is undefined");
      return false;
    }
    // this.mCoordinatorListener = new CoordinatorListenerImp();
    WakeUpLog.i(TAG, "=================detected=================");
    if (this.canWakeUp()) {
      WakeUpLog.i(TAG, "wakeup fail");
      return false;
    }
    // 设备处在工作态 or 工作抑制态 需要运行唤醒
    if (StateMachine.getNewState() === State.WORKING || StateMachine.getNewState() === State.WORKED) {
      // 修改协同唤醒工作状态
      StateMachine.setNewState(State.STANDBY);
    }
    WakeUpLog.i(TAG, "deviceType: " + deviceType);
    // 记录数据用于打点
    this.putReportData(deviceType);

    // 初始化协同响应实例的状态标志位，避免因BLE初始化不成功导致的唤不醒问题
    this.mCoordinatorListener.initCoordinationStatus();
    // 检测唤醒协同是否可用，蓝牙是否打开,nearby初始化，系统非root
    if (!this.isWakeupCoordinationAvailable(false)) {
      return false;
    }

    // 判断设备组里面是否有车机
    this.checkSoftBusBuildCar = this.mCoordinatorListener.checkSoftBusBuildCar();

    // 判断连接的蓝牙是否是车机
    this.checkBluetoothBuildCar = this.mCoordinatorListener.checkBluetoothBuildCar();

    // 设置最早决策延迟器2.0，700/500ms延迟，3.0，1300ms
    this.setFirstLevelTimer();
    // 设置最晚决策延迟器2000ms
    this.setSecondLevelTimer();

    // 获取设备类型
    let basicDeviceParameter = new BasicDeviceParameter();
    basicDeviceParameter.setDeviceType(deviceType);

    // 初始化蓝牙
    return await this.mCoordinatorListener.doStartScanAdvertise(new SceneInfo(basicDeviceParameter));
  }

  /**
   * 是否满足Detected预唤醒条件
   *
   * @returns 是否满足预唤醒条件
   */
  private canWakeUp(): boolean {
    if (this.mCoordinatorListener === undefined) {
      WakeUpLog.w(TAG, "notifyOnWakeupDetected:: mCoordinatorListener is null")
      return true;
    }
    return false;
  }

  /**
   * 记录数据用于打点
   * @param deviceType 设备类型
   */
  private putReportData(deviceType: number): void {
    if (this.mCoordinatorListener === undefined) {
      return;
    }
    // 记录协同唤醒开始时间戳
    this.mCoordinatorListener.putCoordinatorData(ReportUtil.COOR_START_TIME, Date.now().toString());

    // 记录设备类型
    this.mCoordinatorListener.putCoordinatorData(ReportUtil.COOR_DEVICE_TYPE, deviceType.toString());
  }

  /**
   * 最早决策延迟
   */
  private setFirstLevelTimer(): void {
    if (this.mCoordinatorListener === undefined) {
      return;
    }
    // 停止之前的决策定时器
    try {
      WakeUpLog.i(TAG, "setFirstLevelTimer:: stop already Timer");
      this.stopTimer(Constants.TIMER_TYPE_EARLIEST_COORDINATION);
      this.stopTimer(Constants.TIMER_TYPE_LATEST_COORDINATION);
    } catch (e) {
      WakeUpLog.i(TAG, "setFirstLevelTimer:: stop already Timer fail");
    }

    this.mCoordinatorListener.setFirstOnWakeup(true);
    this.startCoordinationStamp = Date.now();

    // 获取设备的主要通信通道
    let firstLevelTimer = Constants.BLE_EARLIEST_COORDINATION_TIMER_VALUE;
    if (this.checkBluetoothBuildCar || this.checkSoftBusBuildCar) {
      firstLevelTimer = Constants.BLE_EARLIEST_COORDINATION_TIMER_VALUES;
    }
    let mainChannelType = CommonUtil.getChannelStatus(this.mCoordinatorListener.getDeviceType());
    WakeUpLog.d(TAG, "setFirstLevelTimer:: mainChannelType::" + mainChannelType);
    if (CommonUtil.getIsSecondVersionOk() && Constants.WIFI_CHANNEL === mainChannelType) {
      firstLevelTimer = Constants.WIFI_EARLIEST_COORDINATION_TIMER_VALUE;
    }
    // 创建1.0最早决策定时器
    this.startTimer(firstLevelTimer, Constants.TIMER_TYPE_EARLIEST_COORDINATION);
  }

  /**
   * 最晚决策延迟
   */
  private setSecondLevelTimer(): void {
    if (this.mCoordinatorListener === undefined) {
      return;
    }
    // 假如已经开启了第二个定时器，就应该重置该定时器
    if (this.mCoordinatorListener.getIsLatestCoordinationTimerStarted()) {
      this.stopTimer(Constants.TIMER_TYPE_LATEST_COORDINATION);
    }
    if (CommonUtil.isThirdVersionOk()) {
      this.stopTimer(Constants.TIMER_TYPE_SOFTBUS_COORDINATION_CLEAR);
      this.startTimer(LATEST_COORDINATION_TIMER_VALUE, Constants.TIMER_TYPE_LATEST_COORDINATION);
    } else {
      if (this.checkBluetoothBuildCar || this.checkSoftBusBuildCar) {
        this.startTimer(Constants.LATEST_COORDINATION_TIMER_VALUE_SECOND_VERSIONS,
          Constants.TIMER_TYPE_LATEST_COORDINATION);
      } else {
        this.startTimer(Constants.LATEST_COORDINATION_TIMER_VALUE_SECOND_VERSION,
          Constants.TIMER_TYPE_LATEST_COORDINATION);
      }
    }
    this.mCoordinatorListener.setLatestCoordinationTimerStarted(true);
  }

  /**
   * 创建各种延时决策任务，并保存自身的类型，用于提前决策是取消对应的延时任务
   *
   * @param time  延迟时间
   * @param type  任务类型
   */
  private startTimer(time: number, type: number): void {
    if (this.mCoordinatorListener == null) {
      WakeUpLog.w(TAG, "Coordinator listener is null");
      return;
    }
    WakeUpLog.i(TAG, "begin to start detected timer:" + time + ",type:" + type);
    this.mCoordinatorListener.doStartTimer(time, type);
  }


  /**
   * 取消延时任务
   *
   * @param type  任务类型
   */
  private stopTimer(type: number): void {
    if (this.mCoordinatorListener == null) {
      WakeUpLog.w(TAG, "Coordinator listener is null");
      return;
    }
    WakeUpLog.i(TAG, "begin to stop timer:" + type);
    this.mCoordinatorListener.doStopTimer(type);
  }

  /**
   * 语音收到消息后，通过此接口来判断是否需要自身唤醒，通过回调反馈决策结果
   *
   * @param context 上下文
   * @param resultListener 唤醒协同结束后执行的回调接口
   * @param sceneInfo 执行唤醒时的使用场景信息
   */
  public start(context: Context, resultListener: IResultListener, sceneInfo: SceneInfo): void {
    if (this.mCoordinatorListener === undefined) {
      WakeUpLog.w(TAG, "start:: mCoordinatorListener is null")
      return;
    }
    WakeUpLog.i(TAG, Constants.VERSION_ID + "=================start::wake up=================");
    if (!this.canStartWakeUp(context, resultListener, sceneInfo)) {
      WakeUpLog.w(TAG, "start:: canStartWakeUp error")
      return;
    }

    // 无预唤醒直接二级唤醒 设备处在工作态 or 工作抑制态 需要运行唤醒
    if (CommonUtil.isThirdVersionOk()
      && (StateMachine.getNewState() === State.WORKING || StateMachine.getNewState() === State.WORKED)) {
      StateMachine.setNewState(State.STANDBY);
    }
    if (!this.mCoordinatorListener.getFirstOnWakeup()) {
      WakeUpLog.w(TAG, "start:: getFirstOnWakeup:" + this.mCoordinatorListener.getFirstOnWakeup())
      // 打点-没有一级唤醒,取二级唤醒的时间戳作为开始时间
      this.mCoordinatorListener.putCoordinatorData(ReportUtil.COOR_START_TIME, Date.now().toString());
    }
    let deviceType = sceneInfo.getDeviceType();
    let deviceName = sceneInfo.getDeviceName();
    if (deviceType === 0 && !(deviceName === null || deviceName === "")) {
      deviceType = this.getDeviceValue(deviceName);
      // 没有设置deviceType,根据deviceName取值
      sceneInfo.setDeviceType(deviceType);
    }
    this.mCoordinatorListener.setDeviceType(deviceType);
    WakeUpLog.i(TAG, "start::device name = " + deviceName + "; deviceType = " + deviceType + "; deviceId = "
      + CoordinationUtil.getOdidHash());
    // 打点-将设备信息保存，用于后面打点
    this.recordReportData(sceneInfo);
    // 设置唤醒回调，用于通知调用方是否需要本地唤醒
    this.mCoordinatorListener.setResultListener(resultListener);
    if (!this.isWakeupCoordinationAvailable(true)) {

      // 协同不可用或蓝牙没开，通知唤醒可以拉起小艺主进程
      resultListener.onReady(true);
      resultListener.onResult(new CoordinatorResult(true, true, 0, new List<number>(), 0));
      return;
    }

    // 真正二级唤醒成功后设置本机的设备类型
    ReportUtil.setReportDeviceType(sceneInfo);
    // 假如没有一级唤醒，需要走一级唤醒的流程
    if (!this.mCoordinatorListener.getFirstOnWakeup()) {
      this.handleNoFirstLevelWakeup(sceneInfo, resultListener);
      return;
    }

    // 二级唤醒事件标识设置
    this.mCoordinatorListener.setOnWakeup(true, true);
    this.mCoordinatorListener.doStart(resultListener, sceneInfo);
  }

  /**
   * 是否满足Star协同条件
   *
   * @param context 上下文
   * @param resultListener 唤醒协同结束后执行的回调接口
   * @param sceneInfo 执行唤醒时的使用场景信息
   */
  private canStartWakeUp(context: Context, responseListener: IResultListener, sceneInfo: SceneInfo): boolean {
    if (context === undefined || context === null) {
      WakeUpLog.w(TAG, "start:: context is null")
      return false;
    }
    if (responseListener === undefined || responseListener === null) {
      WakeUpLog.w(TAG, "start:: responseListener is null")
      return false;
    }
    if (sceneInfo === undefined || sceneInfo === null) {
      WakeUpLog.w(TAG, "start:: sceneInfo is null")
      return false;
    }
    if (this.mCoordinatorListener === undefined || this.mCoordinatorListener === null) {
      WakeUpLog.w(TAG, "start:: mCoordinatorListener is null")
      return false;
    }
    WakeUpLog.i(TAG, "canWakeUp::voiceAbsoluteEnergy = " + sceneInfo.getVoiceAbsoluteEnergy()
      + "; spatial spectrum = " + sceneInfo.getSpatialSpectrum());
    return true;
  }

  private getDeviceValue(deviceName: string): number {
    if (this.mCoordinatorListener != null) {
      this.mCoordinatorListener.setDeviceName(deviceName);
    }
    HiAnalyticsReport.getInstance().setDeviceName(deviceName);
    if (this.deviceMap === null || this.deviceMap.size === 0) {
      this.deviceMap = CommonUtil.getMapByResId();
    }
    if (!this.deviceMap.has(deviceName)) {
      return 0;
    }
    //获取当前设备类型id
    let deviceTypeId = this.deviceMap.get(deviceName);
    return deviceTypeId === undefined ? 0 : deviceTypeId;
  }

  private recordReportData(sceneInfo: SceneInfo): void {
    // TODO
  }

  /**
   * 没有一级唤醒的，直接上报二级唤醒的，需要通过该接口去扫描，和广播二级包
   *
   * @param sceneInfo 二级唤醒时的场景信息
   */
  private handleNoFirstLevelWakeup(sceneInfo: SceneInfo, resultListener: IResultListener): void {
    if (sceneInfo == null || resultListener == null || this.mCoordinatorListener == null) {
      return;
    }

    // 开启第一个定期器
    this.setFirstLevelTimer();

    // 假如已经开启了第二个定时器，就应该重置该定时器
    this.setSecondLevelTimer();

    // 初始化协同唤醒的初始化标志位，避免因nearby初始化不成功导致的唤不醒问题
    this.mCoordinatorListener.initCoordinationStatus();
    this.mCoordinatorListener.setOnWakeup(true, true);
    this.mCoordinatorListener.doWhenNoFirstLevelWakeup(sceneInfo, resultListener);
  }

  /**
   * 二级唤醒失败后，切换发送唤醒失败包
   *
   * @param deviceType 设备类型   int
   * @return 返回是否成功切换发送失败包
   */
  public notifyOnWakeupFailed(deviceType: number): boolean {
    WakeUpLog.i(TAG_DETECTED, "notifyOnWakeupFailed begin.");

    if (this.mCoordinatorListener == null) {
      WakeUpLog.w(TAG_DETECTED, "notifyOnWakeupFailed: input paramter2 is null.");
      return false;
    }

    if (!this.mCoordinatorListener.getFirstOnWakeup()) {
      return false;
    }

    if (this.mCoordinatorListener.getIsOnWakeup()) {
      WakeUpLog.w(TAG, "notifyOnWakeupFailed: device wakeup again in 1.3s!!!");
      return false;
    }

    // onWakeup比900ms定时后来场景，需要停止广播和扫描
    if (this.getLongTimeout()) {
      if (!CommonUtil.getIsSecondVersionOk()) {
        this.mCoordinatorListener.doStopAdvertise();
        this.mCoordinatorListener.doStopScan();

        // 如果超过了900ms才到来，也不需要走后续流程了。
        return false;
      } else {
        this.mCoordinatorListener.doStopHiLinkAdvertistAndScan(false);
      }
    }

    // 二级唤醒事件标识设置
    this.mCoordinatorListener.setOnWakeup(true, false);

    // 二级唤醒失败流程需要停止700ms定时器,统一在900ms超时停止nearby这些
    this.stopTimer(Constants.TIMER_TYPE_EARLIEST_COORDINATION);
    let basicParam = new BasicDeviceParameter();
    basicParam.setDeviceType(deviceType);

    // 切换发送失败包
    this.mCoordinatorListener.doChangeAdvertise(new SceneInfo(basicParam));
    return true;
  }

  /**
   * 停止协同
   */
  public stop(): void {
    if (this.mCoordinatorListener == null) {
      WakeUpLog.w(TAG_DETECTED, "stop::Input parameter is null.");
      return;
    }
    this.mCoordinatorListener.doStop();
  }

  /**
   * 释放资源
   */
  public release(): void {
    if (this.mCoordinatorListener == null) {
      WakeUpLog.w(TAG_DETECTED, "release::Input parameter is null.");
      return;
    }
    this.mCoordinatorListener.doRelease();
  }

  /**
   * 判断nearby是否初始化完成
   *
   * @return 返回对应判断结果
   */
  public isNearbyInitialed(): boolean {
    if (this.mCoordinatorListener == null) {
      WakeUpLog.w(TAG, "isNearbyInitaled input paramter is null");
      return false;
    }
    if (!this.mCoordinatorListener.isNearbyInitialed()) {
      WakeUpLog.i(TAG, "nearBy is still not initialed!");
      return false;
    }
    return true;
  }

  /**
   * 判断900ms延时是否结束
   *
   * @return 返回从发现包接口调用开始的900ms是否定时结束
   */
  public getLongTimeout(): boolean {
    if (this.mCoordinatorListener == null) {
      WakeUpLog.w(TAG, "getLongTimeout input paramter is null");
      return false;
    }
    return this.mCoordinatorListener.getIsScanLongTimeout();
  }

  /**
   * 唤醒协同是否可用，蓝牙是否打开
   *
   * @param isSecondWakeup true 二级唤醒标识
   * @return 可用返回true，否则返回false。
   */
  public isWakeupCoordinationAvailable(isSecondWakeup: boolean): boolean {
    if (this.mCoordinatorListener === null || this.mCoordinatorListener === undefined) {
      WakeUpLog.w(TAG, "isWakeCoordinationAvailable::input parameter2 is null");
      return false;
    }
    if (!BleProxyImp.isBluetoothOn()) {
      WakeUpLog.w(TAG, "isWakeCoordinationAvailable::1.0version bluetooth switch off");
      return false;
    }
    return true;
  }

  /**
   * 判断协同唤醒是否可用
   *
   * @param isOpenOld 开关的旧值
   * @param isOpenNew 开关的新值
   * @param wakeupWord 唤醒词    byte
   * @param deviceType 设备类型   int
   */
  public setCoordinatorOn(isOpenOld: boolean, isOpenNew: boolean, wakeupWord: number, deviceType: number): void {
    // 通过依赖
    WakeUpLog.i(TAG, "setCoordinatorOn： " + isOpenOld);
    if (this.mCoordinatorListener == null) {
      return;
    }
    if (!isOpenNew) {
      this.mCoordinatorListener.onAdvertiseStop();
      this.mCoordinatorListener.doStopScan();
    }
    this.mCoordinatorListener.setCoordinationSwitch(isOpenNew);
    this.mCoordinatorListener.setDeviceType(deviceType);
    this.mCoordinatorListener.setWakeupWord(wakeupWord);
  }

  /**
   * 在语音助手退出的时候
   *
   * @param state int 1：退出语音助手； -1：默认值； 其它值：保留
   */
  public sendVoiceAssistantState(state: number): void {
    if (this.mCoordinatorListener === undefined) {
      return;
    }
    WakeUpLog.i(TAG, "sendVoiceAssistantState -> state: " + state);
    if (!CommonUtil.isThirdVersionOk()) {
      return;
    }
    if (state === VOICE_ASSIS_EXITED) {
      this.mCoordinatorListener.onVoiceAssistantExit();
    }
  }
}