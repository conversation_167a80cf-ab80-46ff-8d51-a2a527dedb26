/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { DeviceData } from '../entity/DeviceData';
import { BaseCoordinationRule } from './BaseCoordinationRule';
import List from '@ohos.util.List';
import { DataFormatter } from '../utils/DataFormatter';
import WakeUpLog from '../utils/WakeUpLog';

const TAG = "WakeupSecondImproveRule";

const MIN_LIST_SIZE = 2;

/**
 * 协同唤醒最新改进规则2号版本规则
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-28
 */
export class WakeupSecondImproveRule extends BaseCoordinationRule {
  private isAllNewRule = true;


  private THRESHOLD_VALUE = 3;

  private UNRELIABLE_DATA = 0;

  private RULE_FOUR_VERSION = 4;

  private RULE_DEVICEONE_2E = 1;

  private RULE_DEVICETWO_2E = 2;

  /**
   * 外部接口函数，用来进行协同，得到协同结果
   *
   * @param deviceDataLists 设备信息列表
   * @return 返回协同结果，列表第一个设备是应该响应的设备
   */
  public doCoordinate(deviceDataLists: List<DeviceData>): List<DeviceData> {
    if (deviceDataLists == null || deviceDataLists.length < MIN_LIST_SIZE) {
      WakeUpLog.d(TAG, "deviceDataLists:"+deviceDataLists.length);
      return deviceDataLists;
    }

    let isAllPhoneDev = WakeupSecondImproveRule.isAllPhoneDevice(deviceDataLists);
    WakeUpLog.i(TAG, "isAllPhoneDev:" + isAllPhoneDev);
    if (isAllPhoneDev) {
      return this.coordinateWithAllPhones(deviceDataLists);
    } else {
      return this.coordinateWithAllDevices(deviceDataLists);
    }
  }

  /**
   * 全手机场景协同
   *
   * @param deviceDataLists 设备信息列表
   * @return 返回协同结果
   */
  private coordinateWithAllPhones(deviceDataLists: List<DeviceData>): List<DeviceData> {
    WakeUpLog.i(TAG, "coordinateWithAllPhones SATART");
    deviceDataLists.sort((deviceOne, deviceTwo) => {
      let deviceOnePriority = deviceOne.getSceneInfo().getHighPriority();
      let deviceTwoPriority = deviceTwo.getSceneInfo().getHighPriority();
      if (deviceOnePriority !== deviceTwoPriority) {
        return deviceTwoPriority - deviceOnePriority;
      }
      let deviceOneNegative = deviceOne.getSceneInfo().getNegativePriority();
      let deviceTwoNegative = deviceTwo.getSceneInfo().getNegativePriority();
      if (deviceOneNegative !== deviceTwoNegative) {
        return deviceTwoNegative - deviceOneNegative;
      }
      let deviceOneNormal = deviceOne.getSceneInfo().getNormalPriority();
      let deviceTwoNormal = deviceTwo.getSceneInfo().getNormalPriority();
      if (deviceOneNormal !== deviceTwoNormal) {
        return deviceTwoNormal - deviceOneNormal;
      }
      let deviceOneEnergy = deviceOne.getSceneInfo().getVoiceAbsoluteEnergy();
      let deviceTwoEnergy = deviceTwo.getSceneInfo().getVoiceAbsoluteEnergy();
      if (deviceOneEnergy !== deviceTwoEnergy) {
        return deviceTwoEnergy - deviceOneEnergy;
      }
      let deviceOneConfidence = deviceOne.getSceneInfo().getVoiceConfidence();
      let deviceTwoConfidence = deviceTwo.getSceneInfo().getVoiceConfidence();
      if (deviceOneConfidence !== deviceTwoConfidence) {
        return deviceTwoConfidence - deviceOneConfidence;
      }
      let deviceOneId = DataFormatter.byteArray2Int(deviceOne.getDeviceId());
      let deviceTwoId = DataFormatter.byteArray2Int(deviceTwo.getDeviceId());
      return deviceTwoId - deviceOneId;
    })
    return deviceDataLists;
  }

  private coordinateWithAllDevices(deviceDataLists: List<DeviceData>): List<DeviceData> {
    this.getFinalDeviceResult(deviceDataLists);
    return deviceDataLists;
  }

  /**
   * 获取最终结果的函数
   * 使用generalDeviceComparator比较设备，获得最高优先级的设备之后将其放在设备信息列表第1个
   *
   * @param deviceDataLists 设备信息列表
   */
  private getFinalDeviceResult(deviceDataLists: List<DeviceData>): List<DeviceData> {
    WakeUpLog.i(TAG, "coordinateWithAllDevices START");
    deviceDataLists.sort((deviceOne, deviceTwo) => {
      let deviceOnePriority = deviceOne.getSceneInfo().getHighPriority();
      let deviceTwoPriority = deviceTwo.getSceneInfo().getHighPriority();
      if (deviceOnePriority !== deviceTwoPriority) {
        return deviceTwoPriority - deviceOnePriority;
      }
      let deviceOneNegative = deviceOne.getSceneInfo().getNegativePriority();
      let deviceTwoNegative = deviceTwo.getSceneInfo().getNegativePriority();
      if (deviceOneNegative !== deviceTwoNegative) {
        return deviceTwoNegative - deviceOneNegative;
      }
      let deviceOneNormal = deviceOne.getSceneInfo().getNormalPriority();
      let deviceTwoNormal = deviceTwo.getSceneInfo().getNormalPriority();
      if (deviceOneNormal !== deviceTwoNormal) {
        return deviceTwoNormal - deviceOneNormal;
      }
      // pc rule
      let deviceOneDeviceType = deviceOne.getSceneInfo().getDeviceType();
      let deviceTwoDeviceType = deviceTwo.getSceneInfo().getDeviceType();
      if (deviceOneDeviceType !== deviceTwoDeviceType) {
        return deviceTwoDeviceType - deviceOneDeviceType;
      }

      let deviceOneEnergy = deviceOne.getSceneInfo().getVoiceAbsoluteEnergy();
      let deviceTwoEnergy = deviceTwo.getSceneInfo().getVoiceAbsoluteEnergy();
      if (deviceOneEnergy !== deviceTwoEnergy) {
        return deviceTwoEnergy - deviceOneEnergy;
      }
      let deviceOneConfidence = deviceOne.getSceneInfo().getVoiceConfidence();
      let deviceTwoConfidence = deviceTwo.getSceneInfo().getVoiceConfidence();
      if (deviceOneConfidence !== deviceTwoConfidence) {
        return deviceTwoConfidence - deviceOneConfidence;
      }
      let deviceOneId = DataFormatter.byteArray2Int(deviceOne.getDeviceId());
      let deviceTwoId = DataFormatter.byteArray2Int(deviceTwo.getDeviceId());
      return deviceTwoId - deviceOneId;
    })
    return deviceDataLists;
  }
}