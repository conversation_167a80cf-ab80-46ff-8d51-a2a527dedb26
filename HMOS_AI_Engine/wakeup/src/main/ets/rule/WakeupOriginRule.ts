/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { DeviceData } from '../entity/DeviceData';
import { BaseCoordinationRule } from './BaseCoordinationRule';
import List from '@ohos.util.List';

const TAG = "WakeupOriginRule";

/**
 * 协同唤醒原始规则
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-28
 */
export class WakeupOriginRule extends BaseCoordinationRule {
    /**
     * 获取规则协同结果接口
     *
     * @param deviceDataLists 设备信息列表
     * @return 返回协同规则设备响应结果
     */
    public doCoordinate(deviceDataLists: List<DeviceData>): List<DeviceData> {
        throw new Error('Method not implemented.');
    }
}