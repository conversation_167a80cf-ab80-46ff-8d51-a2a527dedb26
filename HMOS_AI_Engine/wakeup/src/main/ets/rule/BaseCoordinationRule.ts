/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import List from '@ohos.util.List';
import { DeviceData } from '../entity/DeviceData';
import { Constants } from '../utils/Constants';
import WakeUpLog from '../utils/WakeUpLog';

const TAG = "BaseCoordinationRule";

/**
 * 协同规则抽象类
 *
 * @version 1.0
 * @since 2024-03-28
 */
export abstract class BaseCoordinationRule {
    /**
     * 获取规则协同结果接口
     *
     * @param deviceDataLists 设备信息列表
     * @return 返回协同规则设备响应结果
     */
    public abstract doCoordinate(deviceDataLists:List<DeviceData>):List<DeviceData>;

    /**
     * 检查设备列表是否全是手机
     *
     * @param deviceDataLists 设备信息列表
     * @return 返回检查结果
     */
    public static isAllPhoneDevice(deviceDataLists:List<DeviceData>):boolean {
        if (deviceDataLists === null || deviceDataLists === undefined || deviceDataLists.length === 0) {
            WakeUpLog.d(TAG, "isAllPhoneDev:err");
            return false;
        }
        let isAllPhoneDevices = true;
        for (let deviceData of deviceDataLists) {
            let deviceType = deviceData.getSceneInfo().getDeviceType();
            if (deviceType === null || deviceType === undefined || deviceType !== Constants.DEVICE_TYPE_PHONE) {
                isAllPhoneDevices = false;
                break;
            }
        }
        return isAllPhoneDevices;
    }
}