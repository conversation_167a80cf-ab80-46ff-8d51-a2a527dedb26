/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import List from '@ohos.util.List';
import { WakeupDataParser } from '../data/WakeupDataParser';
import { DeviceData } from '../entity/DeviceData';
import { ArrayUtil } from '../utils/ArrayUtil';
import { Constants } from '../utils/Constants';
import { CoordinationUtil } from '../utils/CoordinationUtil';
import { FilterUtil } from '../utils/FilterUtil';
import WakeUpLog from '../utils/WakeUpLog';
import { BaseCoordinationRule } from './BaseCoordinationRule';
import { WakeupFirstImproveRule } from './WakeupFirstImproveRule';
import { WakeupOriginRule } from './WakeupOriginRule';
import { WakeupSecondImproveRule } from './WakeupSecondImproveRule';
import { DataFormatter } from '../utils/DataFormatter';

const TAG = "WakeupResponseManager";

const FIRST_RULE_VER = 1;

const SECOND_RULE_VER = 2;

/**
 * 协同唤醒规则管理类，对外提供唤醒响应设备结果
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-28
 */
export class WakeupResponseManager {
  /**
   * 获取协同唤醒需响应的设备结果
   *
   * @param deviceDataList 设备信息列表
   * @return 返回对应响应结果
   */
  public getCoordinateResult(deviceDataList: List<DeviceData>): DeviceData {
    WakeUpLog.i(TAG, "getCoordinateResult");
    if (deviceDataList === undefined || deviceDataList.isEmpty()) {
      WakeUpLog.d(TAG, "deviceDataList is empty");
      return new DeviceData();
    }
    //获取已响应设备
    let wakedData: DeviceData|undefined = this.getWakedDevice(deviceDataList);
    if (wakedData !== undefined && wakedData !== null) {
      WakeUpLog.d(TAG, "getCoordinateResult() other device waked already.");
      return wakedData;
    }
    let coordinateResult: DeviceData;

    // 只按照2号规则解析
    coordinateResult = this.soluteWithSecondImprovedRule(deviceDataList);
    return coordinateResult;
  }

  private getWakedDevice(deviceDataList: List<DeviceData>): DeviceData|undefined {
    let result: DeviceData = null;
    if (deviceDataList === undefined || deviceDataList.isEmpty()) {
      return result;
    }
    for (let deviceData of deviceDataList) {
      if (deviceData == null || deviceData.getOriginData() == null) {
        continue;
      }
      let bytes = deviceData.getOriginData();
      let isDeviceRespond = CoordinationUtil.isDeviceRespond(bytes);
      if (isDeviceRespond) {
        result = deviceData;
        break;
      }
    }
    return result;
  }

  /**
   * 检查是否有规则0协同的设备
   *
   * @param deviceDataList 设备信息列表
   * @return 返回对应检查结果
   */
  private hasRuleZeroDevice(deviceDataList: List<DeviceData>): boolean {
    for (let deviceData of deviceDataList) {
      if (deviceData.getWakeUpRuleVersion() === Constants.RULE_VERSION_ZERO) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否有规则1协同的设备
   *
   * @param deviceDataLists 设备信息列表
   * @return 返回对应检查结果
   */
  private hasRuleOneDevice(deviceDataList: List<DeviceData>): boolean {
    for (let deviceData of deviceDataList) {
      if (deviceData.getWakeUpRuleVersion() === Constants.RULE_VERSION_ONE) {
        return true;
      }
    }
    return false;
  }

  /**
   * 如果有0号规则的设备，则采用兼容方案
   * 首先按照0号规则方案解析，然后按照0号规则排序；
   * 如果0号规则排序结果不是老设备则重新按照1号规则数据解析，并将新设备使用新规则排序；
   *
   * @param deviceDataList 设备信息列表
   * @return 返回兼容方案最终响应结果
   */
  private soluteWithCompatibleMethod(deviceDataList: List<DeviceData>): DeviceData {
    let wakeupDataParser = new WakeupDataParser();

    // 按照0号规则解析
    for (let deviceData of deviceDataList) {
      deviceData.setSceneInfo(wakeupDataParser.byteToSceneInfo(deviceData.getOriginData(), Constants.RULE_VERSION_ZERO))
    }

    // 日志打印
    WakeUpLog.i(TAG, "Have rule 0 device, now coordinate with rule 0");

    // 按照0号规则排序，如果排序结果是0号规则设备则返回结果
    let coordinationRule = new WakeupOriginRule();
    let ruleZeroSortedList = coordinationRule.doCoordinate(deviceDataList);

    let ruleZeroResult = ruleZeroSortedList.get(0);
    if (ruleZeroResult.getWakeUpRuleVersion() === 0) {
      return ruleZeroResult;
    }

    // 过滤规则版本号不为0的设备信息
    ruleZeroSortedList = this.filter(ruleZeroSortedList, Constants.RULE_VERSION_ZERO);
    //ruleZeroSortedList = ruleZeroSortedList.stream().filter(dev -> dev.getWakeUpRuleVersion() != 0).collect(Collectors.toList());
    if (ruleZeroSortedList.length === 1) {
      return ruleZeroSortedList.get(0);
    }

    // 判断是否有1规则
    if (this.hasRuleOneDevice(ruleZeroSortedList)) {
      // 按照1号规则排序，如果排序结果是1号规则设备则返回结果
      return this.soluteWithFirstImprovedRule(ruleZeroSortedList);
    } else {
      // 假如排除完0号版本规则后只剩下2号版本规则的话，直接用2号规则排序
      return this.soluteWithSecondImprovedRule(ruleZeroSortedList);
    }
  }

  private filter(deviceDataLists: List<DeviceData>, rule: number): List<DeviceData> {
    let filterList = new List<DeviceData>();
    for (let deviceData of deviceDataLists) {
      if (deviceData.getWakeUpRuleVersion() !== rule) {
        filterList.add(deviceData);
      }
    }
    return filterList;
  }

  /**
   * 获取1号规则排序的结果
   *
   * @param deviceDataList 待排序设备信息列表
   * @return 返回最终结果
   */
  private soluteWithFirstImprovedRule(deviceDataList: List<DeviceData>): DeviceData {
    // 日志打印
    WakeUpLog.i(TAG, "Wakeup compatible rule doCoordinate::before rule1 sort:");
    for (let deviceData of deviceDataList) {
      WakeUpLog.i(TAG, deviceData.toString());
    }

    let wakeupDataParser = new WakeupDataParser();

    // 按照1号规则解析
    for (let deviceData of deviceDataList) {
      deviceData.setSceneInfo(wakeupDataParser.byteToSceneInfo(deviceData.getOriginData(), FIRST_RULE_VER));
    }

    // 按照1号规则排序
    let coordinationRule = new WakeupFirstImproveRule();
    let ruleOneSortedList = coordinationRule.doCoordinate(deviceDataList);

    // 日志打印
    WakeUpLog.i(TAG, "Wakeup compatible rule1 doCoordinate::sorted result:");
    for (let deviceData of ruleOneSortedList) {
      WakeUpLog.i(TAG, deviceData.toString());
    }

    // 对排序结果筛查
    let ruleOneResult: DeviceData;
    if (BaseCoordinationRule.isAllPhoneDevice(ruleOneSortedList)) {
      ruleOneResult = this.getFinalTopAllPhones(ruleOneSortedList, FIRST_RULE_VER);
    } else {
      ruleOneResult = this.getFinalTopWithAllDevices(ruleOneSortedList, FIRST_RULE_VER);
    }

    // 假如排序结果是1号版本规则的设备
    if (ruleOneResult.getWakeUpRuleVersion() === FIRST_RULE_VER) {
      return ruleOneResult;
    }

    // 过滤规则版本号不为1的设备信息
    ruleOneSortedList = FilterUtil.filter(ruleOneSortedList, Constants.RULE_VERSION_ONE);
    if (ruleOneSortedList.length === 1) {
      return ruleOneSortedList.get(0);
    }
    return this.soluteWithSecondImprovedRule(ruleOneSortedList);
  }

  private getFinalTopWithAllDevices(sortedDeviceDatas: List<DeviceData>, ruleVersion: number): DeviceData {
    // TODO 非全手机场景

    WakeUpLog.i(TAG, "getFinalTopAllPhones");
    if (ruleVersion !== FIRST_RULE_VER && ruleVersion !== SECOND_RULE_VER) {
      WakeUpLog.i(TAG, "ruleVersion in getFinalTopWithAllDevices not match");
      return sortedDeviceDatas.get(0);
    }
    return sortedDeviceDatas.get(0);
  }

  /**
   * 全手机场景下筛选最终结果
   *
   * @param sortedDeviceDatas 已经排序好的设备信息列表
   * @param ruleVersion 规则版本
   * @return 返回筛选后的结果
   */
  private getFinalTopAllPhones(sortedDeviceDatas: List<DeviceData>, ruleVersion: number): DeviceData {
    WakeUpLog.i(TAG, "getFinalTopAllPhones");
    if (ruleVersion !== FIRST_RULE_VER && ruleVersion !== SECOND_RULE_VER) {
      WakeUpLog.i(TAG, "ruleVersion in getFinalTopAllPhones not match");
      return sortedDeviceDatas.get(0);
    }
    let tempTopDevice = sortedDeviceDatas.get(0);

    // 过滤无负面状态标志相同并且声强绝对值3以内(不包括3)的设备信息
    let filteredDeviceDatas = FilterUtil.filterDeviceByTop(sortedDeviceDatas, tempTopDevice);
    if (filteredDeviceDatas.length === 1) {
      return filteredDeviceDatas.get(0);
    }

    filteredDeviceDatas.sort((deviceOne, deviceTwo) => {

      let deviceOneConfidence = deviceOne.getSceneInfo().getVoiceConfidence();
      let deviceTwoConfidence = deviceTwo.getSceneInfo().getVoiceConfidence();
      if (deviceOneConfidence !== deviceTwoConfidence) {
        return deviceTwoConfidence - deviceOneConfidence;
      }
      let deviceOneId = DataFormatter.byteArray2Int(deviceOne.getDeviceId());
      let deviceTwoId = DataFormatter.byteArray2Int(deviceTwo.getDeviceId());
      return deviceTwoId - deviceOneId;
    })

    return filteredDeviceDatas.get(0);
  }

  private getDeviceNormalPriority(device: DeviceData): number {
    return device.getSceneInfo().getNormalPriority();
  }

  private getDeviceVoiEnergy(device: DeviceData): number {
    return device.getSceneInfo().getVoiceAbsoluteEnergy();
  }

  private getDeviceNegPriority(device: DeviceData): number {
    return device.getSceneInfo().getNegativePriority();
  }

  private getDeviceHighPriority(device: DeviceData): number {
    return device.getSceneInfo().getHighPriority();
  }

  private getDeviceType(device: DeviceData): number {
    return device.getSceneInfo().getDeviceType();
  }

  /**
   * 获取2号规则排序的结果
   *
   * @param ruleOneSortedLists 待排序设备信息列表
   * @return 返回最终结果
   */
  private soluteWithSecondImprovedRule(ruleOneSortedLists: List<DeviceData>): DeviceData {
    // 日志打印
    WakeUpLog.i(TAG, "Wakeup compatible rule doCoordinate::before rule2 sort:");
    for (let deviceData of ruleOneSortedLists) {
      WakeUpLog.i(TAG, deviceData.toString());
    }

    let wakeupDataParser = new WakeupDataParser();

    // 按照2号规则解析
    for (let deviceData of ruleOneSortedLists) {
      deviceData.setSceneInfo(wakeupDataParser.byteToSceneInfo(deviceData.getOriginData(), SECOND_RULE_VER));
    }

    // 按照2号规则排序
    let coordinationRule = new WakeupSecondImproveRule();
    let ruleTwoSortedLists = coordinationRule.doCoordinate(ruleOneSortedLists);

    // 日志打印
    WakeUpLog.i(TAG, "Wakeup compatible rule2 doCoordinate::sorted result:");
    for (let deviceData of ruleTwoSortedLists) {
      WakeUpLog.i(TAG, deviceData.toString());
    }

    // 对排序结果筛查
    if (BaseCoordinationRule.isAllPhoneDevice(ruleTwoSortedLists)) {
        return this.getFinalTopAllPhones(ruleTwoSortedLists, SECOND_RULE_VER);
    } else {
        let currentTopDevice = ruleTwoSortedLists.get(0);
        let topDeviceType: number = currentTopDevice.getSceneInfo().getDeviceType();
        if (topDeviceType !== Constants.DEVICE_TYPE_PHONE) {
            return currentTopDevice;
        }
        return this.getFinalTopWithAllDevices(ruleTwoSortedLists, SECOND_RULE_VER);
    }
  }
}
