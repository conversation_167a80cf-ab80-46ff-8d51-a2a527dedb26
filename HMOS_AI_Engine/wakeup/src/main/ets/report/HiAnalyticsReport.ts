/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
 
const TAG = "HiAnalyticsReport";

/**
 * HA打点工具
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-25
 */
export class HiAnalyticsReport {
    private deviceName:string = '';

    private static instance:HiAnalyticsReport = new HiAnalyticsReport();

    private constructor() {};

    /**
     * 获取HiAnalyticsReport实例
     *
     * @return HiAnalyticsReport实例
     */
    public static getInstance():HiAnalyticsReport {
        return HiAnalyticsReport.instance;
    }

    public setDeviceName(deviceName:string): void {
        this.deviceName = deviceName;
    }


}