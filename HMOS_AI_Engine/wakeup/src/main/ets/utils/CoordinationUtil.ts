/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { Constants } from './Constants';
import { DataFormatter } from './DataFormatter';
import WakeUpLog from './WakeUpLog';
import deviceInfo from '@ohos.deviceInfo';
import List from '@ohos.util.List';
import { CommonUtil } from './CommonUtil';
import { SceneInfo } from '../entity/SceneInfo';
import { DeviceData } from '../entity/DeviceData';


const TAG: string = "CoordinationUtil";

const THIRD_RULE_VERSION = 3;

const SECOND_RULE_VERSION = 2;

const TIME_COEFFICIENT = 5;

const MAX_VOICE_ENERGY = 127;

const MAX_LCD_VOICE_ENERGY = 255;

const MAX_RECENT_TIME = 63;

const MAX_HIGH_SCORE = 15;

const PUBLIC_DEVICE_IN_SERVICE = 4;

const SIX_BIT_TO_MOVE = 6;

const SEVEN_BIT_TO_MOVE = 7;

const EIGHT_BIT_TO_MOVE = 8;

const LOW_SIX_MASK = 0x3F;

const NOTIFY_INDEX = 12;

const ALREADY_RESPOND_MASK = 0x20;

const FIVE_BIT_OFFSET = 5;

const OTHER_DEVICE_WAKEUP = 1;

const DEFAULT_VALUE = -1;

const HIGH_SCORE_PUBLIC_DEVICE = 6;




/**
 * 唤醒协作设备信息构建工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-15
 */
export class CoordinationUtil {

  private static udIdMemory = "";
  /**
   * 手机一级唤醒后发送的探测广播包或入网事件构建闲时感知包  2.0及以下，可以兼容（组网包）
   *
   * @param messageId 消息id
   * @param deviceType 协同唤醒包类型
   * @param wakeupWord 唤醒词
   * @return 探测广播包/闲时感知包
   */
  public static buildBytes(messageId: number, deviceType: number, wakeupWord: number): Uint8Array {
    let sendMsgs = new Uint8Array(Constants.DATA_LEN_DEVICE_INFO);
    let offset = 0;

    // 第1字节：消息ID
    sendMsgs[offset++] = messageId;

    // 第2字节：设备类型
    sendMsgs[offset++] = deviceType;

    // 第3-6字节：顺序号（用户防重放）
    let intValue = Date.now() / Constants.MILLISEC_TO_SEC;
    sendMsgs[offset++] = (intValue >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (intValue >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (intValue >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = intValue & Constants.LOW_EIGHT_MASK;
    /*let sequenceNumbers = CoordinationUtil.getSequenceNumber();
    System.arraycopy(sequenceNumbers, 0, sendMsgs, offset, sequenceNumbers.length);
    offset += sequenceNumbers.length;*/

    // 第7-10字节：设备UDID哈希值
    let udidHash = CoordinationUtil.getOdidHash();
    sendMsgs[offset++] = (udidHash >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (udidHash >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (udidHash >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = udidHash & Constants.LOW_EIGHT_MASK;
    /*let deviceIds = DataFormatter.int2ByteArray(getUdid().hashCode());
    System.arraycopy(deviceIds, 0, sendMsgs, offset, deviceIds.length);
    offset += deviceIds.length;*/

    // 第11字节：唤醒词id
    sendMsgs[offset++] = wakeupWord;

    // 第12字节：预留位
    let any = 0;
    sendMsgs[offset++] = any;

    // 将本机的ip地址存放在后三位
    let localIpAddress = CommonUtil.getLocalIpAddress();
    sendMsgs[offset++] = (localIpAddress >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (localIpAddress >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (localIpAddress >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = localIpAddress & Constants.LOW_EIGHT_MASK;
    WakeUpLog.i(TAG, "buildBytes: " + JSON.stringify(sendMsgs));
    return sendMsgs;
  }

  /**
   * 顺序号，用于防重放。
   *
   * @return 顺序号
   */
  private static getSequenceNumber(): Uint8Array {
    let timestamp = Date.now() / Constants.MILLISEC_TO_SEC;
    WakeUpLog.i(TAG, "getSequenceNumber::" + timestamp);
    return DataFormatter.int2ByteArray(timestamp);
  }

  /**
   * 获取udid的hash值
   *
   * @returns hash值
   */
  public static getOdidHash(): number {
    let udid: string = deviceInfo.ODID;
    if (udid === undefined) {
      WakeUpLog.e(TAG, "udid permission fail")
    }
    let udidHash = 0;
    if (udid.length !== 0) {
      for (let index = 0; index < udid.length; index++) {
        const char = udid.charCodeAt(index);
        udidHash = ((udidHash << 5) - udidHash) + char;
        udidHash |= 0;
      }
    }
    return udidHash;
  }

  /**
   * 获取字符串的hash值
   *
   * @param value 要求hash的字符串
   * @returns hash值
   */
  public static getHash(value: string): number {
    let valueHash = 0;
    if (value.length !== 0) {
      for (let index = 0; index < value.length; index++) {
        const char = value.charCodeAt(index);
        valueHash = ((valueHash << 5) - valueHash) + char;
        valueHash |= 0;
      }
    }
    return valueHash;
  }

  /**
   * 获取当前字节是不是已经响应的设备
   *
   * @param deviceData 设备的十五个字节
   * @return 当前字节对应的设备是不是已经响应的设备
   */
  public static isDeviceRespond(deviceData: Uint8Array): boolean {
    if (deviceData === null || deviceData.length !== Constants.DATA_LEN_DEVICE_INFO) {
      return false;
    }
    if (deviceData[0] !== Constants.MSG_ID_DEVICE_COORDINATION) {
      return false;
    }
    let notifyFlag = (deviceData[NOTIFY_INDEX] & ALREADY_RESPOND_MASK) >> FIVE_BIT_OFFSET;
    // 1 是响应（抑制包）协同包 0是组网包
    WakeUpLog.d(TAG, "device waked flag: " + notifyFlag);
    return notifyFlag === OTHER_DEVICE_WAKEUP;
  }

  /**
   * 构建协同或抑制包
   *
   * @param sceneInfo 场景信息
   * @return Uint8Array数组
   */
  public static genDeviceInfo(sceneInfo: SceneInfo): Uint8Array {
    let sendMsgs = new Uint8Array(Constants.DATA_LEN_DEVICE_INFO);
    if (sceneInfo === undefined || sceneInfo === null) {
      WakeUpLog.i(TAG, "sceneInfo is null");
      return sendMsgs;
    }
    WakeUpLog.i(TAG, "genDeviceInfo::" + sceneInfo);

    let offset = 0;

    // 第1字节：消息ID
    sendMsgs[offset++] = Constants.MSG_ID_DEVICE_COORDINATION;

    // 第2字节：设备类型
    let deviceType = sceneInfo.getDeviceType();
    sendMsgs[offset++] = deviceType;

    // 第3-6字节：顺序号（用户防重放）
    let sequenceNumbers = Date.now() / Constants.MILLISEC_TO_SEC;
    sendMsgs[offset++] = (sequenceNumbers >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (sequenceNumbers >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (sequenceNumbers >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = sequenceNumbers & Constants.LOW_EIGHT_MASK;

    // 第7-10字节：设备UDID哈希值
    let deviceIds = CoordinationUtil.getOdidHash();
    sendMsgs[offset++] = (deviceIds >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (deviceIds >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (deviceIds >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = deviceIds & Constants.LOW_EIGHT_MASK;

    // 第11字节：保留bit + 声强绝对值大小
    offset = CoordinationUtil.fillVoiceEnergy(sceneInfo, sendMsgs, offset, deviceType);
    // 第12字节：声强等级2 bit位 + 当前设备语音助手最近使用时间后6位bit
    offset = CoordinationUtil.fillVoiceLevel(sceneInfo, sendMsgs, offset);
    // 第13字节：2个保留位 + 响应包标记位1bit(1:抑制包；0：协同包) +高优先级分值4bit +无负状态标志1bit,限制小于15
    offset = CoordinationUtil.fillHighPriorityScore(sceneInfo, sendMsgs, offset, deviceType);

    // 第14字节：声纹置信度
    let voiceConfidence = sceneInfo.getVoiceConfidence();
    sendMsgs[offset++] = voiceConfidence;

    // 第15字节：规则版本号+普通优先级状态分数
    CoordinationUtil.fillRuleVersion(sceneInfo, sendMsgs, offset, deviceType);
    WakeUpLog.i(TAG,
      "genDeviceInfo:: isLightScreenManually = " + sceneInfo.isLightScreenManually() + "; inService = "
        + sceneInfo.getInService() + "LowerPriority = " + sceneInfo.getLowerPriority() + "TimeStamp = "
        + sceneInfo.getSwitchTimeStamp());
    WakeUpLog.i(TAG, "sendMsgs = " + sendMsgs);
    return sendMsgs;
  }

  private static fillRuleVersion(sceneInfo: SceneInfo, sendMsgs: Uint8Array, offset: number, deviceType: number): void {
    // 第15字节：规则版本号+普通优先级状态分数
    let ruleVersion = CommonUtil.isThirdVersionOk() ? THIRD_RULE_VERSION : SECOND_RULE_VERSION;
    let fourBitToMove = 4;
    ruleVersion = (ruleVersion << fourBitToMove);
    let normalPriorityScore =
      CoordinationUtil.getNormalPriorityScore(deviceType & Constants.LOW_EIGHT_MASK, sceneInfo.getInService());
    sendMsgs[offset] = (ruleVersion | normalPriorityScore);
  }

  /**
   * 获取普通优先级
   *
   * @param deviceType 设备类型
   * @param inService 是否在服务中字段标识
   * @return 返回对应普通优先级分值
   */
  private static getNormalPriorityScore(deviceType: number, inService: number): number {
    let score = 0;
    let isDeviceInService = inService > 0;

    // 只有公有设备且在服务中才需要普通优先级加4
    let maxDeviceType = 255;
    if (deviceType >= Constants.PUBLIC_PRIVATE_DEVICE_BOUND && deviceType <= maxDeviceType && isDeviceInService) {
      score += PUBLIC_DEVICE_IN_SERVICE;
    }
    score = CoordinationUtil.limitValueRange(score, 0, MAX_HIGH_SCORE);
    return score;
  }

  private static fillHighPriorityScore(sceneInfo: SceneInfo, sendMsgs: Uint8Array, offset: number, deviceType: number): number {
    // 第13字节：2个保留位 + 响应包标记位1bit(1:抑制包；0：协同包) +高优先级分值4bit +无负状态标志1bit,限制小于15
    let highPriorityScore = CoordinationUtil.getHighPriorityScore(sceneInfo);
    highPriorityScore = (highPriorityScore << 1);
    let isNotNegative = (sceneInfo.getNegativePriority() & 0x01);
    sendMsgs[offset] = (highPriorityScore | isNotNegative);
    return offset + 1;
  }

  /**
   * 获取高优先级状态分值
   *
   * @param sceneInfo 设备唤醒数据
   * @return 高优先级状态分值
   */
  private static getHighPriorityScore(sceneInfo: SceneInfo): number {
    let score = 0;

    // 协同唤醒3.x后，为兼容2.x及以下设备 3.x公有设备优先于低版本设备 需要高优先级加6
    let maxDeviceType = 255;
    let deviceType = sceneInfo.getDeviceType() & Constants.LOW_EIGHT_MASK;
    if (CommonUtil.isThirdVersionOk() && deviceType >= Constants.PUBLIC_PRIVATE_DEVICE_BOUND
      && CommonUtil.canPressByHighScore(sceneInfo) && deviceType <= maxDeviceType) {
      score += HIGH_SCORE_PUBLIC_DEVICE;
    }
    WakeUpLog.i(TAG, "getHighPriorityScore:: high priority total score::" + score);
    return CoordinationUtil.limitValueRange(score, 0, MAX_HIGH_SCORE);
  }

  private static fillVoiceLevel(sceneInfo: SceneInfo, sendMsgs: Uint8Array, offset: number): number {
    // 第12字节：声强等级2 bit位 + 当前设备语音助手最近使用时间后6位bit
    let energyLevel = sceneInfo.getVoiceEnergyLevel();
    energyLevel = (energyLevel << SIX_BIT_TO_MOVE);
    let lastUseFromNow = CoordinationUtil.getLastUseTime(sceneInfo.getVaLastUseFromNow());
    lastUseFromNow = CoordinationUtil.limitValueRange(lastUseFromNow, 0, MAX_RECENT_TIME);
    sendMsgs[offset] = (energyLevel | lastUseFromNow);
    return offset + 1;
  }

  /**
   * 获取上次使用的时间（单位秒，除以5后的值）
   *
   * @param time 时间戳
   * @return 上次使用的时间
   */
  private static getLastUseTime(time: number): number {
    return Math.abs(time / TIME_COEFFICIENT);
  }

  private static fillVoiceEnergy(sceneInfo: SceneInfo, sendMsgs: Uint8Array, offset: number, deviceType: number): number {
    // 第11字节：保留bit + 声强绝对值大小
    let energyAbsoluteValue = sceneInfo.getVoiceAbsoluteEnergy();
    if ((deviceType & Constants.LOW_EIGHT_MASK) === Constants.DEVICE_TYPE_LCD) {
      energyAbsoluteValue = CoordinationUtil.limitValueRange(energyAbsoluteValue, 0, MAX_LCD_VOICE_ENERGY);
    } else {
      energyAbsoluteValue = CoordinationUtil.limitValueRange(energyAbsoluteValue, 0, MAX_VOICE_ENERGY);
    }
    sendMsgs[offset] = energyAbsoluteValue;
    return offset + 1;
  }

  private static limitValueRange(originValue: number, lowRange: number, highRange: number): number {
    if (lowRange >= highRange) {
      return originValue;
    }
    let result = Math.min(originValue, highRange);
    return Math.max(result, lowRange);
  }

  /**
   * 返回唤醒的设备id，本机的id放在第一位
   *
   * @param deviceDataList 返回结果时唤醒的设备列表
   * @param localDeviceData 本机的设备
   * @return 设备iD列表，本机的设备id放在第一位
   */
  public static getDeviceIdList(deviceDataList: List<DeviceData>, localDeviceData: DeviceData): List<number> {
    let deviceIdList: List<number> = new List<number>();
    if (localDeviceData == null || deviceDataList == null) {
      return deviceIdList;
    }

    let localDeviceId: number = new Number(localDeviceData.getDeviceId().toString()) as number;
    deviceIdList.add(localDeviceId);
    for (let device of deviceDataList) {
      if (device == null) {
        continue;
      }
      let deviceId: number = new Number(device.getDeviceId().toString()) as number;
      if (!deviceIdList.has(deviceId)) {
        deviceIdList.add(deviceId);
      }
    }
    return deviceIdList;
  }

  /**
   * 获取UDID
   *
   * @return udid
   */
  public static getOdid(): string {
    if (CoordinationUtil.udIdMemory !== "") {
      return CoordinationUtil.udIdMemory;
    }
    CoordinationUtil.udIdMemory = deviceInfo.ODID;
    WakeUpLog.e(TAG, "device odid:" + this.udIdMemory);
    if (CoordinationUtil.udIdMemory === "") {
      //todo 记得删除 没有udid给默认udid值
      CoordinationUtil.udIdMemory = "999";
      WakeUpLog.e(TAG, "odid permission fail")
    }
    let maxDeviceIdSize: number = 32;
    if (CoordinationUtil.udIdMemory.length >= maxDeviceIdSize) {
      CoordinationUtil.udIdMemory = CoordinationUtil.udIdMemory.substring(0, maxDeviceIdSize);
    }
    return CoordinationUtil.udIdMemory;
  }


  /**
   * 从工作包中获取抑制功能的序列号
   *
   * @param originData 工作包报文
   * @return 抑制功能的序列号
   */
  public static getResponseSn(originData: Uint8Array): number {
    let responseSn = 0;
    if (originData == null || originData.length < Constants.DATA_LEN_DEVICE_INFO) {
      WakeUpLog.i(TAG,
        "getResponseSn::originData == null || originData.length < Constants.DATA_LEN_DEVICE_INFO, return 0");
      return responseSn;
    }
    if ((originData[0] & Constants.LOW_EIGHT_MASK) !== Constants.MSG_ID_DEVICE_WORKING) {
      WakeUpLog.i(TAG, "getResponseSn::Not work package, return 0");
      return responseSn;
    }
    let index = 11;
    let hasResponseSn =
      (originData[index] & Constants.WORK_PACKAGE_RESPONSE_SN_MASK) === Constants.WORK_PACKAGE_RESPONSE_SN_MASK;
    if (!hasResponseSn) {
      WakeUpLog.i(TAG, "getResponseSn::Not has SN, return 0");
      return responseSn;
    }
    responseSn = (originData[index] & LOW_SIX_MASK) << EIGHT_BIT_TO_MOVE | originData[index + 1];
    return responseSn;
  }

  /**
   * 构建工作包/停止包
   *
   * @param messageId 消息ID
   * @param deviceType 设备类型
   * @param wakeupWord 唤醒词
   * @param responseSn 响应包序列号
   * @return 工作包/停止包
   */
  public static buildTaskBytes(messageId: number, deviceType: number, wakeupWord: number, responseSn: number): Uint8Array {
    let sendMsgs = new Uint8Array(Constants.DATA_LEN_DEVICE_INFO);
    let offset = 0;

    // 第1字节：消息ID
    sendMsgs[offset++] = messageId;

    // 第2字节：设备类型
    sendMsgs[offset++] = deviceType;

    // 第3-6字节：顺序号（用户防重放）
    let intValue = Date.now() / Constants.MILLISEC_TO_SEC;
    sendMsgs[offset++] = (intValue >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (intValue >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (intValue >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = intValue & Constants.LOW_EIGHT_MASK;

    // 第7-10字节：设备UDID哈希值
    let udidHash = CoordinationUtil.getOdidHash();
    sendMsgs[offset++] = (udidHash >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (udidHash >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = (udidHash >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
    sendMsgs[offset++] = udidHash & Constants.LOW_EIGHT_MASK;

    // 第11字节：唤醒词id
    sendMsgs[offset++] = wakeupWord;

    // 第12字节：1Bit（是否支持 判断同一次唤醒） + 1Bit(是否支持RoomID 预留) + 6bit（responseSN后14位中的前6bit）
    sendMsgs[offset++] = (1 << SEVEN_BIT_TO_MOVE | ((responseSn >> EIGHT_BIT_TO_MOVE) & LOW_SIX_MASK));

    // 第13字节：responseSN后14位中的后8bit
    sendMsgs[offset++] = (responseSn & Constants.LOW_EIGHT_MASK);

    // 第14、15字节：预留位 用于RoomID需求
    let any = 0;
    sendMsgs[offset++] = any;
    sendMsgs[offset++] = any;
    return sendMsgs;
  }
}
