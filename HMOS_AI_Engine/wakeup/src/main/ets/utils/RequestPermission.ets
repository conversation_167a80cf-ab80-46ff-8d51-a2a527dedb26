/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

import UIAbility from '@ohos.app.ability.UIAbility';
import window from '@ohos.window';
import abilityAccessCtrl, { Context, PermissionRequestResult, Permissions } from '@ohos.abilityAccessCtrl';
import { BusinessError } from '@ohos.base';
import common from '@ohos.app.ability.common';

const permissions: Array<Permissions> = ['ohos.permission.READ_CALENDAR'];

/**
 * 权限申请工具类
 *
 */
export default class EntryAbility extends UIAbility {
    // ...
    onWindowStageCreate(windowStage: window.WindowStage) {
        // Main window is created, set main page for this ability
        let context: Context = this.context;
        let atManager: abilityAccessCtrl.AtManager = abilityAccessCtrl.createAtManager();
        // requestPermissionsFromUser会判断权限的授权状态来决定是否唤起弹窗

        atManager.requestPermissionsFromUser(context, permissions).then((data: PermissionRequestResult) => {
            let grantStatus: Array<number> = data.authResults;
            let length: number = grantStatus.length;
            for (let i = 0; i < length; i++) {
                if (grantStatus[i] === 0) {
                    // 用户授权，可以继续访问目标操作
                } else {
                    // 用户拒绝授权，提示用户必须授权才能访问当前页面的功能，并引导用户到系统设置中打开相应的权限
                    return;
                }
            }
            // 授权成功
        }).catch((err: BusinessError) => {
            console.error(`Failed to request permissions from user. Code is ${err.code}, message is ${err.message}`);
        })
        // ...
    }
}

@Entry
@Component
struct Index {
    reqPermissionsFromUser(permissions: Array<Permissions>): void {
        let context: Context = getContext(this) as common.UIAbilityContext;
        let atManager: abilityAccessCtrl.AtManager = abilityAccessCtrl.createAtManager();
        // requestPermissionsFromUser会判断权限的授权状态来决定是否唤起弹窗
        atManager.requestPermissionsFromUser(context, permissions).then((data: PermissionRequestResult) => {
            let grantStatus: Array<number> = data.authResults;
            let length: number = grantStatus.length;
            for (let i = 0; i < length; i++) {
                if (grantStatus[i] === 0) {
                    // 用户授权，可以继续访问目标操作
                } else {
                    // 用户拒绝授权，提示用户必须授权才能访问当前页面的功能，并引导用户到系统设置中打开相应的权限
                    return;
                }
            }
            // 授权成功
        }).catch((err: BusinessError) => {
            console.error(`Failed to request permissions from user. Code is ${err.code}, message is ${err.message}`);
        })
    }

    // 页面展示
    build() {
        // ...
    }
}
