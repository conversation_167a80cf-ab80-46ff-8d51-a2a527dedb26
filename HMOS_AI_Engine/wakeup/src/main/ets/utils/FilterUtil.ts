/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { DeviceData } from '../entity/DeviceData';
import List from '@ohos.util.List';
import { Constants } from './Constants';

const TAG = "FilterUtil";

/**
 * 排序，拦截工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-02
 */
export class FilterUtil {

    /**
     * 对规则类型进行拦截
     *
     * @param deviceDataLists 设备信息列表
     * @param rule 需要拦截的规则类型
     * @returns 返回拦截后的结果
     */
    public static filter(deviceDataLists: List<DeviceData>, rule:number):List<DeviceData> {
        let filterList = new List<DeviceData>();
        for (let deviceData of deviceDataLists) {
            if (deviceData.getWakeUpRuleVersion() !== rule) {
                filterList.add(deviceData);
            }
        }
        return filterList;
    }

    /**
     * 过滤无负面状态标志相同并且声强绝对值3以内(不包括3)的设备信息
     *
     * @param deviceDataLists 设备信息列表
     * @returns 返回拦截后的结果
     */
    public static filterDeviceByTop(deviceDataLists: List<DeviceData>, tempTopDevice:DeviceData):List<DeviceData> {
        let filterList = new List<DeviceData>();
        let tempTopDevNegPrior = FilterUtil.getDeviceNegPriority(tempTopDevice);
        let tempTopDevVoiEnergy = FilterUtil.getDeviceVoiEnergy(tempTopDevice);
        for (let device of deviceDataLists) {
            let currDevNegPrior = FilterUtil.getDeviceNegPriority(device);
            let currDevVoiEnergy = FilterUtil.getDeviceVoiEnergy(device);
            let filterConditions = (currDevNegPrior === tempTopDevNegPrior)
                && (Math.abs(currDevVoiEnergy - tempTopDevVoiEnergy) < Constants.ENERGY_DIFFERENCE_THRESHOLD);
            if (filterConditions) {
                filterList.add(device);
            }
        }
        return filterList;
    }

    /**
     *
     * @returns
     */
    public static sort():List<DeviceData> {
        let sortList = new List<DeviceData>();

        return sortList;
    }

    private static getDeviceNegPriority(device:DeviceData):number {
        return device.getSceneInfo().getNegativePriority();
    }

    private static getDeviceVoiEnergy(device:DeviceData):number {
        return device.getSceneInfo().getVoiceAbsoluteEnergy();
    }
}
