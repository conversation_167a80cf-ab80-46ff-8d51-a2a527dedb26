/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

import WakeUpLog from './WakeUpLog';

// 字符串
const hello : string = "Hello";
let hello2: string = '大哥';
let hello3: string = "二哥";
let hello4: string = hello + "三哥 ${hello}" + '四哥 ${hello}';
console.log(hello + hello2 + hello3 + hello4);

// const 常量
const TAG : string = "TestUtil";

// 创建外部类
WakeUpLog.d(TAG, "init");

// number 数字
let num1 = 3.14;
let num2 = 3;
let num3 = 0.53;
let num4 = -58;
let num5 : number = 5e2;

// boolean
let bool1 : boolean = true;

// emun枚举
enum ColorSet {Red, Blue, Black, Green};
let color : ColorSet = ColorSet.Red;

// Union联合类型
type StringNum = string | number | boolean;
let stringNum : StringNum = 'string and number and boolean';
stringNum = 2;
stringNum = true;

// Aliases匿名类型（叔祖，函数，对象字面量活联合类型）提供名称，或为已有类型提供替代名称。
type Matrix = number[][];
type Handler = (s:string, no:number)=>string;

// 运算符与java一致

// 语句 if/else if/else
//     switch/case
//     ? :
//     for(let i = 0; i < 10; i++)
//     for(let ch of 'dfeghhh')
//     while()  do{}while()
//  break,continue,return
// try{} catch{} finally{}/throw

// 函数式申明
function addXY(x:string);
function addXY(x:string, y:string);   // 函数重载
function addXY(x:string, y?:string):string {
  // 获取时间戳
  Date.now().toString();
  // 类似于byte[8]并输出
  let encryptData = new Uint8Array(8);
  JSON.stringify(encryptData)


  let z:string = '${x} ${y}'
  return z;
}

// 可选参数
function addX(x?:string):string {
  if (x === undefined) {
    return 'null';
  }
  return x;
}
// 设置默认值
function addY(x:string = 'new'):string {
  if (x === undefined) {
    return 'null';
  }
  return x;
}
// 任意数量的参数
function  addXYZ(...x:string[]):string {
  let z:string = "";
  for (let n of x) {
    z += n;
  }
  return z;
}

export default class TestUtils {
  private _names: string = '';

  public set names(value: string) {
    this._names = value;
  }

  public get names(): string {
    return this._names;
  }

  private _sunName: string = '';

  public set sunName(value: string) {
    this._sunName = value;
  }

  public get sunName(): string {
    return this._sunName;
  }

  constructor(x:string, y:string) {
    this._names = x;
    this._sunName = y;
  }

  name():void {
    console.log(hello);
  }
}

let boj = new TestUtils('s', 'b');
boj.name();

let a = 4;
// function name(name：Type): Type
function myFunction(): number {

  return a * 4;
}
