/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import { DeviceData } from '../entity/DeviceData';

import { SceneInfo } from '../entity/SceneInfo';
import WakeUpLog from './WakeUpLog';

const TAG: string = "ReportUtil";

const INVALID_SCENE_LOG = "Invalid scene parameter";

const RULE_VERSION_KEY = "rv";

const RECEIVE_CHANNEL_TYPE = "rct";

const SCAN_TIME = "sct";

const TIME_STAMP = "ts";

const DEVICE_TYPE = "dt";

const UDID_HASH_CODE = "uid";

const WAKEUP_WORD = "wrd";

const RESPONSE_PACKAGE_NUMBER = "rpn";

const LASR_USE_FROM_NOW = "lt";

const VOICE_ENERGY_LEVEL = "ve";

const VOICE_ABSOLUTE_ENERGY = "va";

const VOICE_CONFIDENCE = "vc";

const IS_LIGHT_SCREEN_MANUALLY = "lsm";

const IS_IN_SERVICE = "is";

const IS_LOWER_PRIORITY = "ilp";

const WAKE_UP_ID = "wuid";

const WAKE_UP_DEV_NUM = "wn";

const ALL_DEV_NUM = "an";

const IS_HIGH_PRIORITY = "ihp";

const HIGH_PRIORITY_DEVICE_TYPE = "hpt";

const HIGH_PRIORITY_UDID = "hpid";

const MESSAGE_ID = "mId";

const HIGH_PRIORITY = "hp";

const NORMAL_PRIORITY = "np";

const NOT_NEGATIVE_PRIORITY = "nnp";

const IS_WIFI_OPEN = "iwo";

const IS_BLE_OPEN = "ibo";

const CURRENT_MAIN_CHANNEL = "cmc";

const ONLY_BLE_COORDINATION_DEVICE_COUNT = "obcdc";

const ONLY_WIFI_COORDINATION_DEVICE_COUNT = "owcdc";

const BOTH_WIFI_BLE_COORDINATION_DEVICE_COUNT = "bwbcdc";

const FIRST_LEVER_TIMER = "flt";

const SENDER_EVENT = 211;

const REC_COOR_PACKAGE_EVENT = 212;

const CALCULATION_EVENT = 213;

const REC_OTHER_PACKAGE_EVENT = 454;

const SECOND_RULE_VER = 2;

// 协同成功率打点,不同产品使用不同的id
const HIVIEW_ID_PHONE_TABLE = 991640608;

const HIVIEW_ID_TV = 991660048;

const HIVIEW_ID_MOBILE_TV = 991662006;

const HA_ID_OEM = 991662006;

const HA_ID_COOR_RESULT = 991670685;

/**
 * 运营打点类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-19
 */
export class ReportUtil {
  /**
   * 协同成功率打点,appId
   */
  public static COOR_APP = "app";
  /**
   * 协同成功率打点,开始时间
   */
  public static COOR_START_TIME = "startTime";
  /**
   * 协同成功率打点,结束时间
   */
  public static COOR_END_TIME = "endTime";
  /**
   * 协同成功率打点,设备类型
   */
  public static COOR_DEVICE_TYPE = "deviceType";
  /**
   * 协同成功率打点,设备id
   */
  public static COOR_DEVICE_ID = "deviceId";
  /**
   * 协同成功率打点,声强
   */
  public static COOR_SOUND_INTENSITY = "soundIntensity";
  /**
   * 协同成功率打点,是否亮屏
   */
  public static COOR_MANUAL = "manual";
  /**
   * 协同成功率打点,协同结果
   */
  public static COOR_RESULT = "result";
  /**
   * 协同成功率打点,协同主通道
   */
  public static COOR_CHANNEL = "channel";
  /**
   * 协同成功率打点,协同唤醒版本号
   */
  public static COOR_VERSION = "version";
  /**
   * 协同成功率打点,逃生通道
   */
  public static COOR_NEGATIVE_FLAG = "negetiveFlag";
  /**
   * 协同成功率打点,参与协同的设备数
   */
  public static COOR_DEVICE_NUM = "deviceNum";
  /**
   * 协同成功率打点,是否拾音态
   */
  public static COOR_ACTIVE = "active";
  /**
   * 协同成功率打点,协同id
   */
  public static COOR_WAKEUP_ID = "wuid";
  /**
   * 协同唤醒打点,app id
   */
  public static COOR_UUID = "uuid";
  /**
   * 协同唤醒打点,协同唤醒唯一id
   */
  public static COOR_WAKEUP = "wakeupId";
  /**
   * 协同唤醒打点,设备udid
   */
  public static COOR_UDID = "udid";
  /**
   * 协同唤醒打点,华为账号的hash值
   */
  public static COOR_HWID = "hwid";
  /**
   * 协同唤醒打点,协同唤醒的端到端时延,启动协同唤醒到返回决策结果的时间
   */
  public static COOR_TOTAL_TIME = "totalTime";
  /**
   * 协同唤醒打点,空间谱
   */
  public static COOR_SPATIAL_SPECTRUM = "spatialSpectrum";
  /**
   * 协同唤醒打点,wifi appid
   */
  public static COOR_WIFI_AP_ID = "wifiAPId";
  /**
   * 协同唤醒打点,规则版本号
   */
  public static COOR_RULE_VER = "ruleVer";
  /**
   * 协同唤醒打点,设备型号
   */
  public static COOR_MODEL = "model";
  /**
   * 协同唤醒打点,协同设备数量
   */
  public static COOR_COORDINATION_DEVICE_TOTAL = "coordinationDeviceTotal";
  /**
   * 协同唤醒打点,参与协同的设备列表
   */
  public static COOR_COORDINATION_DEVICE_LIST = "coordinationDeviceList";
  /**
   * 协同唤醒打点,参与协同的设备列表中的设备ID
   */
  public static COORDINATION_DEVICE_ID = "deviceid";
  /**
   * 协同唤醒打点,参与协同的设备列表中的设备名字
   */
  public static COORDINATION_DEVICE_TYPE_NAME = "devicetypename";
  /**
   * 协同唤醒打点,参与协同的设备列表中的设备发现类型
   */
  public static COORDINATION_NET_WORK_TYPE = "coordinationtype";
  /**
   * 协同唤醒打点,响应设备数量
   */
  public static COOR_ANSWER_DEVICE_NUM = "answerDeviceNum";
  /**
   * 协同唤醒打点,最终协同决策的规则
   */
  public static COOR_DECISION_TYPE = "decisionType";
  /**
   * 协同唤醒打点,协同唤醒开关状态
   */
  public static COOR_COORDINATION_SWITCH = "coordinationSwitch";
  /**
   * 协同唤醒打点,sdk版本号
   */
  public static COOR_SDK_VER = "sdkVer";
  /**
   * 协同唤醒打点,协同唤醒的位置
   */
  public static COOR_DEVICE_LOCATION = "deviceLocation";
  /**
   * 协同唤醒打点,是否处于逃生通道抑制
   */
  public static COOR_IS_ESCAPE = "isEscape";
  /**
   * 协同唤醒打点,是否处于单设备模式
   */
  public static COOR_IS_SINGLE = "isSingle";
  /**
   * 协同唤醒打点,是否被工作态抑制
   */
  public static COOR_IS_SUPPRESSION = "isSuppression";
  /**
   * 协同唤醒打点,本身唤醒得分
   */
  public static COOR_SELF_SCORE = "selfScore";
  /**
   * 协同唤醒打点,决策响应设备的最高得分
   */
  public static COOR_MAX_SCORE = "maxScore";
  /**
   * 协同唤醒打点,是否为误唤醒
   */
  public static COOR_IS_FALSE_WAKEUP = "isFalseWakeup";
  /**
   * 协同唤醒打点,二级唤醒成功前的误唤醒次数
   */
  public static COOR_FALSE_WAKEUP_COUNT = "falseWakeupCount";
  /**
   * 协同唤醒打点,参与协同的设备类型列表
   */
  public static COOR_COORDINATION_DEVICE_NUM = "coordinationDeviceNum";
  /**
   * 协同唤醒决策规则:极近
   */
  public static DECISION_TYPE_SUPER_NEAR = 1;
  /**
   * 协同唤醒决策规则:服务中
   */
  public static DECISION_TYPE_IN_SERVICE = 2;
  /**
   * 协同唤醒决策规则:设备类型
   */
  public static DECISION_TYPE_DEVICE_NAME = 3;
  /**
   * 协同唤醒决策规则:就近
   */
  public static DECISION_TYPE_NEAREST = 4;
  /**
   * 协同唤醒决策规则:设备id
   */
  public static DECISION_TYPE_DEVICE_ID = 5;
  /**
   * 协同唤醒决策规则:3.0非中心设备默认返回0
   */
  public static DECISION_TYPE_THIRD_DEFAULT = 0;
  /**
   * 协同唤醒决策规则:2.0设备默认返回100
   */
  public static DECISION_TYPE_SECOND_DEFAULT = 100;
  /**
   * 本设备发送闲时感知包和响应包时打点
   */
  public static DEVICE_ENTER_NETWORK_SEND_EVENT = 214;
  /**
   * 本设备接收闲时感知包和响应包时打点
   */
  public static DEVICE_ENTER_NETWORK_RECEIVE_EVENT = 215;
  private static localDeviceType: number;
  private static wakeUpRandomId: number;

  /**
   * 将发送的包的信息存起来，后面用于打点
   *
   * @param deviceInfo 发送的字节包信息
   * @param msgType 消息包类型
   */
  public static holdSendPackage(deviceInfo: Uint8Array, msgType: number): void {
    // TODO 打点实现
  }

  /**
   * 设置本地的设备类型
   *
   * @param outSceneInfo 本机的场景信息
   */
  public static setReportDeviceType(outSceneInfo: SceneInfo): void {
    if (outSceneInfo === undefined || outSceneInfo === null) {
      WakeUpLog.e(TAG, "setReportDeviceType get null outSceneInfo");
      return;
    }
    ReportUtil.localDeviceType = outSceneInfo.getDeviceType();
  }

  public static holdSenderReport(localData: DeviceData, outSceneInfo: SceneInfo): void {

  }

  /**
   * 设置唤醒的随机id
   *
   * @param wakeUpId 唤醒的随机id
   */
  public static setReportintWakeUpRandomId(wakeUpId: number): void {
    ReportUtil.wakeUpRandomId = wakeUpId;
  }
}