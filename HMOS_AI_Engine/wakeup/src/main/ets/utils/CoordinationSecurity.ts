/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import WakeUpLog from './WakeUpLog';
import libwakeupnapi from 'libwakeupnapi.so'
import util from '@ohos.util';
import cryptoFramework from '@ohos.security.cryptoFramework';
const TAG: string = "CoordinationSecurity";

const RANDOM_LENGTH = 21;

/**
 * 随机数长度
 */
const RANDOM_LENTH = 6;

/**
 * 秘钥长度
 */
const LEN = 16;

let wakeupSecurityGeneratorOne = '6im8h0gluh77NRp1qjg3Xw=='
let wakeupSecurityGeneratorTwo = 'CRyc8wcNWjM/Zt7MK6+ZEvy/f4p04d/zohb3bA+NwDCb/YR4Qh4d+az856hrkIh3'
let symKeyGenerator = cryptoFramework.createSymKeyGenerator("AES128");

/**
 * 唤醒协作数据加解密工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-15
 */
export class CoordinationSecurity {
  /**
   * 将数据加密
   *
   * @param originalData 加密前的数据
   * @return 加密报文
   */
  public static async getEncryptData(originalData: Uint8Array): Promise<Uint8Array> {
    if (originalData === undefined || originalData === null || originalData.length === 0) {
      // 传入数据异常，返回默认空Uint8
      WakeUpLog.i(TAG, "originalData is null");
      return new Uint8Array(RANDOM_LENGTH);
    }

    // 广播之前进行白盒加密
    // 原始数据data,随机数据random,最终传送random+加密后的数据secret
    let random = CoordinationSecurity.getRandomBytes();
    WakeUpLog.i(TAG, "random = " + JSON.stringify(random));
    let encryptData = new Uint8Array(random.length + originalData.length);
    encryptData.set(random, 0);
    // 如果系统被root则进行明文通讯，否则进行加密通讯
    let secret = await CoordinationSecurity.encryptBussinessData(random, originalData);
    encryptData.set(secret, RANDOM_LENTH);
    // 打印加密和解密后的报文
    WakeUpLog.i(TAG, "getEncryptData::sec data = " + JSON.stringify(secret) + "; dec data = "
      + JSON.stringify(await CoordinationSecurity.decryptBussinessData(random, secret)));
    return encryptData;
  }

  /**
   * 获取RANDOM_LENTH的安全随机数
   *
   * @return 安全随机数
   */
  public static getRandomBytes(): Uint8Array {
    let randomBytes = new Uint8Array(RANDOM_LENTH);
    randomBytes[0] = this.createRandom();
    randomBytes[1] = this.createRandom();
    randomBytes[2] = this.createRandom();
    randomBytes[3] = this.createRandom();
    randomBytes[4] = this.createRandom();
    randomBytes[5] = this.createRandom();

    return randomBytes;
  }

  public static createRandom(): number {
    try {
      const rand = cryptoFramework.createRandom();
      const randData = rand.generateRandomSync(1);
      const randomNum = randData.data[0] % 128;
      console.log("randomNum: " + randomNum);
      return randomNum;
    } catch (error) {
      console.error("[Promise]: error code: " + error.code + ", message is: " + error.message);
      throw error;
    }
  }
  /**
   * 加密业务数据
   *
   * @param random 安全随机数
   * @param data 待加密报文
   * @return 加密报文
   */
  public static async encryptBussinessData(random: Uint8Array, data: Uint8Array): Promise<Uint8Array> {
    let iv = new Uint8Array(LEN);
    let key = new Uint8Array(LEN);
    let result = await CoordinationSecurity.generateIvAndSessionKey(random, iv, key);
    key = Uint8Array.from(result.slice(0, LEN));
    iv = Uint8Array.from(result.slice(LEN, 2 * LEN));
    try {
      return await CoordinationSecurity.encrypt(data, key, iv);
    } catch (err) {
      WakeUpLog.w(TAG, "encrypt err");
    }
    CoordinationSecurity.fill(key, 0);
    CoordinationSecurity.fill(iv, 0);
    return new Uint8Array(0);
  }

  /**
   * 解密业务报文
   *
   * @param random 安全随机数
   * @param data 加密报文
   * @return 解密结果
   */
  public static async decryptBussinessData(random: Uint8Array, data: Uint8Array): Promise<Uint8Array> {
    let iv = new Uint8Array(LEN);
    let key = new Uint8Array(LEN);
    let result = await CoordinationSecurity.generateIvAndSessionKey(random, iv, key);
    key = Uint8Array.from(result.slice(0, LEN));
    iv = Uint8Array.from(result.slice(LEN, 2 * LEN));
    try {
      return await CoordinationSecurity.decrypt(data, key, iv);
    } catch (err) {
      WakeUpLog.w(TAG, "decrypt err");
    }
    CoordinationSecurity.fill(key, 0);
    CoordinationSecurity.fill(iv, 0);
    return new Uint8Array(0);
  }

  private static async encrypt(encrypted: Uint8Array, sessionKey: Uint8Array, iv: Uint8Array): Promise<Uint8Array> {
    if (encrypted == null || sessionKey == null || iv == null) {
      WakeUpLog.e(TAG, "decrypt Key is null");
      return new Uint8Array(0);
    }
    let blob: cryptoFramework.DataBlob = {
      data: sessionKey
    }
    let result: Uint8Array = new Uint8Array;
    await symKeyGenerator.convertKey(blob).then(async key => {
      let cipher = cryptoFramework.createCipher("AES128|CFB|NoPadding");
      let ivsData: cryptoFramework.DataBlob = {
        data: iv
      }
      let ivPs: cryptoFramework.IvParamsSpec = {
        iv: ivsData,
        algName: "IvParamsSpec"
      }
      await cipher.init(cryptoFramework.CryptoMode.ENCRYPT_MODE, key, ivPs).then(async err => {
        let plainText: cryptoFramework.DataBlob = {
          data: encrypted
        }
        await cipher.doFinal(plainText).then((finalOutput) => {
          result = finalOutput.data;
          WakeUpLog.e(TAG, "encrypt result:" + result);
        });
      })
    });
    CoordinationSecurity.fill(sessionKey, 0);
    CoordinationSecurity.fill(iv, 0);
    return result;
  }

  private static async decrypt(encrypted: Uint8Array, sessionKey: Uint8Array, iv: Uint8Array): Promise<Uint8Array> {
    if (encrypted == null || sessionKey == null || iv == null) {
      WakeUpLog.e(TAG, "decrypt Key is null");
      return new Uint8Array(0);
    }
    let blob: cryptoFramework.DataBlob = {
      data: sessionKey
    }
    let result: Uint8Array = new Uint8Array;
    await symKeyGenerator.convertKey(blob).then(async key => {
      let cipher = cryptoFramework.createCipher("AES128|CFB|NoPadding");
      let ivsData: cryptoFramework.DataBlob = {
        data: iv
      }
      let ivPs: cryptoFramework.IvParamsSpec = {
        iv: ivsData,
        algName: "IvParamsSpec"
      }
      await cipher.init(cryptoFramework.CryptoMode.DECRYPT_MODE, key, ivPs).then(async err => {
        let plainText: cryptoFramework.DataBlob = {
          data: encrypted
        }
        await cipher.doFinal(plainText).then((finalOutput) => {
          result = finalOutput.data;
        });
      })
    });
    CoordinationSecurity.fill(sessionKey, 0);
    CoordinationSecurity.fill(iv, 0);
    return result;
  }

  private static fill(array: Uint8Array, value: number): void {
    if (array == null) {
      return;
    }
    for (let i = 0; i < array.length; i++) {
      array[i] = value;
    }
  }

  private static async generateIvAndSessionKey(random: Uint8Array, iv: Uint8Array, key: Uint8Array): Promise<Uint8Array> {
    let rootKey = CoordinationSecurity.getRootKey();
    let result = new Uint8Array();
    await CoordinationSecurity.hmacSha256(random, rootKey).then(temp => {
      result = temp;
    });
    return result;
  }

  private static getRootKey(): Uint8Array {
    let base64 = new util.Base64Helper();
    let iv: Uint8Array = base64.decodeSync(wakeupSecurityGeneratorOne);
    let source: Uint8Array = base64.decodeSync(wakeupSecurityGeneratorTwo);
    let rootKey = CoordinationSecurity.wb_aes_decrypt_cbc_stub(iv, source);
    return rootKey === undefined ? new Uint8Array : rootKey;
  }

  private static wb_aes_decrypt_cbc_stub(iv: Uint8Array, source: Uint8Array): Uint8Array {
    try {
      let rootKey = new Uint8Array;
      return rootKey;
    } catch (error) {
      console.error("get rootKey fail ");
    }
    return new Uint8Array;
  }

  private static async hmacSha256(random: Uint8Array, rootKey: Uint8Array): Promise<Uint8Array> {
    let hmacSha256Generator = cryptoFramework.createSymKeyGenerator("AES256");
    let mac = cryptoFramework.createMac("SHA256");
    try {
      mac = cryptoFramework.createMac("SHA256");
    } catch (error) {
      WakeUpLog.e(TAG, "[Promise]: error code: " + error.code + ", message is: " + error.message);
    }
    //TODO rootkey获取结果校验
    rootKey = new Uint8Array([100, -65, -65, 85, 99, 92, -34, -45, 112, -21, 7, -52, 100, 89, 117, -15, -127, -94, -40, -48, 86, 23, 62, 92, -119, -46, -42, -12, 41, 124, 101, 41]);
    let keyBlob: cryptoFramework.DataBlob = {
      data: rootKey
    }
    let blob: cryptoFramework.DataBlob = {
      data: random
    }
    let result: Uint8Array = new Uint8Array();

    await hmacSha256Generator.convertKey(keyBlob).then(async symKey => {
      await mac.init(symKey).then(async () => {
        await mac.update(blob).then(async () => {
          await mac.doFinal().then(async (macOutput) => {
            result = macOutput.data;
          });
        });
      })
    })
    return result;
  }
}