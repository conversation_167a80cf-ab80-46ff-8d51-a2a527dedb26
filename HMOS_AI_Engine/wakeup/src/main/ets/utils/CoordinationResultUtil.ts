/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import { SceneInfo } from '../entity/SceneInfo';
import { CommonUtil } from './CommonUtil';

const TAG = "CoordinationResultUtil";

export class CoordinationResultUtil {
    private static instance = new CoordinationResultUtil();

    private deviceMap:Map<string, number>;

    private constructor() {
        this.deviceMap = CommonUtil.getMapByResId();
    }

    public static getInstance():CoordinationResultUtil {
        return CoordinationResultUtil.instance;
    }

    /**
     * 根据sceneInfo获取当前设备分数
     *
     * @param sceneInfo 设备信息
     * @return 当前设备分数
     */
    public getTotalScore(sceneInfo:SceneInfo):number {
        // TODO 好像是3.0的设置，暂时不写，看下下面是否使用
        let score = 0;
        return score;
    }
}