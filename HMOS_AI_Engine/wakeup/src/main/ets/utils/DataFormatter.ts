/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { Constants } from './Constants';

const TAG:string = "DataFormatter";

/**
 * 数据格式化
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-15
 */
export class DataFormatter {
    public static BITNUM_OF_THREE_BYTES = 24;

    public static BITNUM_OF_TWO_BYTES = 16;

    public static BITNUM_OF_ONE_BYTE = 8;

    public static LENGTH_OF_ARRAY = 4;

    /**
     * int值转byte数组
     *
     * @param intValue int值
     * @return byte数组
     */
    public static int2ByteArray(intValue:number):Uint8Array {
        let array = new Uint8Array(DataFormatter.LENGTH_OF_ARRAY);
        let index = 0;
        array[index++] = (intValue >> DataFormatter.BITNUM_OF_THREE_BYTES) & Constants.LOW_EIGHT_MASK;
        array[index++] = (intValue >> DataFormatter.BITNUM_OF_TWO_BYTES) & Constants.LOW_EIGHT_MASK;
        array[index++] = (intValue >> DataFormatter.BITNUM_OF_ONE_BYTE) & Constants.LOW_EIGHT_MASK;
        array[index++] = intValue & Constants.LOW_EIGHT_MASK;
        return array;
    }

    /**
     * byte数组转int
     *
     * @param bytes byte数组
     * @return byte数组
     */
    public static byteArray2Int(bytes:Uint8Array):number {
        if (bytes == null || bytes.length < DataFormatter.LENGTH_OF_ARRAY) {
            return 0;
        }
        let fourthByteIndex = 3;
        let thirdByteIndex = 2;
        let first = bytes[fourthByteIndex] & Constants.LOW_EIGHT_MASK;
        let second = (bytes[thirdByteIndex] & Constants.LOW_EIGHT_MASK) << DataFormatter.BITNUM_OF_ONE_BYTE;
        let third = (bytes[1] & Constants.LOW_EIGHT_MASK) << DataFormatter.BITNUM_OF_TWO_BYTES;
        let fourth = (bytes[0] & Constants.LOW_EIGHT_MASK) << DataFormatter.BITNUM_OF_THREE_BYTES;
        return first | second | third | fourth;
    }

    public static isUint8ArrayEqual(array1: Uint8Array, array2: Uint8Array): boolean {
        if (!array1 || !array2) {
            return false;
        }
        if (array1.length !== array2.length) {
            return false;
        }
        for (let i = 0; i < array1.length; i++) {
            if (array1[i] !== array2[i]) {
                return false;
            }
        }
        return true;
    }
}