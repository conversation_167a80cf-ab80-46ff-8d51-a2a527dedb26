/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

import { SceneInfo } from '../entity/SceneInfo';
import { Constants } from './Constants';
import access from '@ohos.bluetooth.access';
import WakeUpLog from './WakeUpLog';
import cryptoFramework from '@ohos.security.cryptoFramework';
import ble from '@ohos.bluetooth.ble';
const TAG: string = "CommonUtil";

/**
 * 协同唤醒通用工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-03
 */
export class CommonUtil {
  private static mIsThirdVersionOk: boolean;
  private static isSecondVersionOk: boolean = false;
  private static mDeviceMap: Map<string, number> = null;
  private static currentMainChannelType: string = "";

  /**
   * 设置协同方案3.0使用条件状态码
   *
   * @param isThirdVersionOk
   */
  public static setIsThirdVersionOk(isThirdVersionOk: boolean): void {
    CommonUtil.mIsThirdVersionOk = isThirdVersionOk;
  }

  /**
   * 获取协同方案3.0使用条件状态码
   *
   * @returns 获取协同方案3.0使用条件状态码
   */
  public static isThirdVersionOk(): boolean {
    return CommonUtil.mIsThirdVersionOk;
  }

  /**
   * 是不是公有设备
   *
   * @param deviceType 设备类型
   * @return true代表是
   */
  public static isPublicDevice(deviceType: number): boolean {
    return deviceType >= Constants.PUBLIC_PRIVATE_DEVICE_BOUND;
  }


  /**
   * 根据绑定Link apk的服务是否成功来决定走1.0还是2.0
   *
   * @param isBinderLinkServiceSuccess 绑服务结果是不是成功
   */
  public static setBinderLinkServiceResult(isBinderLinkServiceSuccess: boolean): void {
    CommonUtil.isSecondVersionOk = isBinderLinkServiceSuccess;
  }

  public static getIsSecondVersionOk(): boolean {
    return CommonUtil.isSecondVersionOk;
  }

  /**
   * 读取本地ip
   *
   * @return 本地ip地址
   */
  public static getLocalIpAddress(): number {
    //todo getip
    return 0;
  }

  /**
   * 判断蓝牙和wifi的开关打开情况
   *
   * @param localDeviceType 本机设备类型
   * @return 0代表只打开了蓝牙，1代表只打开了wifi，2代表打开了蓝牙和wifi，3全关闭
   */
  public static getChannelStatus(localDeviceType: number): string {
    let isBleOpen = access.getState() === access.BluetoothState.STATE_ON;
    WakeUpLog.i(TAG, "bleOpenStatus::" + isBleOpen);
    CommonUtil.currentMainChannelType = isBleOpen ? Constants.BLE_CHANNEL : Constants.BLE_WIFI_CLOSE;
    return CommonUtil.currentMainChannelType;
  }

  /**
   * 蓝牙连接设备列表
   *
   * @return 蓝牙连接设备列表
   */
  public static getBondedDevices(): Array<string> {
    return ble.getConnectedBLEDevices();
  }

  /**
   * wifi是不是打开
   *
   * @return true代表是
   */
  public static isWifiOn(): boolean {
    // TODO
    return false;
  }

  /**
   * 构建设备类型类
   *
   * @returns
   */
  public static getMapByResId(): Map<string, number> {
    if (CommonUtil.mDeviceMap === null || CommonUtil.mDeviceMap.size === 0) {
      CommonUtil.mDeviceMap = new Map<string, number>();
      // 公共设备125以上
      CommonUtil.mDeviceMap.set("builtincar", 240);
      CommonUtil.mDeviceMap.set("robot", 238);
      CommonUtil.mDeviceMap.set("ideahub", 235);
      CommonUtil.mDeviceMap.set("mobiletv", 230);
      CommonUtil.mDeviceMap.set("huaweilcd", 220);
      CommonUtil.mDeviceMap.set("oemlcd", 220);
      CommonUtil.mDeviceMap.set("huaweispeaker", 210);
      CommonUtil.mDeviceMap.set("hishow", 200);
      CommonUtil.mDeviceMap.set("tv", 150);
      // 私有设备125以下
      CommonUtil.mDeviceMap.set("smartwatch", 110);
      CommonUtil.mDeviceMap.set("earphone", 80);
      CommonUtil.mDeviceMap.set("phone", 50);
      CommonUtil.mDeviceMap.set("tablet", 40);
      CommonUtil.mDeviceMap.set("2in1", 30);
    }
    return CommonUtil.mDeviceMap
  }

  /**
   * 协同唤醒3.0是否需要通过高优先级+6，压制其他设备
   *
   * @param sceneInfo 设备信息
   * @return true:高优先级+6压制2.0设备 false：不需要+6压制
   */
  public static canPressByHighScore(sceneInfo: SceneInfo): boolean {
    if (sceneInfo == null) {
      WakeUpLog.w(TAG, "canPressByHighScore:: sceneInfo is null");
      return false;
    }
    if (!CommonUtil.isThirdVersionOk) {
      WakeUpLog.w(TAG, "canPressByHighScore:: isThirdVersionOK is false");
      return false;
    }

    let isCoorByAirlink = CommonUtil.isCoorByAirlink(sceneInfo);
    let isNegativeDevice = sceneInfo.getNegativePriority() === 0;
    WakeUpLog.i(TAG,
      "canPressByHighScore:: isNegativeDevice = " + isNegativeDevice + "; isCoorByAirlink = " + isCoorByAirlink);
    return !(isCoorByAirlink || isNegativeDevice);
  }

  /**
   * 当前3.0设备，是否回落到使用2.0Airlink决策
   *
   * @param sceneInfo 设备信息
   * @return true代表是
   */
  public static isCoorByAirlink(sceneInfo: SceneInfo): boolean {
    if (sceneInfo == null) {
      return false;
    }
    return CommonUtil.isTvScreenOff(sceneInfo);
  }

  private static isTvScreenOff(sceneInfo: SceneInfo): boolean {
    return (sceneInfo.getDeviceType() === Constants.DEVICE_TYPE_TV
      || Constants.DEVICENAME_HUAWEI_TV === (sceneInfo.getDeviceName()))
      && sceneInfo.getInService() !== Constants.DEVICE_IN_SERVICE;
  }

  /**
   * 产生随机的数
   *
   * @return 生成的随机数
   */
  public static getSecureRandomNumber(): number {
    try {
      const rand = cryptoFramework.createRandom();
      const randData = rand.generateRandomSync(1);
      const randomNum = randData.data[0] % (2147483647 + 1);
      return randomNum;
    } catch (error) {
      WakeUpLog.e(TAG, "NoSuchAlgorithmException in getSecureRandomNumber");
      return 0;
    }
  }
}