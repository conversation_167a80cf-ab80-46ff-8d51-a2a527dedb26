/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

import hilog from '@ohos.hilog';

/**
 * Set the default prefix tag
 */
const TAG_PREFIX = "WakeUp_";

const WAKEUP_LOG_DOMAIN_ID: number = 0x0001;

const isDLogCanPrint: boolean = hilog.isLoggable(WAKEUP_LOG_DOMAIN_ID, TAG_PREFIX, hilog.LogLevel.DEBUG);

/**
 * 日志打点工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-02
 */
export default class WakeUpLog {
    // Set the default prefix tag
    private static prefixTag: string = TAG_PREFIX;

    /**
     * Used to modify the default log prefix.
     * For example, the prefix of the NLU module can be set to "HiAI_NLU_"
     *
     * @param prefixTag the prefix of the module
     */
    static setPrefixTag(prefixTag: string): void {
        WakeUpLog.prefixTag = prefixTag;
    }

    /**
     * Outputs debug-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static d(tag: string, message: string): void {
        if (isDLogCanPrint) {
            hilog.debug(WAKEUP_LOG_DOMAIN_ID, TAG_PREFIX + tag, message);
        }
    }

    /**
     * Outputs info-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static i(tag: string, message: string): void {
        hilog.info(WAKEUP_LOG_DOMAIN_ID, TAG_PREFIX + tag, message);
    }

    /**
     * Outputs warn-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static w(tag: string, message: string): void {
        hilog.warn(WAKEUP_LOG_DOMAIN_ID, TAG_PREFIX + tag, message);
    }

    /**
     * Outputs error-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static e(tag: string, message: string): void {
        hilog.error(WAKEUP_LOG_DOMAIN_ID, TAG_PREFIX + tag, message);
    }

    /**
     * Outputs fatal-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static f(tag: string, message: string): void {
        hilog.fatal(WAKEUP_LOG_DOMAIN_ID, tag, TAG_PREFIX + message);
    }
}