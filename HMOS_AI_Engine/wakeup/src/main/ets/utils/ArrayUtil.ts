/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import List from '@ohos.util.List';
import { DeviceData } from '../entity/DeviceData';
import { DataFormatter } from './DataFormatter';
import WakeUpLog from './WakeUpLog';

const TAG = "ArrayUtil";

export class ArrayUtil {

  public static revert(encryptData: Uint8Array): Int8Array{
    let result: Int8Array = new Int8Array(encryptData.length);
    for (let i = 0; i < encryptData.length; i++) {
      if (encryptData[i] > 127) {
        result[i] = encryptData[i] - 256;
      }else {
        result[i] = encryptData[i];
      }
    }
    return result;
  }

  /**
   * 从列表中删除数据
   *
   * @param deviceList 数据列表
   * @param deviceData 设备
   *
   */
  public static removeDeviceFromList(deviceList: List<DeviceData>, deviceData: DeviceData): void {
    if (deviceData == null || deviceList == null || deviceData.getSceneInfo() == null) {
      WakeUpLog.e(TAG, "deviceData is null or deviceList is null!");
      return;
    }
    let deviceId = DataFormatter.byteArray2Int(deviceData.getDeviceId());
    let deviceType = deviceData.getSceneInfo().getDeviceType();
    let index = 0;
    let size = deviceList.length;
    while (index < size) {
      if (deviceList.get(index) == null) {
        continue;
      }
      let deviceDetectedId = DataFormatter.byteArray2Int(deviceList.get(index).getDeviceId());
      if (deviceList.get(index).getSceneInfo() == null) {
        continue;
      }
      let deviceDetectedType = deviceList.get(index).getSceneInfo().getDeviceType();
      if ((deviceId === deviceDetectedId) && (deviceType === deviceDetectedType)) {
        deviceList.removeByIndex(index);
        size--;
      } else {
        index++;
      }
    }
  }

  /**
   * 判断设备是不是在列表中
   *
   * @param deviceDataLists 数据列表
   * @param deviceData 设备
   *
   * @return 判断设备是不是在列表汇总，true代表在
   */
  public static judgeDeviceInList(deviceDataLists: List<DeviceData>, deviceData: DeviceData): boolean {
    /*if (deviceDataLists == null || deviceData == null) {
      // 代表已经在列表里，不需要往列表里面加
      return true;
    }*/
    WakeUpLog.i(TAG, "judgeDeviceInList,deviceData:"+deviceData.toString());
    for (let tempDeviceData of deviceDataLists) {
      let tempOriginDatas = tempDeviceData.getDeviceId();
      let currentOriginDatas = deviceData.getDeviceId();
      if (ArrayUtil.isEqual(tempOriginDatas,currentOriginDatas)) {
        WakeUpLog.i(TAG, "deviceData bytes in list! no need add");
        return true;
      }
    }
    return false;
  }

  /**
   * 从探测列表、唤醒列表、未唤醒列表汇总删除设备
   *
   * @param detectDeviceList 探测包的设备
   * @param wakeupDeviceList 唤醒的设备
   * @param unwakeDeviceList 未唤醒设备
   * @param deviceData 接收到的设备
   *
   */
  public static removeLastTimeDevice(detectDeviceList: List<DeviceData>, wakeupDeviceList: List<DeviceData>,
                                     unwakeDeviceList: List<DeviceData>, deviceData: DeviceData): void {
    if (deviceData == null) {
      WakeUpLog.w(TAG, "deviceData is null");
      return;
    }
    ArrayUtil.filterPreviousData(detectDeviceList, deviceData);
    ArrayUtil.filterPreviousData(wakeupDeviceList, deviceData);
    ArrayUtil.filterPreviousData(unwakeDeviceList, deviceData);
  }


  private static filterPreviousData(deviceLists: List<DeviceData>, deviceData: DeviceData): void {
    if (deviceLists == null || deviceData == null) {
      return;
    }
    let filterTimeStamp = 2;
    let currentDeviceId = DataFormatter.byteArray2Int(deviceData.getDeviceId());
    let currentDeviceTimeStamp = deviceData.getSequenceNumber();

    let index = 0;
    let size = deviceLists.length;
    while (index < size) {
      if (deviceLists.get(index) == null) {
        continue;
      }
      let deviceDetectedId = DataFormatter.byteArray2Int(deviceLists.get(index).getDeviceId());
      let tempDeviceTimeStamp = deviceLists.get(index).getSequenceNumber();
      if ((currentDeviceId === deviceDetectedId)
        && ((currentDeviceTimeStamp - tempDeviceTimeStamp > filterTimeStamp))) {
        deviceLists.removeByIndex(index);
        size--;
        WakeUpLog.i(TAG, "timeStamp larger than 2,remove it");
      } else {
        index++;
      }
    }
  }

  public static isEqual(array1: Uint8Array, array2: Uint8Array): boolean {
    if (array1.length !== array2.length) {
      return false;
    }
    for (let i = 0; i < array1.length; i++) {
      if (array1[i] !== array2[i]) {
        return false;
      }
    }
    return true;
  }
}