/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

const TAG:string = "Constants";

/**
 * 唤醒协作相关常量
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04
 */
export class Constants {
    /**
     * TODO 初始版本号待定
     * 协同唤醒sdk版本号
     */
    public static VERSION_ID:string = "2025-07-08 1.0.0.221"

    /**
     * 海思的声强增益值
     */
    public static HAI_AI_GAIN:number =34;

    /**
     * 广播发送handle值
     */
    public static advNumber:number = -1;

    /**
     * 已开启任务map
     */
    public static timeoutMap: Map<number, number> = new Map<number, number>();

    /**
     * 空字符串
     */
    public static EMPTY_STR:string = "";

    /**
     * 毫秒转换成秒的单位
     */
    public static MILLISEC_TO_SEC:number =1000;

    /**
     * 取number的最后一位模板
     */
    public static LOW_ONE_MASK:number =0x01;

    /**
     * 取number的低八位模板
     */
    public static LOW_EIGHT_MASK:number =0xFF;

    /**
     * 取number的第一位
     */
    public static WORK_PACKAGE_RESPONSE_SN_MASK:number =0x80;

    /**
     * 取number的前4位
     */
    public static HIGH_FOUR_BITS_MASK:number = 0xF0;

    /**
     * 小于关系数字表示
     */
    public static LESS_RELATION_REPRESENT:number =-1;

    /**
     * 设备类型：音箱
     */
    public static DEVICE_TYPE_SPEAKER:number =200;

    /**
     * 设备类型：大屏
     */
    public static DEVICE_TYPE_TV:number =150;

    /**
     * 设备类型：Bloom
     */
    public static DEVICE_TYPE_BLOOM:number =230;

    /**
     * 设备类型：车
     */
    public static DEVICE_TYPE_CAR:number =240;

    /**
     * 设备类型：手机
     */
    public static DEVICE_TYPE_PHONE:number =50;

    /**
     * 设备类型：手表
     */
    public static DEVICE_TYPE_WATCH:number =110;

    /**
     * 设备类型：平板
     */
    public static DEVICE_TYPE_PAD:number =40;

    /**
     * 设备类型：LCD面板
     */
    public static DEVICE_TYPE_LCD:number =220;

    /**
     * 2.0最早决策时间定时器标识
     */
    public static TIMER_TYPE_EARLIEST_COORDINATION:number =0;

    /**
     * 2.0最晚决策时间定时器标识
     */
    public static TIMER_TYPE_LATEST_COORDINATION:number =1;

    // 2.0 最晚协同决策时间
    public static LATEST_COORDINATION_TIMER_VALUE_SECOND_VERSION:number =1300;

    // 2.0 最晚协同决策时间(与车机协同)
    public static LATEST_COORDINATION_TIMER_VALUE_SECOND_VERSIONS:number =2000;

    /**
     * 设备信息交换类型消息ID，手机一级唤醒的定时1300ms停止扫描和广播
     */
    public static TIMER_TYPE_CHANGE_NEARBY_CONTENT:number =2;

    /**
     * 第三个定时器，判断wifi的通道下要不要延长定时器
     */
    public static TIMER_TYPE_EXTENSION_TIMER:number =3;

    /**
     * 新增定时器，协同3.0，数据未收齐，1.3s兜底决策
     */
    public static TIMER_TYPE_COORDINATION_LAST_TIME:number =4;

    /**
     * 2.0方案消息ID和3.0方案消息ID的分界
     */
    public static TIMER_TYPE_AIRLINK_SOFTBUS_BOUND:number =10;

    /**
     * 软总线中心设备,超时清理软总线数据
     */
    public static TIMER_TYPE_SOFTBUS_COORDINATION_CLEAR:number =15;

    /**
     * 停止发送工作包/停止包的定时发送器
     */
    public static TIMER_TYPE_STOP_SEND_PACKAGE:number =11;

    /**
     * 工作包重复定时器
     */
    public static TIMER_TYPE_WORK_PACKAGE_RESTART:number =12;

    /**
     * 压制音量超时兜底定时器
     */
    public static TIMER_TYPE_INHIBITED_TIMEOUT:number =13;

    /**
     * 软总线中心设备300ms超时决策
     */
    public static TIMER_TYPE_SOFTBUS_COORDINATION:number =14;

    /**
     * BLE一级定时器大小
     */
    public static BLE_EARLIEST_COORDINATION_TIMER_VALUE:number =700;

    /**
     * BLE一级定时器大小(与车机协同)
     */
    public static BLE_EARLIEST_COORDINATION_TIMER_VALUES:number =1400;

    /**
     * wifi一级定时器大小
     */
    public static WIFI_EARLIEST_COORDINATION_TIMER_VALUE:number =500;

    /**
     * 工作包发包时长
     */
    public static WORK_PACKAGE_TIMER_VALUE:number =600;

    /**
     * 停止包发包时长
     */
    public static STOP_PACKAGE_TIMER_VALUE:number =1200;

    /**
     * 兜底时长内收不到工作包，自动停止
     */
    public static WORK_TIMEOUT_TIMER_VALUE:number =10000;

    /**
     * 2000ms发一次工作包
     */
    public static WORK_PACKAGE_RESTART_TIMER_VALUE:number =2000;

    /**
     * 设备信息交换类型消息ID，扫描到协同包
     */
    public static MSG_ID_DEVICE_COORDINATION:number =0;

    /**
     * 设备信息交换类型消息ID，手机一级唤醒扫描到发现包
     */
    public static MSG_ID_DEVICE_DETECTION:number =1;

    /**
     * 设备信息交换类型消息ID，应答包
     */
    public static MSG_ID_DEVICE_SUCCESS:number =3;

    /**
     * 设备没有唤醒的消息id
     */
    public static MSG_ID_DEVICE_NOT_WAKEUP:number =3;

    /**
     * 设备信息交换类型消息ID，手机一级唤醒的定时700ms的扫描
     */
    public static MSG_ID_DEVICE_FAILED:number =2;

    /**
     * 闲时入网感知消息id
     */
    public static MSG_ID_DEVICE_ENTER_NETWORK:number =4;

    /**
     * 闲时入网感知响应消息id
     */
    public static MSG_ID_DEVICE_ENTER_NETWORK_RESPONSE:number =5;

    /**
     * 设备信息交换类型消息ID，工作包
     */
    public static MSG_ID_DEVICE_WORKING:number =6;

    /**
     * 停止包id
     */
    public static MSG_ID_DEVICE_STOP:number =8;

    /**
     * Airlink入网事件
     */
    public static MSG_EVENT_TYPE_ENTER_NETWORK:number =1;

    /**
     * Airlink低功耗蓝牙消息包
     */
    public static MSG_EVENT_TYPE_LOW_POWER:number =2;

    /**
     * Airlink非入网事件,非低功耗,其他消息包
     */
    public static MSG_EVENT_TYPE_OTHER:number =0;

    /**
     * 第一轮收到的设备信息数据字段长度15
     */
    public static DATA_LEN_DEVICE_INFO:number =15;

    /**
     * 同设备的声强绝对值差值
     */
    public static ENERGY_DIFFERENCE_THRESHOLD:number =3;

    /**
     * 公有设备和私有设备的分界
     */
    public static PUBLIC_PRIVATE_DEVICE_BOUND:number =125;

    /**
     * 设备的最低位
     */
    public static DEVICE_LOW_BOUND:number =0;

    /**
     * 设备的最高位
     */
    public static DEVICE_HIGH_BOUND:number =255;

    /**
     * 公有设备表示
     */
    public static PUBLIC_DEVICE:number =-1;

    /**
     * 私有设备表示
     */
    public static PRIVACY_DEVICE:number =1;

    /**
     * 其它设备表示
     */
    public static OTHER_DEVICE:number =0;

    /**
     * 主动降低优先级时间
     */
    public static LOWER_PRIORITY_TIME:number =60 * 3 * 1000;

    /**
     * 主动提高优先级时间
     */
    public static HIGHER_PRIORITY_TIME:number =60 * 1000;

    /**
     * 第一次改变广播
     */
    public static FIRST_CHANGE_BROADCAST:number =1;

    /**
     * 第二次改变广播
     */
    public static SECOND_CHANGE_BROADCAST:number =2;

    /**
     * EMUI版本信息配置项
     */
    public static RO_BUILD_VERSION_EMUI:string = "ro.build.version.emui";

    /**
     * HarmonyOs版本信息配置项
     */
    public static HARMONY_OS_BUILD_VERSION:string = "hw_sc.build.platform.version";

    /**
     * BLE通信通道类型
     */
    public static BLE_CHANNEL:string = "0";

    /**
     * Wifi通信通道类型
     */
    public static WIFI_CHANNEL:string = "1";

    /**
     * 软总线数据包长度
     */
    public static SOFTBUS_DEVICE_PACKAGE_SIZE:number =48;

    /**
     * 通信通道关闭了蓝牙和wifi
     */
    public static BLE_WIFI_CLOSE:string = "3";

    /**
     * 软总线结果包长度
     */
    public static SOFTBUS_RESULT_PACKAGE_SIZE:number =48;

    /**
     * 设备向中心设备请求决策结果时的上传信息id
     */
    public static SOFTBUS_DEVICE_REQUEST_MSG_ID:number =10;

    /**
     * 软总线决策结果信息id
     */
    public static SOFTBUS_RESULT_MSG_ID:number =11;

    /**
     * 软总线中心决策结果 2：没有决策 1：响应 0：不响应
     */
    public static SOFTBUS_COORDINATION_SHOULD_RESPONSE:number =1;

    /**
     * 软总线中心决策结果 2：没有决策 1：响应 0：不响应
     */
    public static SOFTBUS_COORDINATION_NO_RESULT:number =2;

    /**
     * 语音助手被退出的状态
     */
    public static VOICE_ASSISTANT_EXITED:number =1;

    /**
     * 语音助手被退出的状态
     */
    public static VOICE_ASSISTANT_WORKING:number =2;

    /**
     * 默认为-1的值
     */
    public static DEFAULT_VALUE:number =-1;

    /**
     * 压制语音助手的音量
     */
    public static PRESS_VOLUME_TO_VALUE:number =10;

    /**
     * 压制语音助手字段
     */
    public static VOICE_CALLBACK_PRESS_STATE:string = "press";

    /**
     * 解除压制语音助手字段
     */
    public static VOICE_CALLBACK_RELEASE_STATE:string = "unPress";

    /**
     * 数据加密数据随机数长度
     */
    public static RANDOM_NUM_LENGTH:number =6;

    /**
     * SceneInfo.getInService() 1为服务中 0为非服务中
     */
    public static DEVICE_IN_SERVICE:number =1;

    /**
     * 协同结果非3.0软总线协同，返回默认协同结果
     */
    public static COORDINATION_RESULT_DEVICENAME_DEFAULT:string = "";

    /**
     * 软总线设备列表中获取该设备的默认发现类型;默认：-1；软总线wifi：0；软总线ble：1；
     */
    public static NET_WORK_DEFAULT_TYPE:string = "-1";

    /**
     * 手机
     */
    public static DEVICENAME_PHONE:string = "phone";

    /**
     * 平板
     */
    public static DEVICENAME_HUAWEI_PAD:string = "huaweipad";

    /**
     * 大屏
     */
    public static DEVICENAME_HUAWEI_TV:string = "huaweitv";

    /**
     * 移动智慧屏
     */
    public static DEVICENAME_MOBILE_TV:string = "mobiletv";

    /**
     * OEM设备
     */
    public static DEVICENAME_OEM_LCD:string = "oemlcd";

    /**
     * 使用的解析规则版本0
     */
    public static RULE_VERSION_ZERO = 0;

    /**
     * 使用的解析规则版本1
     */
    public static RULE_VERSION_ONE = 1;

    /**
     * 使用的解析规则版本2
     */
    public static RULE_VERSION_TWO = 2;

    /**
     * 使用的解析规则版本3
     */
    public static RULE_VERSION_THREE = 3;
}