/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

import { CoordinatorResult } from './entity/CoordinatorResult';

/**
 * 唤醒协同监听接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-02
 */
export default interface IResultListener {
    /**
     * 唤醒协同结果回调
     *
     * @param coordinatorResult 协同唤醒结果数据
     */
    onResult(coordinatorResult: CoordinatorResult):void;

    /**
     * 协同发广播准备结果回调，唤醒判断是否可以拉起小艺主进程
     *
     * @param readySendPackage 协同发广播准备是否完成
     */
    onReady?(readySendPackage: boolean): void;
}