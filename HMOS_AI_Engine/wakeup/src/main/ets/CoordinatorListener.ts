/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

import { WakeupCoordinateCarrier } from './data/WakeupCoordinateCarrier';
import { DeviceData } from './entity/DeviceData';
import { SceneInfo } from './entity/SceneInfo';
import IResultListener from './IResultListener';

/**
 * 唤醒协同监听
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-03
 */
export interface CoordinatorListener {
  /**
   * 初始化成功
   */
  onInit(): void;

  /**
   * 2.0版本绑定服务成功
   */
  onRegisterCallbackSuccess(): void;

  /**
   * 连接断开
   */
  onBinderDied(): void;

  /**
   * 设置协同结果监听
   *
   * @param listener 协同结果监听
   */
  setResultListener(listener: IResultListener): void;

  /**
   * 启动唤醒协同
   *
   * @param responseListener 协同结果监听
   * @param sceneInfo 场景数据
   */
  doStart(responseListener: IResultListener, sceneInfo: SceneInfo): void;

  /**
   * 没有一级唤醒的，直接上报二级唤醒的，需要通过该接口去扫描，和广播二级包
   *
   * @param sceneInfo 二级唤醒时的场景信息
   */
  doWhenNoFirstLevelWakeup(sceneInfo: SceneInfo, resultListener: IResultListener): void;

  /**
   * 一级唤醒后开始启动和停止扫描和广播接口，优化手机单设备场景下不参与协同的响应时延
   *
   * @param sceneInfo 场景数据
   * @return true:启动或者停止扫描广播成功；false:启动或者停止扫描广播失败
   */
  doStartScanAdvertise(sceneInfo: SceneInfo): Promise<boolean>;

  /**
   * 切换通过nearby发送的数据
   *
   * @param sceneInfo 待切换发送的协同数据
   * @return 是否被切换发送数据成功
   */
  doChangeAdvertise(sceneInfo: SceneInfo): Promise<boolean>;

  /**
   * 停止通过nearby广播发送数据
   */
  doStopAdvertise(): void;

  /**
   * 停止nearby蓝牙扫描
   */
  doStopScan(): void;

  /**
   * 停止hilink的扫描和收包；
   *
   * @param isSecondWakeUpSuccess 本次是不是二级唤醒成功还是失败
   */
  doStopHiLinkAdvertistAndScan(isSecondWakeUpSuccess: boolean): void;

  /**
   * 一级唤醒后开始启动扫描和广播的定时器700ms和950ms
   *
   * @param time 定时时长，单位ms    long
   * @param type 定时器类型值   int
   */
  doStartTimer(time: number, type: number): void;

  /**
   * 停止定时
   *
   * @param type 定时器类型    int
   */
  doStopTimer(type: number): void;

  /**
   * 判断是否存在定时器类型
   *
   * @param type 定时器类型    int
   * @return ture 表示存在；否则为不存在
   */
  hasStartTimer(type: number): boolean;

  /**
   * 停止唤醒协同
   */
  doStop(): void;

  /**
   * 释放资源
   */
  doRelease(): void;

  /**
   * 开始发送{@link DeviceData}数据
   */
  onAdvertiseStart(): void;

  /**
   * {@link DeviceData}数据发送结束
   */
  onAdvertiseStop(): void;

  /**
   * 开始扫描
   */
  onScanStart(): void;

  /**
   * 停止扫描
   */
  onScanStop(): void;

  /**
   * 扫描到设备数据
   *
   * @param deviceData 设备数据
   * @param type 0:蓝牙通路； 1：wifi
   */
  onAwareResult(deviceData: DeviceData, type: string): void;

  /**
   * 响应唤醒协同结果
   *
   * @param isShouldResponse 当前设备是否需要响应
   * @param isCoordinator 当前设备是否参与协同
   * @param responseDeviceName 主响设备类型
   */
  onResult(isShouldResponse: boolean, isCoordinator: boolean, responseDeviceName: string): void;

  /**
   * 判断nearby是否初始化完成绑定
   *
   * @return 返回判断结果
   */
  isNearbyInitialed(): boolean;

  /**
   * 检查nearby是否可用
   *
   * @return 返回检查结果
   */
  isNearbyOk(): boolean;

  /**
   * 设置二级唤醒设备是否已经成功唤醒
   *
   * @param isOnWakeup 二级唤醒标志
   * @param isSecondWakeupResult 二级唤醒是成功还是失败
   */
  setOnWakeup(isOnWakeup: boolean, isSecondWakeupResult: boolean): void;

  /**
   * 是否协同唤醒蓝牙扫描定时是否结束
   *
   * @return 返回蓝牙扫描定时是否结束标志
   */
  getIsScanLongTimeout(): boolean;

  /**
   * 判断系统是否被root接口
   *
   * @return 返回0，表示未被root；否则，表示已root  int
   */
  getIsOsRooted(): number;

  /**
   * 向nearby实现类设置协同相关信息的载体类
   *
   * @param wakeupCoordinateCarrier 等待绑定nearby回调
   */
  setCoordinatorToNearby(wakeupCoordinateCarrier: WakeupCoordinateCarrier): void;

  /**
   * 设备入网
   */
  onDeviceEnterNetwork(): void;

  /**
   * 是否一级唤醒
   *
   * @return 是否一级唤醒
   */
  getFirstOnWakeup(): boolean;

  /**
   * 启动蓝牙10%的低功耗扫描
   */
  startLowPowerScan(): void;

}