/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import ble from '@ohos.bluetooth.ble';
import WakeUpLog from '../utils/WakeUpLog';
import { AsyncCallback, BusinessError, Callback } from '@ohos.base';
import access from '@ohos.bluetooth.access';
import deviceInfo from '@ohos.deviceInfo';
import { CoordinatorListener } from '../CoordinatorListener';
import { Constants } from '../utils/Constants';
import { DeviceData } from '../entity/DeviceData';
import { CoordinatorListenerImp } from '../CoordinatorListenerImp';
import { WakeupDataParser } from '../data/WakeupDataParser';
import { CoordinationSecurity } from '../utils/CoordinationSecurity';
import { ArrayUtil } from '../utils/ArrayUtil';

const TAG: string = "BleProxyImp";

/**
 * BLE广播功能实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-10
 */
export class BleProxyImp {
  private isCoodinatorFlag: boolean = false;
  private adv: boolean = true;
  private deviceType: number = 0;
  private static temp: Uint8Array;
  private static mCoordinatorListener: CoordinatorListenerImp | undefined = undefined;
  private static filterData = new Uint8Array;
  private static isBLEScanSuccess = false;
  private advNumber: number = -1;

  public setCoordinatorListener(coordinatorListener: CoordinatorListenerImp): void {
    WakeUpLog.w(TAG, "BleProxyImp constructor mCoordinatorListener define")
    BleProxyImp.mCoordinatorListener = coordinatorListener;
    BleProxyImp.mCoordinatorListener === undefined ? WakeUpLog.i(TAG, "setCoordinatorListener fail") : WakeUpLog.i(TAG, "setCoordinatorListener success");
  }

  private async onReceiveEvent(data: Array<ble.ScanResult>): Promise<void> {
    WakeUpLog.i(TAG, "BLE scan device find result = " + JSON.stringify(data));
    for (let result of data) {
      //解析result数据为deviceData
      let resultData: Uint8Array = new Uint8Array(result.data);
      if (ArrayUtil.isEqual(BleProxyImp.filterData,resultData)) {
        WakeUpLog.i(TAG, "repeat data");
        return;
      }
      BleProxyImp.filterData = resultData;
      let decryptDataCopy: Uint8Array = new Uint8Array(21);
      decryptDataCopy = resultData.slice(resultData.length-21, resultData.length);

      //解密
      let radom: Uint8Array = new Uint8Array(Constants.RANDOM_NUM_LENGTH);
      let businessData: Uint8Array = new Uint8Array(result.data.byteLength - Constants.RANDOM_NUM_LENGTH - 10);
      radom = decryptDataCopy.slice(0, 6);
      businessData = decryptDataCopy.slice(6, decryptDataCopy.length);
      //解密后数据
      let decryptData: Uint8Array = await CoordinationSecurity.decryptBussinessData(radom, businessData);
      let wakeupDataParser = new WakeupDataParser();

      // mSceneInfo此时位为数据
      let deviceData = wakeupDataParser.byteToDeviceData(decryptData);
      WakeUpLog.i(TAG, "deviceData = " + JSON.stringify(deviceData));
      if (BleProxyImp.mCoordinatorListener === undefined) {
        WakeUpLog.e(TAG, "mCoordinatorListener is null ");
      } else {
        WakeUpLog.i(TAG, "mCoordinatorListener is not null ");
        deviceData.setChannelType(Constants.BLE_CHANNEL);
        BleProxyImp.mCoordinatorListener.onAwareResult(deviceData, Constants.BLE_CHANNEL);
      }
    }
  }

  //开始扫描
  public startBLEScan(): boolean {
    if (BleProxyImp.isBLEScanSuccess){
      return true;
    }
    try {
      WakeUpLog.i(TAG, "startBLEScan start");
      ble.on("BLEDeviceFind", this.onReceiveEvent);
      let manufactureDatas = new Uint8Array(1);
      manufactureDatas[0] = 0x10; //
      let manufactureDataMask = new Uint8Array(1);
      manufactureDataMask[0] = 0xff;

      let scanFilter: ble.ScanFilter = {
        manufactureId: 637,
        manufactureData: manufactureDatas.buffer,
        manufactureDataMask: manufactureDataMask.buffer
      };
      let scanOptions: ble.ScanOptions = {
        interval: 0,
        dutyMode: ble.ScanDuty.SCAN_MODE_LOW_LATENCY,
        matchMode: ble.MatchMode.MATCH_MODE_AGGRESSIVE,
      }
      ble.startBLEScan([scanFilter], scanOptions);
      BleProxyImp.isBLEScanSuccess = true;
      WakeUpLog.i(TAG, "startBLEScan end");
    } catch (err) {
      WakeUpLog.e(TAG, 'errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
    return BleProxyImp.isBLEScanSuccess;
  }

  public static stopBLEScan(): void {
    try {
      WakeUpLog.i(TAG, "stopBLEScan");
      BleProxyImp.isBLEScanSuccess = false;
      ble.stopBLEScan();
    } catch (err) {
      WakeUpLog.e(TAG, 'errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
  }


  //发送广播
  public startAdvertising(encryptData: Uint8Array): boolean {
    WakeUpLog.i(TAG, "startAdvertising start stopAdvertising number：" + Constants.advNumber);
    try {
      if (Constants.advNumber !== this.advNumber) {
        ble.stopAdvertising(Constants.advNumber).then(() => {
          this.startAdvertisingAfter(encryptData);
        });
      } else {
        this.startAdvertisingAfter(encryptData);
      }
    } catch (err) {
      WakeUpLog.e(TAG, 'errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
    WakeUpLog.i(TAG, "startAdvertising end stopAdvertising")
    return true;
  }

  public startAdvertisingAfter(encryptData: Uint8Array): boolean {
    this.adv = false;
    let manufactureValue = new Uint8Array(24);
    let tmp = new Uint8Array([0x10, 0x11, 0x01]);
    manufactureValue.set(tmp, 0);
    manufactureValue.set(encryptData, tmp.length);
    console.info('manufactureValue = ' + JSON.stringify(manufactureValue));
    let result = ArrayUtil.revert(manufactureValue);
    console.info('revert = ' + JSON.stringify(result));
    let setting: ble.AdvertiseSetting = {
      interval: 32,
      txPower: 1,
      connectable: false,
    };
    WakeUpLog.i(TAG, "manufactureDataUnit start");
    let manufactureDataUnit: ble.ManufactureData = {
      manufactureId: 637,
      manufactureValue: result.buffer
    };
    WakeUpLog.i(TAG, "advData start");
    let advData: ble.AdvertiseData = {
      serviceUuids: [],
      manufactureData: [manufactureDataUnit],
      serviceData: [],
    };
    let param: ble.AdvertisingParams = {
      advertisingSettings: setting,
      advertisingData: advData,
      duration: 50
    }
    ble.startAdvertising(param).then(outAdvHandle => {
      Constants.advNumber = outAdvHandle;
      WakeUpLog.i(TAG, "startAdvertising success");
    }).catch((err: BusinessError) => {
      WakeUpLog.i(TAG, "err:" + err.message);
      if (err.code === 2900099) {
        WakeUpLog.i(TAG, "startAdvertising again");
        ble.startAdvertising(param).then(outAdvHandle => {
          Constants.advNumber = outAdvHandle;
          WakeUpLog.i(TAG, "startAdvertising success");
        }).catch((err: BusinessError) => {
          WakeUpLog.i(TAG, "startAdvertising fail");
          return false;
        })
      }
    });
    WakeUpLog.i(TAG, "startAdvertising end");
    return true;
  }

  public static stopAdvertising(): void {
    try {
      WakeUpLog.i(TAG, "stopAdvertising");
      ble.stopAdvertising(Constants.advNumber);
    } catch (err) {
      WakeUpLog.e(TAG, 'errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
  }

  private onEvent(data: ble.AdvertisingStateChangeInfo): void {

    WakeUpLog.i(TAG, "bluetooth advertising state = " + JSON.stringify(data));
  }

  public on(): void {
    try {
      WakeUpLog.i(TAG, "on = start");
      ble.on('advertisingStateChange', this.onEvent);
    } catch (err) {
      WakeUpLog.e(TAG, 'errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
  }

  public off(): void {
    try {
      ble.off('advertisingStateChange', this.onEvent);
    } catch (err) {
      WakeUpLog.e(TAG, 'errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
  }

  /**
   * 检查蓝牙开光状态
   *
   * @returns true:蓝牙打开
   */
  public static isBluetoothOn(): boolean {
    try {
      let state = access.getState();
      if (state === access.BluetoothState.STATE_ON) {
        return true;
      }
    } catch (err) {
      WakeUpLog.e(TAG, 'errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
    return false;
  }

  /**
   * 注册蓝牙广播，监听蓝牙关闭时解绑nearby,需要修改适配一下，当蓝牙关闭，BLE广播无法使用
   */
  public static registerBluetoothReceiver(): void {
    // 进行蓝牙状态获取和监听
    try {
      let bluetoothStateCallback: Callback<access.BluetoothState> = (data: access.BluetoothState) => {
        WakeUpLog.i(TAG, "BluetoothState: " + JSON.stringify(data));
        if (data === access.BluetoothState.STATE_OFF) {
          // 蓝牙打开
          WakeUpLog.i(TAG, "BluetoothState.STATE_OFF");
        } else if (data === access.BluetoothState.STATE_ON) {
          // 蓝牙关闭
          WakeUpLog.i(TAG, "BluetoothState.STATE_ON");
        }
      };
      // 订阅蓝牙设备开关状态事件
      access.on('stateChange', bluetoothStateCallback);
    } catch (err) {
      console.error('errCode: ' + (err as BusinessError).code + ', errMessage: ' + (err as BusinessError).message);
    }
  }

  /**
   * 设置是不是协同了
   *
   * @param isCoodinator 是不是已经进入协同
   */
  public setCoodinatorFlag(isCoodinator: boolean): void {
    this.isCoodinatorFlag = isCoodinator;
  }

  /**
   * 设置设备类型
   *
   * @param deviceType 设备类型
   */
  public setDeviceType(deviceType: number): void {
    this.deviceType = deviceType;
  }
}

