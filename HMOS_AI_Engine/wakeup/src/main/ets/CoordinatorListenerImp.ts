/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

import { CoordinatorListener } from './CoordinatorListener';
import { WakeupCoordinateCarrier } from './data/WakeupCoordinateCarrier';
import { DeviceData } from './entity/DeviceData';
import { SceneInfo } from './entity/SceneInfo';
import { SoftBusImp } from './hwsoftbus/SoftBusImp';
import IResultListener from './IResultListener';
import { IStateChangeCallback } from './IStateChangeCallback';
import WakeUpLog from './utils/WakeUpLog';
import ble from '@ohos.bluetooth.ble';
import { StateMachine } from './entity/StateMachine';
import { CoordinationUtil } from './utils/CoordinationUtil';
import { Constants } from './utils/Constants';
import { CoordinationSecurity } from './utils/CoordinationSecurity';
import { CommonUtil } from './utils/CommonUtil';
import { ReportUtil } from './utils/ReportUtil';
import { State } from './entity/State';
import { WakeupDataParser } from './data/WakeupDataParser';
import List from '@ohos.util.List';
import { CoordinationResultUtil } from './utils/CoordinationResultUtil';
import { CoordinatorResult } from './entity/CoordinatorResult';
import systemDateTime from '@ohos.systemDateTime';
import { SequenceDataFilter } from './data/SequenceDataFilter';
import { WakeupResponseManager } from './rule/WakeupResponseManager';
import { DataFormatter } from './utils/DataFormatter';
import { ArrayUtil } from './utils/ArrayUtil';
import { BleProxyImp } from './ble/BleProxyImp';
import { BusinessError } from '@kit.BasicServicesKit';
import { map } from '@kit.ConnectivityKit';
import { HashMap } from '@kit.ArkTS';
import Fetch from '@system.fetch';
import { FilterUtil } from './utils/FilterUtil';
import { Context } from '@kit.AbilityKit';
import { distributedDeviceManager } from '@kit.DistributedServiceKit';

const TAG: string = "CoordinatorListenerImp";

/**
 * CoordinatorListener实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-03
 */
export class CoordinatorListenerImp implements CoordinatorListener {
  private mStateChangeCallback: IStateChangeCallback | undefined = undefined;
  private localDeviceData: DeviceData = new DeviceData();
  private mResultListener: IResultListener | undefined = undefined;
  private deviceName: string = '';
  private wakeupWord: number = 0;
  private mDeviceType: number = 0;
  private mSequenceNumber: number = 0;
  private changeNearbyTimeGap: number = 0;
  private highPriorIndex: number = 12;
  private HIGHT_PRIORITY: number = 0X3E;
  private lastChangeNearbyTime: number = 0;
  private NEARBY_CHANGE_TIME_GAP: number = 0;
  private mStartCoordinatingStamp: number = 0;
  private mTimestampToStartDetected: number = 0;
  private wakeUpRandomId: number = 0;
  private isOnWakeup: boolean = false;
  private isSecondWakeupSuccess: boolean = false;
  private isLastTimeOut: boolean = false;
  private isScanTimeout: boolean = false;
  private isFirstOnWakeup: boolean = false;
  private mIsScanLongTimeout: boolean = false;
  private isNearbyRuntimeError: boolean = false;
  private isShouldWakeupWhenWakeup: boolean = false;
  private isCoordinationSwitchOn: boolean = true;
  private isLatestCoordinationTimerStarted = false;
  private MAX_INHIBITED_VALUE = 2000;
  private PARSE_SN_FROM_TIMESTAMP = 0x3FFF;
  //抑制状态
  private isInhibited: boolean = false;
  // 协同结果是否返回flag
  private hasSentResult: boolean = false;
  private isScanLongTimeout: boolean = false;
  private resultMap: Map<string, string> = new Map<string, string>();
  private timeoutMap: Map<number, number> = new Map<number, number>();
  private tuneBaseReportDataMap: Map<string, unknown> = new Map<string, unknown>();
  private mDeviceDataList: List<DeviceData> = new List<DeviceData>();
  private coordinateResult: DeviceData = new DeviceData();
  private nearbyInitialed: boolean = false;
  private mWorkPackageSeqNum: number = Constants.DEFAULT_VALUE;
  private mLastInhibitedNum = 0;
  private static REPORT_RESPONSE: string = "1";
  private static REPORT_NOT_RESPONSE: string = "0";
  private mLastPressDeviceId = Constants.DEFAULT_VALUE;
  private finalFirstLevelTimer: number = 0;
  /**
   * 收到的探测包集合
   */
  private mDeviceDataListDetected: List<DeviceData> = new  List<DeviceData>();
  private notWakeUpDeviceList: List<DeviceData> = new List<DeviceData>();
  private responseData: Uint8Array = new Uint8Array(Constants.DATA_LEN_DEVICE_INFO);
  // 通过ble蓝牙收到的设备id
  private receivedBleDeviceIds: List<number> = new List<number>();
  //参与协同设备id
  private receivedDeviceIds: List<number> = new List<number>();
  private isNearbyInitialedState: boolean = false;
  private isRegisterCoordinationSuccess: boolean = false;
  private bleProxyImp: BleProxyImp = new BleProxyImp();
  private mSoftBusImp: SoftBusImp;
  private hwCar: string = "CAR";

  constructor(context: Context) {
    this.initCoListener();
    if (context !== null) {
      this.mSoftBusImp = new SoftBusImp(context, this);
    }
  }

  setCoordinatorToNearby(wakeupCoordinateCarrier: WakeupCoordinateCarrier): void {
    throw new Error('Method not implemented.');
  }

  /**
   * wifi、蓝牙通信初始化方法
   */
  private initCoListener(): void {
    this.bleProxyImp.setCoordinatorListener(this);
    WakeUpLog.i(TAG, "initCoListener.");
  }

  public setStateChangeCallback(callback: IStateChangeCallback): void {
    WakeUpLog.i(TAG, "setStateChangeCallback::setCallback");
    this.mStateChangeCallback = callback;
  }

  /**
   * 初始化成功
   */
  onInit(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 2.0版本绑定服务成功
   */
  onRegisterCallbackSuccess(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 连接断开
   */
  onBinderDied(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 软总线已连接设备
   */
  public checkSoftBusBuildCar() : boolean {
    WakeUpLog.i(TAG, "isFind car by SoftBus");
    let softBusDeviceList: Array<distributedDeviceManager.DeviceBasicInfo> = this.mSoftBusImp.getDeviceListBySoftBus();
    WakeUpLog.i(TAG, "list size: " + (softBusDeviceList?.length ?? 0));
    for (let i = 0; i < softBusDeviceList.length; i++) {
      WakeUpLog.i(TAG, "SoftBusDevice:{}" + softBusDeviceList[i].toString());
      if (this.hwCar === softBusDeviceList[i].deviceType) {
        WakeUpLog.i(TAG, "isConnectCarSoftBus:{}" + true);
        return true;
      }
    }
    WakeUpLog.i(TAG, "isConnectCarSoftBus:{}" + false);
    return false;
  }


  /**
   * 蓝牙已连接设备
   */
  public checkBluetoothBuildCar(): boolean {
    WakeUpLog.i(TAG, "isFind car by Bluetooth");
    let devices: Array<string> = CommonUtil.getBondedDevices();
    if (devices === null) {
      WakeUpLog.i(TAG, "list size: 0");
      return false;
    }
    WakeUpLog.i(TAG, "list size:{}" + devices.length);
    for (let i = 0; i < devices.length; i++) {
      WakeUpLog.i(TAG, "BluetoothDevice:{}" + devices[i].toString());

      if (devices[i].toString() === "CAR") {
        WakeUpLog.i(TAG, "isConnectCarBluetooth:{}" + true);
        return true;
      }
    }
    WakeUpLog.i(TAG, "isConnectCarBluetooth:{}" + false);
    return false;
  }

  /**
   * 设置协同结果监听
   *
   * @param listener 协同结果监听
   */
  public setResultListener(listener: IResultListener): void {
    WakeUpLog.i(TAG, "setStateChangeCallback::setListener");
    this.mResultListener = listener;
  }

  /**
   * 启动唤醒协同,对外发送协同包
   *
   * @param responseListener 协同结果监听
   * @param sceneInfo 场景数据
   */
  public async doStart(responseListener: IResultListener, sceneInfo: SceneInfo): Promise<void> {
    this.setFlagToCommunication();
    this.mStartCoordinatingStamp = Date.now();

    WakeUpLog.i(TAG, "doStart:: send cooditationPackage now");
    // 解析当前设备信息 转换
    let deviceInfo = CoordinationUtil.genDeviceInfo(sceneInfo);
    let wakeupDataParser = new WakeupDataParser();
    // mSceneInfo此时位为数据
    let deviceData = wakeupDataParser.byteToDeviceData(deviceInfo);
    // 记录当前设备发送设备状态广播事件打点
    ReportUtil.holdSenderReport(deviceData, sceneInfo);
    // 将本设备添加进总设备列表中
    if (!ArrayUtil.judgeDeviceInList(this.mDeviceDataList, deviceData)) {
      WakeUpLog.i(TAG, "deviceData add1:" + deviceData.toString());
    }
    deviceData.setDeviceId(DataFormatter.int2ByteArray(CoordinationUtil.getOdidHash()));
    this.mDeviceDataList.add(deviceData);
    WakeUpLog.i(TAG,
      "doStart:: added an coordination package to mDeviceDataList. size() = " + this.mDeviceDataList.length);
    this.localDeviceData = deviceData;
    //
    this.recordReportInfoForHa(sceneInfo);

    // 切换发送协同包，数据做简单加密
    let encryptData = await CoordinationSecurity.getEncryptData(deviceInfo);
    //发送广播
    this.bleProxyImp.startAdvertising(encryptData);
    this.lastChangeNearbyTime = Date.now();

    // 假如二级唤醒慢，超过T1定时器，而且已经满足了提前决策的条件，马上去决策了
    if (this.canCoordinateAdvance() && this.isScanTimeout
      && (StateMachine.getState() !== StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE
        && StateMachine.getState() !== StateMachine.STATE_DEVICE_INFO_START_FAILED_PACKAGE)) {
      if (this.isLastTimeOut) {
        WakeUpLog.i(TAG, "isLastTimeOut.");
        // 通知小艺协同唤醒结果（是否需要唤醒，是否协同人，相应设备名称）1300ms超时
        this.onResult(this.isShouldWakeupWhenWakeup, true, "");
      } else {
        WakeUpLog.i(TAG, "handleDeviceList when local device wakeup.");
        // 通过设别列表去进行决策是否要唤醒
        this.handleDeviceInfoList();
        StateMachine.setState(StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE);
      }
    }
  }

  /**
   * 处理收到的所有协同包数据
   */
  private handleDeviceInfoList(): void {
    WakeUpLog.i(TAG, "handleDeviceInfoList::coordination result: " + JSON.stringify(this.mDeviceDataList));
    // 如果设备列表为空或者状态机为未工作态，则直接响应。
    if ((this.mDeviceDataList.isEmpty()) || (StateMachine.getState() === StateMachine.STATE_NA)) {
      WakeUpLog.e(TAG, "No data in DeviceDataList, response now.");
      this.onResultDefault();
      return;
    }

    // 是否需要响应
    let wakeupResponseManager: WakeupResponseManager = new WakeupResponseManager();
    //根据设备列表决策响应设备
    this.coordinateResult = wakeupResponseManager.getCoordinateResult(this.mDeviceDataList);
    if (this.coordinateResult == null) {
      this.onResult(true, false, Constants.COORDINATION_RESULT_DEVICENAME_DEFAULT);
      return;
    }
    let shouldResponse: boolean = this.shouldRespond(this.coordinateResult.getDeviceId());
    this.onResult(shouldResponse, true, Constants.COORDINATION_RESULT_DEVICENAME_DEFAULT);
  }

  private onResultDefault(): void {
    this.onResult(true, true, Constants.COORDINATION_RESULT_DEVICENAME_DEFAULT);
  }

  /**
   * 根据协同结果判断当前设备是否需要响应唤醒
   *
   * @param topDeviceIdInList 设备ID数据，每4个byte表示一部设备。
   * @return 需要响应返回true，否则返回false。0
   */
  private shouldRespond(topDeviceIdInList: Uint8Array): boolean {
    WakeUpLog.i(TAG, "shouldRespond::topDeviceIdInList:" + JSON.stringify(topDeviceIdInList));
    //TODO 获取udid格式统一
    let currentDevId: Uint8Array = DataFormatter.int2ByteArray(CoordinationUtil.getOdidHash());
    WakeUpLog.i(TAG, "shouldRespond::currentDevId:" + JSON.stringify(currentDevId));
    let isNeedRespond: boolean = DataFormatter.isUint8ArrayEqual(topDeviceIdInList, currentDevId);
    WakeUpLog.i(TAG, "targetDevice? " + isNeedRespond + "!!!!!!");
    return isNeedRespond;
  }

  private uniqueArray(arr: List<DeviceData>): List<DeviceData> {
    let uniqueArr: List<DeviceData> = new  List<DeviceData>();
    for (let i = 0; i < arr.length; i++) {
      if (!uniqueArr.has(arr.get(i))) {
        uniqueArr.add(arr[i]);
      }
    }
    return uniqueArr;
  }

  /**
   * 进行低功耗设备（手机）协同包接收是否齐全状态处理
   *
   * @returns 是否已经收集器所有设备信息
   */
  private canCoordinateAdvance(): boolean {
    this.mDeviceDataListDetected = this.uniqueArray(this.mDeviceDataListDetected);
    this.mDeviceDataList = this.uniqueArray(this.mDeviceDataList);
    // 进行低功耗设备（手机）协同包接收是否齐全状态处理
    let count: number = this.mDeviceDataListDetected.length;
    WakeUpLog.i(TAG, "isCoordinateBefore: detected count = " + count + "; coordination count = " + this.mDeviceDataList.length);
    for (let deviceData of this.mDeviceDataList) {
      WakeUpLog.i(TAG, "deviceData:" + deviceData.toString());
      if (deviceData === null || deviceData === undefined) {
        continue;
      }

      let mDeviceId: number = DataFormatter.byteArray2Int(deviceData.getDeviceId());
      let mDeviceType: number = deviceData.getSceneInfo().getDeviceType();

      for (let deviceDataDetected of this.mDeviceDataListDetected) {
        WakeUpLog.i(TAG, "deviceDataDetected:" + deviceDataDetected.toString());
        if (deviceDataDetected == null) {
          continue;
        }
        let deviceDetectedId: number = DataFormatter.byteArray2Int(deviceDataDetected.getDeviceId());
        let deviceDetectedType: number = deviceDataDetected.getSceneInfo().getDeviceType();
        if ((mDeviceType === deviceDetectedType) && (mDeviceId === deviceDetectedId)) {
          WakeUpLog.e(TAG, "mDeviceId:" + mDeviceId + ",mDeviceType:" + mDeviceType);
          count--;
          break;
        }
      }
    }

    if (count === 0) {
      WakeUpLog.i(TAG, "isCoordinateBefore: final count = 0");
      return true;
    } else if (count > 0) {
      WakeUpLog.i(TAG, "isCoordinateBefore:final count = " + count);
      return false;
    } else {
      WakeUpLog.i(TAG, "isCoordinateBefore:final count < 0, final count = " + count);
      return false;
    }
  }

  private recordReportInfoForHa(sceneInfo: SceneInfo): void {
    this.recordReportData(ReportUtil.COOR_SELF_SCORE, CoordinationResultUtil.getInstance().getTotalScore(sceneInfo));
    this.recordReportData(ReportUtil.COOR_SPATIAL_SPECTRUM, sceneInfo.getSpatialSpectrum());
    this.recordReportData(ReportUtil.COOR_SOUND_INTENSITY, sceneInfo.getVoiceAbsoluteEnergy());
  }

  public recordReportData<T>(key: string, value: T): void {
    this.tuneBaseReportDataMap.set(key, value as unknown);
  }

  /**
   * 没有一级唤醒的，直接上报二级唤醒的，需要通过该接口去扫描，和广播二级包
   *
   * @param sceneInfo 二级唤醒时的场景信息
   * @param resultListener 回调接口
   */
  public async doWhenNoFirstLevelWakeup(sceneInfo: SceneInfo, resultListener: IResultListener): Promise<void> {
    this.setFlagToCommunication();
    this.mStartCoordinatingStamp = 0;
    this.mTimestampToStartDetected = Date.now();
    this.isNearbyRuntimeError = false;
    WakeUpLog.i(TAG, "doWhenNoFirstLevelWakeup");

    // 更新状态机
    StateMachine.setState(StateMachine.STATE_COORDINATOR_START_DETECTED);
    WakeUpLog.i(TAG, "start::startScan now");
    let isNearbyCorrect = true;
    if (!CommonUtil.getIsSecondVersionOk()) {
      isNearbyCorrect = this.bleProxyImp.startBLEScan();
    }
    if (!CommonUtil.getIsSecondVersionOk() && !isNearbyCorrect) {
      // 异常处理：一级唤醒扫描，如果nearby扫描接口异常；则重置nearby的可用性为false；
      this.isNearbyRuntimeError = true;

      // 取消定时器
      this.doStopTimer(1);
      this.doStopTimer(0);
      WakeUpLog.e(TAG, "startCoordinateScan failed.");
      return;
    }
    let mDeviceInfo = CoordinationUtil.genDeviceInfo(sceneInfo);
    let wakeupDataParser = new WakeupDataParser();
    let deviceData = wakeupDataParser.byteToDeviceData(mDeviceInfo);

    // 记录当前设备发送设备状态广播事件打点
    ReportUtil.holdSenderReport(deviceData, sceneInfo);
    this.mDeviceDataList.add(deviceData);
    this.localDeviceData = deviceData;
    this.recordReportInfoForHa(sceneInfo);

    WakeUpLog.i(TAG, "sdoWhenNoFirstLevelWakeup: sendMsg" + mDeviceInfo);

    // 切换发送协同包
    let encryptData = await CoordinationSecurity.getEncryptData(mDeviceInfo);

    // 协同发广播准备完毕，通知唤醒可以拉起小艺主进程
    resultListener.onReady(true);

    if (!CommonUtil.getIsSecondVersionOk() && this.bleProxyImp != null) {
      isNearbyCorrect = this.bleProxyImp.startAdvertising(encryptData);
    } else {
      WakeUpLog.w(TAG, "send detect package fail doWhenNoFirstLevelWakeup");
    }
    if (!isNearbyCorrect) {
      // 异常处理：一级唤醒启动广播，如果nearby广播接口异常；则重置nearby的可用性为false；
      this.isNearbyRuntimeError = true;

      // 同时需要把上一步开启的扫描停止掉
      this.doStopScan();

      // 并且取消定时器
      this.doStopTimer(1);
      this.doStopTimer(0);
      WakeUpLog.e(TAG, "startCoordinateAdvertise failed.");
    }
  }

  /**
   * 一级唤醒后开始启动和停止扫描和广播接口，优化手机单设备场景下不参与协同的响应时延
   *
   * @param sceneInfo 场景数据
   * @return true:启动或者停止扫描广播成功；false:启动或者停止扫描广播失败
   */
  public async doStartScanAdvertise(sceneInfo: SceneInfo): Promise<boolean> {
    // 一级唤醒，开始重置内部变量
    this.setFlagToCommunication();
    this.mStartCoordinatingStamp = 0;
    this.mTimestampToStartDetected = Date.now();
    this.isNearbyRuntimeError = false;
    WakeUpLog.i(TAG, "doStartScanAdvertise");

    // 更新状态机,协同5-》启动3
    StateMachine.setState(StateMachine.STATE_COORDINATOR_START_DETECTED);
    WakeUpLog.i(TAG, "start::startScan now");
    let isBLECorrect = this.bleProxyImp.startBLEScan();
    if (!CommonUtil.getIsSecondVersionOk() && !isBLECorrect) {
      // BLE接口异常，扫描接口打开失败
      this.isNearbyRuntimeError = true;

      // 取消相关定时器，不再处理
      this.doStopTimer(Constants.TIMER_TYPE_EARLIEST_COORDINATION);
      this.doStopTimer(Constants.TIMER_TYPE_LATEST_COORDINATION);
      WakeUpLog.e(TAG, "doStartScanAdvertise::startCoordinateScan failed.");
      return false;
    }

    // 1.0版本，只走蓝牙，获取设备信息
    let deviceType = sceneInfo.getDeviceType();
    // 获取本地信息
    let deviceInfoDetacted = CoordinationUtil.buildBytes(Constants.MSG_ID_DEVICE_DETECTION, deviceType, this.wakeupWord)
    // 对本地信息进行加密
    let encryptData = await CoordinationSecurity.getEncryptData(deviceInfoDetacted);
    WakeUpLog.i(TAG, "detect EncryptData is :" + JSON.stringify(encryptData));

    // 1.0 BLE广播发送设备信息
    isBLECorrect = this.bleProxyImp.startAdvertising(encryptData);
    if (!isBLECorrect) {
      // 异常处理：一级唤醒启动广播，如果BLE接口异常；则重置BLE的可用性为false
      this.isNearbyRuntimeError = true;

      // 同时需要把上一步开启的扫描停止掉
      this.doStopScan();

      // 并且取消已有的决策定时器
      this.doStopTimer(Constants.TIMER_TYPE_LATEST_COORDINATION);
      this.doStopTimer(Constants.TIMER_TYPE_EARLIEST_COORDINATION);
      WakeUpLog.e(TAG, "startCoordinateAdvertise failed");
      return false;
    }
    // 打点
    ReportUtil.holdSendPackage(deviceInfoDetacted, Constants.MSG_ID_DEVICE_DETECTION);
    return isBLECorrect;
  }

  private setFlagToCommunication(): void {
    if (this.bleProxyImp !== null && this.bleProxyImp !== undefined) {
      this.bleProxyImp.setCoodinatorFlag(true);
    }
  }

  /**
   * 切换通过nearby发送的数据
   *
   * @param sceneInfo 待切换发送的协同数据
   * @return 是否被切换发送数据成功
   */
  public async doChangeAdvertise(sceneInfo: SceneInfo): Promise<boolean> {
    let time = Constants.LATEST_COORDINATION_TIMER_VALUE_SECOND_VERSION
      - (Date.now() - this.mTimestampToStartDetected);
    WakeUpLog.i(TAG, "doChangeAdvertise to failed package. doStopTimer time=" + time);

    // 更新状态机
    StateMachine.setState(StateMachine.STATE_DEVICE_INFO_START_FAILED_PACKAGE);
    let deviceType = sceneInfo.getDeviceType();
    let deviceInfoFailed = CoordinationUtil.buildBytes(Constants.MSG_ID_DEVICE_FAILED, deviceType, this.wakeupWord);

    // 切换发送协同包
    let encryptData = await CoordinationSecurity.getEncryptData(deviceInfoFailed);
    if (!CommonUtil.getIsSecondVersionOk() && this.bleProxyImp != null) {
      this.bleProxyImp.startAdvertising(encryptData);
    } else {
      WakeUpLog.w(TAG, "doChangeAdvertise fail");
    }
    ReportUtil.holdSendPackage(deviceInfoFailed, Constants.MSG_ID_DEVICE_FAILED);
    return true;
  }

  /**
   * 停止通过nearby广播发送数据
   */
  public doStopAdvertise(): void {
    WakeUpLog.i(TAG, "doStopAdvertise");
    BleProxyImp.stopAdvertising();
  }

  /**
   * 停止nearby蓝牙扫描
   */
  public doStopScan(): void {
    if (this.bleProxyImp === null || this.bleProxyImp === undefined) {
      WakeUpLog.e(TAG, "bleProxyImp is null!!");
      return;
    }
    BleProxyImp.stopBLEScan();
  }

  /**
   * 停止hilink的扫描和收包；
   *
   * @param isSecondWakeUpSuccess 本次是不是二级唤醒成功还是失败
   */
  public doStopHiLinkAdvertistAndScan(isSecondWakeUpSuccess: boolean): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 一级唤醒后开始启动扫描和广播的定时器700ms和950ms
   *
   * @param time 定时时长，单位ms    long
   * @param type 定时器类型值   int
   */
  public async doStartTimer(time: number, type: number): Promise<void> {
    if (Constants.timeoutMap.has(type)) {
      WakeUpLog.i(TAG, "doStartTimer has type");
      clearTimeout(Constants.timeoutMap.get(type));
    }
    // setTimeout:创建异步的延时任务
    let timerId = setTimeout(() => {
      WakeUpLog.i(TAG, "startTimer type: " + type);
      this.handleMessage(type);
    }, time);
    WakeUpLog.i(TAG, "timerId:" + timerId);
    Constants.timeoutMap.set(type, timerId);
  }

  /**
   * 停止定时
   *
   * @param type 定时器类型    int
   */
  public doStopTimer(type: number): void {
    WakeUpLog.i(TAG, "doStopTimer:" + type);
    WakeUpLog.i(TAG, "timeoutMap:" + Array.from(Constants.timeoutMap.entries()));
    if (Constants.timeoutMap.has(type)) {
      clearTimeout(Constants.timeoutMap.get(type));
      Constants.timeoutMap.delete(type);
    }
  }

  /**
   * 判断是否存在定时器类型
   *
   * @param type 定时器类型    int
   * @return ture 表示存在；否则为不存在
   */
  public hasStartTimer(type: number): boolean {
    return Constants.timeoutMap.has(type);
  }

  /**
   * 协同内部定时消息处理，功能单一无法拆分
   *
   * @param msg 定时器ID
   */
  private async handleMessage(type: number): Promise<void> {
    if (type == null) {
      WakeUpLog.w(TAG, "handleMessage::type is null")
      return;
    }
    WakeUpLog.i(TAG, "handleMessage::type = " + type)
    if (type < Constants.TIMER_TYPE_AIRLINK_SOFTBUS_BOUND) {
      this.handleMessageFirstBatch(type);
    } else {
      this.handleMessageSecondBatch(type);
    }
  }

  /**
   * 协同唤醒2.0方案使用的定时器
   *
   * @param msg 信息（定时ID）
   */
  private handleMessageFirstBatch(type: number): void {
    WakeUpLog.i(TAG, "handleMessageFirstBatch");
    switch (type) {
      case Constants.TIMER_TYPE_EARLIEST_COORDINATION:
      // 0 最早协同决策时间700ms/500ms延迟(使用软总线通道，并且是wifi设置才延迟为500ms)
        WakeUpLog.i(TAG, "handleMessageFirstBatch::TIMER_TYPE_EARLIEST_COORDINATION");
        this.doWhenFirstLevelTimeOut();
        break;
      case Constants.TIMER_TYPE_LATEST_COORDINATION:
      // 1 最晚协同决策时间1300ms
        WakeUpLog.i(TAG, "handleMessageFirstBatch::TIMER_TYPE_LAdeviceDetectedType:TEST_COORDINATION");
        this.doWhenSecondTimeOut();
        break;
      case Constants.TIMER_TYPE_CHANGE_NEARBY_CONTENT:
      // 2 本地已经响应修改广播内容通知其他设备不需要响应
        WakeUpLog.i(TAG, "handleMessageFirstBatch::TIMER_TYPE_CHANGE_NEARBY_CONTENT");
        break;
      case Constants.TIMER_TYPE_EXTENSION_TIMER:
      // 3 如果使用的是Wifi进行数据发送可以根据情况进行一定的延时
        WakeUpLog.i(TAG, "handleMessageFirstBatch::TIMER_TYPE_EXTENSION_TIMER");
        break;
      case Constants.TIMER_TYPE_COORDINATION_LAST_TIME:
      // 4 软总线1300ms延迟
        WakeUpLog.i(TAG, "handleMessageFirstBatch::TIMER_TYPE_COORDINATION_LAST_TIME");
        break;
      default:
        WakeUpLog.i(TAG, "handleMessageFirstBatch: go to other case");
        break;
    }
  }

  private doWhenFirstLevelTimeOut(): void {
    WakeUpLog.i(TAG, "doWhenFirstLevelTimeOut");
    // 判断是不是wifi的主要通信通道，然后要不要延长定时器
    let currentMainChannelType = CommonUtil.getChannelStatus(this.getDeviceType());
    if (Constants.BLE_CHANNEL === currentMainChannelType) {
      //给设备类型赋值
      this.startTryToCoordinate();
      this.finalFirstLevelTimer = Constants.BLE_EARLIEST_COORDINATION_TIMER_VALUE;
      return;
    }
  }

  private doWhenSecondTimeOut(): void {
    this.coordinateLatestTime();

    // 不要乱动，可能影响时延
    this.doStopScan();
    this.doStopAdvertise();

    this.changeDeviceStateAtSecondTimeOut();
    this.resetCoordinationStatus();
  }

  private resetCoordinationStatus(): void {
    WakeUpLog.i(TAG, "resetCoordinationStatus");
    // 协同结束，更新并保存历史协同唤醒设备清单
    // 1.3秒超时时，开始将收到的wifi的探测包存入限制长度的类表中，并且存入数据库
    //this.HistoracalDeviceDataHadler.getInstance().updataLimiteQueueFromList();
    this.mDeviceDataListDetected.clear();
    this.mDeviceDataList.clear();
    this.notWakeUpDeviceList.clear();
    this.setFirstOnWakeup(false);
    this.isOnWakeup = false;
    this.isScanTimeout = false;
    this.isOnWakeup = false;
    this.hasSentResult = false;
    this.isLatestCoordinationTimerStarted = false;
    //this.responsePacketCount.set(0);
    this.receivedDeviceIds.clear();
    //this.ReportUtil.clearReportList();
    this.receivedBleDeviceIds.clear();
    this.resultMap.clear();
    this.tuneBaseReportDataMap.clear();
    this.wakeUpRandomId = 0;
    this.mResultListener = undefined;
    this.isLastTimeOut = false;
    this.isShouldWakeupWhenWakeup = false;
    Constants.advNumber = -1;
    Constants.timeoutMap.clear();
    // 二级唤醒成功，则将之前的误唤醒次数清零
    if (this.isSecondWakeupSuccess) {
      //this.falseWakeupCount.set(0);
    }
    WakeUpLog.e(TAG, "resetCoordinationStatus end");

  }

  private startTryToCoordinate(): void {
    this.isScanTimeout = true;

    // 只有自身二级唤醒了，700ms才能做协同
    if (!this.isOnWakeup || !this.isSecondWakeupSuccess) {
      WakeUpLog.i(TAG, "startTryToCoordinate::selfWakeup delayed or notifyOnWakeupFailed.");
      return;
    }

    // 是否单设备场景:没有收到周边任何协同包和探测包
    if (this.mDeviceDataListDetected.length <= 0 && this.mDeviceDataList.length <= 1) {
      WakeUpLog.i(TAG, "startTryToCoordinate::onResult called by mDeviceDataListDetected.size() = "
        + this.mDeviceDataListDetected.length);
      this.onResultDefault();
      this.coordinateResult = this.localDeviceData;
      StateMachine.setState(StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE);
    } else if (this.canCoordinateAdvance()) {
      WakeUpLog.i(TAG, "startTryToCoordinate::StateMachine.getNewState = " + StateMachine.getNewState());

      // 如果设备列表为空,则直接响应。
      if (this.mDeviceDataList.isEmpty()) {
        WakeUpLog.i(TAG,
          "startTryToCoordinate::onResult called by mDeviceDataList.isEmpty(). isShouldResponse = true");
        this.onResultDefault();
        StateMachine.setState(StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE);
        return;
      }
      this.handleDeviceInfoList();
      StateMachine.setState(StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE);
    } else {
      WakeUpLog.w(TAG, "startTryToCoordinate::dot not coordinate in advance.");
    }
  }


  /**
   * 协同唤醒3.0方案使用的定时器
   *
   * @param msg 定时器ID
   */
  private handleMessageSecondBatch(type: number): void {
    WakeUpLog.w(TAG, "handleMessageSecondBatch")

  }

  /**
   * 停止唤醒协同
   */
  public doStop(): void {
    WakeUpLog.i(TAG, "doStop::");
    StateMachine.setState(StateMachine.STATE_NA);
    this.doStopAdvertise();
    this.doStopScan();
  }

  /**
   * 释放资源
   */
  public doRelease(): void {
    WakeUpLog.i(TAG, "doRelease::" + this.bleProxyImp);
    this.mResultListener = undefined;
    this.mStateChangeCallback = undefined;
    this.doStopAdvertise();
    this.doStopScan();
    if (this.mSoftBusImp != null) {
      this.mSoftBusImp.unbindSoftBus();
    }
  }

  /**
   * 开始发送{@link DeviceData}数据
   */
  public onAdvertiseStart(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * {@link DeviceData}数据发送结束
   */
  public onAdvertiseStop(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 开始扫描
   */
  public onScanStart(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 停止扫描
   */
  public onScanStop(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 扫描到设备数据
   *
   * @param deviceData 设备数据
   * @param type 0:蓝牙通路； 1：wifi
   */
  public onAwareResult(deviceData: DeviceData, type: string): void {
    WakeUpLog.i(TAG, "onAwareResult::deviceData:" + deviceData);
    if (deviceData == null) {
      WakeUpLog.w(TAG, "onAwareResult:: deviceData is null");
      return;
    }
    if (deviceData.getDeviceType() === this.mDeviceType && this.mDeviceType === Constants.DEVICE_TYPE_CAR) {
      WakeUpLog.i(TAG, "onAwareResult:: car received another car data, not coordinate.");
      return;
    }
    let messageId = deviceData.getMessageId();
    WakeUpLog.i(TAG, "onAwareResult::messageId:" + messageId);
    if (messageId === Constants.MSG_ID_DEVICE_ENTER_NETWORK) {
      let encryptData = deviceData.getOriginData();
      if (this.contrasts(encryptData)) {
        WakeUpLog.w(TAG, "encryptData has responseWakeupCoordinate not neede send");
        return;
      }
      this.handleDeviceEnterNetwork(deviceData, true);
      return;
    }
    if (messageId === Constants.MSG_ID_DEVICE_ENTER_NETWORK_RESPONSE) {
      this.handleDeviceEnterNetwork(deviceData, false);
      return;
    }
    this.handleAwareResult(deviceData, messageId, type);
  }

  private contrasts(encryptData: Uint8Array): boolean {
    if (encryptData.length !== this.responseData.length) {
      this.responseData = encryptData;
      WakeUpLog.w(TAG, "responseData length is error");
      return false;
    }
    for (let i = 0; i < encryptData.length; i++) {
      if (encryptData[i] !== this.responseData[i]) {
        WakeUpLog.i(TAG, "encryptData: " + encryptData[i] + " responseData: " + this.responseData[i]);
        this.responseData = encryptData;
        return false;
      }
    }
    WakeUpLog.i(TAG, "encryptData equals responseData");
    return true;
  }

  private handleAwareResult(deviceData: DeviceData, messageId: number, type: string): void {
    let changleType: string = deviceData.getChannelType();
    let receiveDataDeviceId: number = DataFormatter.byteArray2Int(deviceData.getDeviceId());
    WakeUpLog.i(TAG, "handleAwareResult start");

    // 根据时间戳判断要不要删除已有的探测包或者协同包
    ArrayUtil.removeLastTimeDevice(this.mDeviceDataListDetected, this.mDeviceDataList, this.notWakeUpDeviceList, deviceData);
    if (Constants.BLE_CHANNEL === changleType) {
      if (!this.receivedBleDeviceIds.has(receiveDataDeviceId)) {
        this.receivedBleDeviceIds.add(receiveDataDeviceId);
      }
    } else {
      WakeUpLog.w(TAG, "changleType is error!");
    }
    deviceData.setScanTime(Date.now() - this.mStartCoordinatingStamp);
    this.receivedDeviceIds.add(receiveDataDeviceId);
    if (!this.isMsgIdHandleSuccess(deviceData, messageId, type)) {
      return;
    }

    if (this.canCoordinateAdvance() && this.isScanTimeout
      && (StateMachine.getState() !== StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE
        && StateMachine.getState() !== StateMachine.STATE_DEVICE_INFO_START_FAILED_PACKAGE)) {
      this.coordinateInAdvance();
    }
  }

  private isMsgIdHandleSuccess(deviceData: DeviceData, messageId: number, type: string): boolean {
    switch (messageId) { // 包的ID
      case Constants.MSG_ID_DEVICE_DETECTION: {
        // 解析一级包，传给短距模块做组网
        this.awareResultDetect(deviceData);
        break;
      }
      case Constants.MSG_ID_DEVICE_COORDINATION: {
        this.awareResultSuccess(deviceData, type); // 协同包或抑制包
        break;
      }
      case Constants.MSG_ID_DEVICE_FAILED: {
        this.awareResultFailed(deviceData);
        break;
      }
      default: {
        WakeUpLog.w(TAG, "handleAwareResult:: unknown message id.");
        return true;
      }
    }
    return true;
  }

  private async awareResultDetect(deviceData: DeviceData): Promise<void> {
    let deviceId = DataFormatter.byteArray2Int(deviceData.getDeviceId());
    let sequenceNumber = deviceData.getSequenceNumber();
    if (!this.isDeviceStatusOk()) {
      // 被工作压制过程中，压制的设备出现唤醒时，防止退出压制
      if (StateMachine.getNewState() === State.WORKED && this.mLastPressDeviceId === deviceId
        && sequenceNumber > this.mWorkPackageSeqNum) {
        WakeUpLog.i(TAG, "awareResultDetect::update mWorkPackageSeqNum.");
        this.doStartTimer(Constants.WORK_TIMEOUT_TIMER_VALUE, Constants.TIMER_TYPE_INHIBITED_TIMEOUT);
        this.mWorkPackageSeqNum = sequenceNumber;
      }
      return;
    }
    let originData = deviceData.getOriginData();
    if (originData == null || originData.length !== Constants.DATA_LEN_DEVICE_INFO) {
      WakeUpLog.e(TAG, "originData fail:" + originData);
      return;
    }
    if (!ArrayUtil.judgeDeviceInList(this.mDeviceDataListDetected, deviceData)) {
      this.mDeviceDataListDetected.add(deviceData);
    }
    ArrayUtil.removeDeviceFromList(this.notWakeUpDeviceList, deviceData);
    return;
  }

  private isDeviceStatusOk(): boolean {
    if (!this.isOnWakeup && !this.isFirstOnWakeup) {
      WakeUpLog.w(TAG, "isDeviceStatusOk:: device do not wakeup.");
      return false;
    }

    let state = StateMachine.getState();
    if (state === StateMachine.STATE_NA) {
      WakeUpLog.w(TAG, "isDeviceStatusOk::Coordination state invalid");
      return false;
    }
    if (state === StateMachine.STATE_DEVICE_INFO_START_FAILED_PACKAGE) {
      WakeUpLog.w(TAG, "isDeviceStatusOk::Coordination selfwakup failed!");
      return false;
    }
    return true;
  }

  private async awareResultSuccess(deviceData: DeviceData, type: string): Promise<void> {
    let isDeviceRespond = CoordinationUtil.isDeviceRespond(deviceData.getOriginData());
    if (isDeviceRespond && CommonUtil.isThirdVersionOk()) {
      this.awareResultInhibited(deviceData, type);
    }

    if (!this.isDeviceStatusOk()) {
      let deviceId = DataFormatter.byteArray2Int(deviceData.getDeviceId());
      let sequenceNumber = deviceData.getSequenceNumber();
      let responseNum = CoordinationUtil.getResponseSn(deviceData.getOriginData());

      // 被工作压制态，收到压制设备唤醒包处理，避免提前退出压制
      if (StateMachine.getNewState() === State.WORKED && this.mLastPressDeviceId === deviceId
        && sequenceNumber > this.mWorkPackageSeqNum) {
        WakeUpLog.i(TAG, "awareResultSuccess::update mWorkPackageSeqNum.");
        this.doStartTimer(Constants.WORK_TIMEOUT_TIMER_VALUE, Constants.TIMER_TYPE_INHIBITED_TIMEOUT);
        this.mWorkPackageSeqNum = sequenceNumber;
        this.mLastInhibitedNum = responseNum;
      }
      return;
    }
    //过滤
    ArrayUtil.removeDeviceFromList(this.notWakeUpDeviceList, deviceData);
    WakeUpLog.i(TAG, "deviceData add:" + deviceData.toString());
    if (!ArrayUtil.judgeDeviceInList(this.mDeviceDataList, deviceData)) {
      this.mDeviceDataList.add(deviceData);
    }
  }

  private awareResultInhibited(deviceData: DeviceData, type: string): void {
    if (Constants.BLE_CHANNEL !== type) {
      WakeUpLog.w(TAG, "awareResultInhibited:: It's not a BLE package, to be filtered.");
      return;
    }
    if (!this.isCoControlDevice(deviceData)) {
      WakeUpLog.w(TAG, "awareResultInhibited:: It should not be inhibited, to be filtered.");
      return;
    }
    if (StateMachine.getNewState() === State.INHIBITING) {
      WakeUpLog.w(TAG, "awareResultInhibited:: It is has responsed, to be filtered.");
      return;
    }
    let mDeviceId = DataFormatter.byteArray2Int(deviceData.getDeviceId());
    WakeUpLog.w(TAG, "awareResultInhibited:: response device id = " + mDeviceId);

    // 工作态收到抑制包切换到被抑制态，停止工作包
    if (StateMachine.getNewState() === State.WORKING) {
      this.doStopTimer(Constants.TIMER_TYPE_WORK_PACKAGE_RESTART);

    }

    StateMachine.setNewState(State.INHIBITED);

    // 切换抑制态，没有被压制过的设备需要压制音量
    if (!this.isInhibited) {
      this.onStateChangeCallback(Constants.PRESS_VOLUME_TO_VALUE, Constants.VOICE_CALLBACK_PRESS_STATE);
      this.isInhibited = true;
    }

    this.mLastInhibitedNum = deviceData.getSequenceNumber() & this.PARSE_SN_FROM_TIMESTAMP;
    WakeUpLog.i(TAG, "awareResultInhibited:: lastInhibitedNum = " + this.mLastInhibitedNum);

    // 连续压制，或者双响时，以最后一次压制本设备为准
    this.mLastPressDeviceId = mDeviceId;
    this.doStartTimer(this.MAX_INHIBITED_VALUE, Constants.TIMER_TYPE_INHIBITED_TIMEOUT);

    WakeUpLog.i(TAG,
      "awareResultInhibited:: this device to be inhibited," + " mLastPressDeviceId = " + this.mLastPressDeviceId);
  }

  /**
   * 判断发送包的设备是否为协同收音设备，只有是协同收音设备才能去压制或解压其它设备的音量及退其它设备的语音助手
   *
   * @param deviceData 设备数据
   * @return true：是协同收音设备
   */
  private isCoControlDevice(deviceData: DeviceData): boolean {
    let deviceId = DataFormatter.byteArray2Int(deviceData.getDeviceId());
    return CommonUtil.isPublicDevice(deviceData.getDeviceType());
  }


  private coordinateInAdvance(): void {
    WakeUpLog.i(TAG, "doCoordination preOnReceive " + (this.mDeviceDataList.length - 1) + ";StateMachine = "
      + StateMachine.getState());

    // 只有自身二级唤醒了，Receive才能提前做协同
    if (!this.isOnWakeup) {
      WakeUpLog.w(TAG, "coordinateInAdvance:: isOnWakeup is false.");
      return;
    }

    // 如果设备列表为空。
    if (this.mDeviceDataList.isEmpty()) {
      WakeUpLog.i(TAG, "coordinateInAdvance:: onResult called to respond by mDeviceDataList.isEmpty() = true.");
      this.onResultDefault();
      StateMachine.setState(StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE);
      return;
    }
    this.handleDeviceInfoList();
    StateMachine.setState(StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE);
  }


  private handleDeviceEnterNetwork(data: DeviceData, isShouldResponse: boolean): void {
    WakeUpLog.d(TAG, "handleDeviceEnterNetwork,shouldResponse = " + isShouldResponse);
    if (data === null || this.wakeupWord !== data.getWakeupWord()) {
      return;
    }

    if (isShouldResponse) {
      let msg: string = "deviceType = " + this.mDeviceType + ",wakeupWord = " + this.wakeupWord;
      WakeUpLog.d(TAG, msg);

      // 根据本机信息，生成闲时感知响应包,id是5
      let deviceInfo: Uint8Array = CoordinationUtil.buildBytes(Constants.MSG_ID_DEVICE_ENTER_NETWORK_RESPONSE,
        this.mDeviceType, this.wakeupWord);

    }
  }

  /**
   * 响应唤醒协同结果
   *
   * @param isShouldResponse 当前设备是否需要响应
   * @param isCoordinator 当前设备是否参与协同
   * @param responseDeviceName 主响设备类型
   */
  public onResult(isShouldResponse: boolean, isCoordinator: boolean, responseDeviceName: string): void {
    //TODO 加锁 synchronized (resultLock) {
    //为获取到本机设备信息
    if (this.localDeviceData === undefined || this.mResultListener === undefined) {
      return;
    }
    if (!this.hasSentResult) { // 如果还没有发送过协同结果

      let otherDeviceCount: number = this.receivedBleDeviceIds.length;
      let deviceIdList: List<number> = CoordinationUtil.getDeviceIdList(this.mDeviceDataList, this.localDeviceData);
      let coordinatorResult: CoordinatorResult = new CoordinatorResult(isShouldResponse, isCoordinator,
        otherDeviceCount, deviceIdList, this.wakeUpRandomId);
      coordinatorResult.setResponseDeviceName(responseDeviceName);
      //设置手机打点数据
      this.mResultListener.onResult(coordinatorResult);
      this.hasSentResult = true;
      //当前设备响应
      if (isShouldResponse) {
        StateMachine.setNewState(State.INHIBITING);
        StateMachine.setVoiceAssistantState(Constants.VOICE_ASSISTANT_WORKING);
        this.notifyOthersSilent();
      } else {
        StateMachine.setVoiceAssistantState(Constants.DEFAULT_VALUE);
      }
      if (isShouldResponse && this.isInhibited) {
        this.onStateChangeCallback(Constants.PRESS_VOLUME_TO_VALUE, Constants.VOICE_CALLBACK_RELEASE_STATE);
        this.isInhibited = false;
        this.mWorkPackageSeqNum = Constants.DEFAULT_VALUE;
      }

      // 获取协同唤醒结束时间戳
      let endStamp: number = Date.now();
      // 协同打点
      this.putCoordinatorData(ReportUtil.COOR_END_TIME, endStamp.toString());
      this.putCoordinatorData(ReportUtil.COOR_RESULT, isShouldResponse ? CoordinatorListenerImp.REPORT_RESPONSE : CoordinatorListenerImp.REPORT_NOT_RESPONSE);
      this.recordReportData(ReportUtil.COOR_TOTAL_TIME, endStamp - this.mTimestampToStartDetected);
      if (!isCoordinator) {
        this.recordReportData(ReportUtil.COOR_RESULT, 2);
      } else {
        this.recordReportData(ReportUtil.COOR_RESULT, isShouldResponse ? 1 : 0);
      }
      this.recordReportData(ReportUtil.COOR_IS_SUPPRESSION, StateMachine.getNewState() === State.INHIBITED ? 1 : 0);
      WakeUpLog.i(TAG,
        "onResult:: isShouldResponse = " + isShouldResponse + ", isCoordinator = " + isCoordinator + ","
          + "Other device count = " + otherDeviceCount + ", cost time = " + (endStamp - this.mStartCoordinatingStamp) + ", responseDeviceName = " + responseDeviceName);
    } else {
      WakeUpLog.i(TAG, "onResult:: has done response by other method.");
    }
  }

  /**
   * 本地设备已经响应，改变广播的内容通知其他的设备不要响应
   */
  private async notifyOthersSilent(): Promise<void> {
    WakeUpLog.i(TAG, "notifyOthersSilent() now to notify others silent.");
    if (this.localDeviceData === null) {
      WakeUpLog.w(TAG, "notifyOthersSilent() local data is null.");
      return;
    }
    let localWakeupBytes: Uint8Array = this.localDeviceData.getOriginData();
    WakeUpLog.i(TAG, "localWakeupBytes =" + JSON.stringify(localWakeupBytes));
    if (localWakeupBytes === null || localWakeupBytes.length !== Constants.DATA_LEN_DEVICE_INFO) {
      WakeUpLog.w(TAG, "notifyOthersSilent() local bytes data is invalid.");
      return;
    }
    let wakeupSuccessBytes = localWakeupBytes;
    WakeUpLog.i(TAG, "wakeupSuccessBytes =" + JSON.stringify(wakeupSuccessBytes));
    this.mSequenceNumber = this.localDeviceData.getSequenceNumber();
    WakeUpLog.i(TAG, "mSequenceNumber = " + this.mSequenceNumber);

    // 应答包将高优先级设置到最大
    wakeupSuccessBytes[this.highPriorIndex] = (wakeupSuccessBytes[this.highPriorIndex] | this.HIGHT_PRIORITY);
    WakeUpLog.i(TAG, "wakeupSuccessBytes =" + JSON.stringify(wakeupSuccessBytes));
    ReportUtil.holdSendPackage(wakeupSuccessBytes, Constants.MSG_ID_DEVICE_SUCCESS);
    this.changeNearbyTimeGap = Date.now() - this.lastChangeNearbyTime;
    if (this.changeNearbyTimeGap <= 0) {
      WakeUpLog.w(TAG, "changeNearbyTimeGap is invalid.");
    } else if (this.changeNearbyTimeGap >= this.NEARBY_CHANGE_TIME_GAP) {
      let encryptData = await CoordinationSecurity.getEncryptData(wakeupSuccessBytes);

      // 发送广播
      this.bleProxyImp.startAdvertising(encryptData);
    } else {
      WakeUpLog.i(TAG, "start timer to change nearby content");
      this.doStartTimer(this.NEARBY_CHANGE_TIME_GAP - this.changeNearbyTimeGap,
        Constants.TIMER_TYPE_CHANGE_NEARBY_CONTENT);
    }
  }
  
  /**
   * 设置二级唤醒设备是否已经成功唤醒
   *
   * @param isOnWakeup 二级唤醒标志
   * @param isSecondWakeupResult 二级唤醒是成功还是失败
   */
  public setOnWakeup(isOnWakeup: boolean, isSecondWakeupResult: boolean): void {
    this.isOnWakeup = isOnWakeup;
    this.isSecondWakeupSuccess = isSecondWakeupResult;
  }

  private onStateChangeCallback(volume: number, state: string): void {
    if (this.mStateChangeCallback == null || !this.isCoordinationSwitchOn) {
      WakeUpLog.e(TAG,
        "callback is null? " + (this.mStateChangeCallback == null) + " switch on? " + this.isCoordinationSwitchOn);
      return;
    }
    this.mStateChangeCallback.onStateChange(volume, state);
  }

  /**
   * 检查nearby是否可用
   *
   * @return 返回检查结果
   */
  public isNearbyOk(): boolean {
    WakeUpLog.i(TAG, "isNearbyInitialed:" + this.isNearbyInitialedState);
    if (!this.isNearbyInitialedState || this.isNearbyRuntimeError) {
      WakeUpLog.i(TAG, "nearBy is not initialed or runtime error.");
      return false;
    }
    return true;
  }

  /**
   * 判断{@link this#mNearbyProxy}是否为null。
   *
   * @return 如果是没有初始化，返回false，否则返回true。仅供一级唤醒调用。
   */
  public isNearbyInitialed(): boolean {
    WakeUpLog.i(TAG, "isNearbyInitialed:" + this.isNearbyInitialedState);
    if (!this.isNearbyInitialed) {
      WakeUpLog.i(TAG, "nearBy is still not initialed!");
      this.bleProxyImp.setCoodinatorFlag(true);
      return true;
    }
    return false;
  }


  /**
   * 是否协同唤醒蓝牙扫描定时是否结束
   *
   * @return 返回蓝牙扫描定时是否结束标志
   */
  public getIsScanLongTimeout(): boolean {
    return this.mIsScanLongTimeout;
  }

  /**
   * 判断系统是否被root接口
   *
   * @return 返回0，表示未被root；否则，表示已root  int
   */
  public getIsOsRooted(): number {
    throw new Error('Method not implemented.');
  }

  /**
   * 设备入网
   */
  public onDeviceEnterNetwork(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 设置是否一级唤醒
   *
   * @param isOnFirstOnWakeup 是否一级唤醒
   */
  public setFirstOnWakeup(isOnFirstOnWakeup: boolean): void {
    this.isFirstOnWakeup = isOnFirstOnWakeup;
  }

  /**
   * 是否一级唤醒
   *
   * @return 是否一级唤醒
   */
  public getFirstOnWakeup(): boolean {
    return this.isFirstOnWakeup;
  }

  /**
   * 启动蓝牙10%的低功耗扫描
   */
  public startLowPowerScan(): void {
    throw new Error('Method not implemented.');
  }

  /**
   * 初始化协同响应实例的状态标志位
   */
  public initCoordinationStatus(): void {
    this.isScanTimeout = false;
    this.isLastTimeOut = false;
    this.isShouldWakeupWhenWakeup = false;
    this.isOnWakeup = false;
    this.hasSentResult = false;
    this.isScanLongTimeout = false;
    this.lastChangeNearbyTime = Date.now();
    this.wakeUpRandomId = CommonUtil.getSecureRandomNumber();
    ReportUtil.setReportintWakeUpRandomId(this.wakeUpRandomId);
    WakeUpLog.d(TAG, "initCoordinationStatus end");
  }

  /**
   * 记录打点数据
   *
   * @param key 打点字段名称
   * @param value 打点的内容
   */
  public putCoordinatorData(key: string, value: string): void {
    this.resultMap.set(key, value);
  }

  public setCoordinationSwitch(isSwitchOn: boolean): void {
    this.isCoordinationSwitchOn = isSwitchOn;
  }

  /**
   * 设置唤醒词
   *
   * @param wakeupWord 唤醒词
   */
  public setWakeupWord(wakeupWord: number): void {
    this.wakeupWord = wakeupWord;
  }

  /**
   * 设置设备名称
   *
   * @param deviceName 设备名称
   */
  public setDeviceName(deviceName: string): void {
    this.deviceName = deviceName;
  }

  /**
   * 设置设备类型
   *
   * @param deviceType 设备类型
   */
  public setDeviceType(deviceType: number): void {
    this.mDeviceType = deviceType;
    if (this.bleProxyImp == null) {
      return;
    }
    this.bleProxyImp.setDeviceType(deviceType);
  }

  /**
   * 获取设备类型
   *
   * @returns 设备类型
   */
  public getDeviceType(): number {
    return this.mDeviceType;
  }

  /**
   * 退出语音助手
   */
  public onVoiceAssistantExit(): void {
    let state = StateMachine.getVoiceAssistantState();
    WakeUpLog.i(TAG, "exitVoiceAssistant::StateMachine.getVoiceAssistantState() = " + state);
    if (state !== Constants.DEFAULT_VALUE) {
      if (this.isFirstOnWakeup || this.isOnWakeup) {
        WakeUpLog.d(TAG, "exitVoiceAssistant::send stopPackage after coordination");
        StateMachine.setVoiceAssistantState(Constants.VOICE_ASSISTANT_EXITED);
      } else {
        WakeUpLog.d(TAG, "exitVoiceAssistant::send stopPackage now");
        StateMachine.setVoiceAssistantState(Constants.DEFAULT_VALUE);
        StateMachine.setNewState(State.STANDBY);
        this.sendStopPackage();
      }
    }
  }

  /**
   * 1300ms超时，最晚协同决策时间点
   */
  private coordinateLatestTime(): void {
    WakeUpLog.i(TAG, "coordinateLatestTime: Coordination package count = " + (this.mDeviceDataList.length));
    this.isLastTimeOut = true;

    // 只有自身二级唤醒了，才能做协同;如果不是提前决策了，超时进行决策
    if (!this.isOnWakeup) {
      this.isShouldWakeupWhenWakeup = this.mDeviceDataList.isEmpty();
      WakeUpLog.i(TAG, "coordinateLatestTime:: selfWakeup delayed." +
        " isShouldWakeupWhenWakeup = " + this.isShouldWakeupWhenWakeup);
    } else if (StateMachine.getState() !== StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE
      && StateMachine.getState() !== StateMachine.STATE_DEVICE_INFO_START_FAILED_PACKAGE) {
      WakeUpLog.i(TAG, "coordinateLatestTime:: timeout! StateMachine = " + StateMachine.getState());

      // 如果设备列表为空,则直接响应，在二级唤醒超时1s的极其异常的情况下发生。
      if (this.mDeviceDataList.isEmpty()) {
        WakeUpLog.i(TAG, "coordinateLatestTime:: onResult called by mDeviceDataList.isEmpty() = true");
        this.onResultDefault();
      } else {
        this.handleDeviceInfoList();
      }
    } else {
      WakeUpLog.i(TAG, "coordinateLatestTime:: coordination in advance or wakeup fail.");
    }
  }

  private changeDeviceStateAtSecondTimeOut(): void {
    if (!CommonUtil.isThirdVersionOk()) {
      return;
    }

    if (!CommonUtil.isPublicDevice(this.mDeviceType)) {
      WakeUpLog.d(TAG, "It's a private device, state -> STANDBY！");
      StateMachine.setNewState(State.STANDBY);
      return;
    }

    // 状态机变换
    if (StateMachine.getVoiceAssistantState() === Constants.VOICE_ASSISTANT_WORKING) { // 响应设备
      WakeUpLog.i(TAG, "changeDeviceStateAtSecondTimeOut:: VOICE_ASSISTANT_WORKING");

      // 发工作包
      StateMachine.setNewState(State.WORKING);
      this.doStartTimer(Constants.WORK_PACKAGE_RESTART_TIMER_VALUE - Constants.WORK_PACKAGE_TIMER_VALUE,
        Constants.TIMER_TYPE_WORK_PACKAGE_RESTART);

    } else if (StateMachine.getVoiceAssistantState() === Constants.VOICE_ASSISTANT_EXITED) { // 响应设备
      WakeUpLog.i(TAG, "changeDeviceStateAtSecondTimeOut:: VOICE_ASSISTANT_EXITED");

      // 语音助手已经退出， 直接切换到休眠态，并停止发送抑制包
      StateMachine.setNewState(State.STANDBY);
      StateMachine.setVoiceAssistantState(Constants.DEFAULT_VALUE);
      this.sendStopPackage();
    } else {
      WakeUpLog.i(TAG, "changeDeviceStateAtSecondTimeOut::this device not response. state = "
        + StateMachine.getNewState());

      // 语音助手在被被压制过程中，出现误唤醒,结束后需切换回被工作压制态
      if (StateMachine.getNewState() === State.STANDBY && this.isInhibited) {
        StateMachine.setNewState(State.WORKED);
      }
    }
  }

  private async sendStopPackage(): Promise<void> {
    WakeUpLog.i(TAG, "sendStopPackage.");
    this.doStopTimer(Constants.TIMER_TYPE_WORK_PACKAGE_RESTART);
    let stopBytes =
      CoordinationUtil.buildTaskBytes(Constants.MSG_ID_DEVICE_STOP, this.mDeviceType, this.wakeupWord, this.mSequenceNumber);
    WakeUpLog.i(TAG, "sendStopPackage:: start sendMsg" + stopBytes);

    // 加密报文
    let encryptData = await CoordinationSecurity.getEncryptData(stopBytes);
    if (!CommonUtil.getIsSecondVersionOk() && this.bleProxyImp != null) {
      this.bleProxyImp.startAdvertising(encryptData);
    } else {
      WakeUpLog.w(TAG, "sendStopPackage::sending workBytes failed");
    }

    // 3.0启动停止包定时器
    this.doStartTimer(Constants.STOP_PACKAGE_TIMER_VALUE, Constants.TIMER_TYPE_STOP_SEND_PACKAGE);
  }

  private awareResultFailed(deviceData: DeviceData): void {
    if (!this.isDeviceStatusOk()) {
      let deviceId = DataFormatter.byteArray2Int(deviceData.getDeviceId());
      let sequenceNumber = deviceData.getSequenceNumber();

      // 被工作压制态收到失败包处理，避免提前退出压制
      if (StateMachine.getNewState() === State.WORKED && this.mLastPressDeviceId === deviceId
        && sequenceNumber > this.mWorkPackageSeqNum) {
        WakeUpLog.i(TAG, "awareResultFailed::update mWorkPackageSeqNum.");
        this.doStartTimer(Constants.WORK_TIMEOUT_TIMER_VALUE, Constants.TIMER_TYPE_INHIBITED_TIMEOUT);
        this.mWorkPackageSeqNum = sequenceNumber;
      }
      return;
    }
    ArrayUtil.removeDeviceFromList(this.notWakeUpDeviceList, deviceData);
    ArrayUtil.removeDeviceFromList(this.mDeviceDataListDetected, deviceData);
  }

  public getIsLatestCoordinationTimerStarted(): boolean {
    return this.isLatestCoordinationTimerStarted;
  }

  public setLatestCoordinationTimerStarted(isStarted: boolean): void {
    this.isLatestCoordinationTimerStarted = isStarted;
  }

  /**
   * 是否二级唤醒
   *
   * @return 是否二级唤醒
   */
  public getIsOnWakeup(): boolean {
    return this.isOnWakeup;
  }
}