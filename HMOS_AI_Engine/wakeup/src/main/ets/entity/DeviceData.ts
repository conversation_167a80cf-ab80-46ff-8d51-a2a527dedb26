/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */
import { Constants } from '../utils/Constants';
import WakeUpLog from '../utils/WakeUpLog';
import { BasicDeviceParameter } from './BasicDeviceParameter';
import { SceneInfo } from './SceneInfo';

const TAG = "DeviceData";

const INVALID_VERSION: number = -1;

/**
 * 设备状态实体
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-28
 */
export class DeviceData {
  /**
   * 消息类型
   */
  private mMessageId: number = 0;
  /**
   * 设备唯一识别号
   */
  private mDeviceId: Uint8Array;
  /**
   * {@link SceneInfo}实例
   */
  private mSceneInfo: SceneInfo;
  /**
   * 原始字节数组
   */
  private mOriginData: Uint8Array;
  /**
   * 序列号，用于防重放。
   */
  private mSequenceNumber: number = 0;
  /**
   * 扫描时间
   */
  private mScanTime: number = 0;
  private channelType: string = '';
  private deviceType: number = 0;
  private wakeupWord: number = 0;
  private ips: Uint8Array = new Uint8Array(0);

  /**
   * 设备信息构造函数，把原始字节数组缓存，在协同的时候根据不同规则版本号按照不同方式解析
   *
   * @param messageId 消息类型
   * @param deviceId 设备唯一标识
   * @param originData 把原始字节数组
   * @param sequenceNumber 广播设备信息序列
   * @param receiveDeviceType 接收到设备的设备类型
   */
  constructor(mMessageId?: number, mDeviceId?: Uint8Array, mOriginData?: Uint8Array, mSequenceNumber?: number, deviceType?: number) {
    if (mMessageId === undefined) {
      this.mDeviceId = new Uint8Array;
      this.mOriginData = new Uint8Array;
      this.mSceneInfo = new SceneInfo(new BasicDeviceParameter());
    } else {
      this.mMessageId = mMessageId;
      this.mDeviceId = mDeviceId == null ? new Uint8Array(0) : mDeviceId;
      this.mOriginData = mOriginData == null ? new Uint8Array(0): mOriginData;
      this.mSequenceNumber = mSequenceNumber == null ? 0 : mSequenceNumber;
      this.deviceType = deviceType == null ? 0 : deviceType;
      this.mSceneInfo = new SceneInfo(new BasicDeviceParameter());
    }
  }

  /**
   * 获取设备唯一标识
   *
   * @return 设备唯一标识
   */
  public getDeviceId(): Uint8Array {
    return this.mDeviceId;
  }

  /**
   * 设置设备唯一识别号
   *
   * @param mDeviceId 设备唯一识别号
   */
  public setDeviceId(mDeviceId: Uint8Array): void {
    this.mDeviceId = mDeviceId;
  }

  /**
   * 获取设备类型
   *
   * @return 设备类型
   */
  public getDeviceType(): number {
    return this.deviceType;
  }

  /**
   * 设置设备类型
   *
   * @param deviceType 设备类型
   */
  public setDeviceType(deviceType: number): void {
    this.deviceType = deviceType;
  }

  /**
   * 获取设备唯一标识
   *
   * @return 设备消息类型
   */
  public getMessageId(): number {
    return this.mMessageId;
  }

  /**
   * 设置消息id
   *
   * @param mMessageId 消息id
   */
  public setMessageId(mMessageId: number): void {
    this.mMessageId = mMessageId;
  }

  /**
   * 设置场景数据
   *
   * @param sceneInfo 待设置的数据
   */
  public setSceneInfo(sceneInfo: SceneInfo): void {
    if (sceneInfo == null) {
      return;
    }
    this.mSceneInfo = sceneInfo;
  }

  /**
   * 获取智能场景值
   *
   * @return 智能场景值
   */
  public getSceneInfo(): SceneInfo {
    return this.mSceneInfo;
  }

  /**
   * 获取顺序号做防重放判断
   *
   * @return 顺序号
   */
  public getSequenceNumber(): number {
    return this.mSequenceNumber;
  }

  /**
   * 设置序列号
   *
   * @param mSequenceNumber 序列号
   */
  public setSequenceNumber(mSequenceNumber: number): void {
    this.mSequenceNumber = mSequenceNumber;
  }

  /**
   * 获取扫描时间
   *
   * @return 返回扫描时间
   */
  public getScanTime(): number {
    return this.mScanTime;
  }

  /**
   * 设置扫描时间
   *
   * @param scanTime 待设置扫描时间
   */
  public setScanTime(scanTime: number): void {
    this.mScanTime = scanTime;
  }

  /**
   * 设置收到这个设备的通信通道
   *
   * @param channelType 通信通道
   */
  public setChannelType(channelType: string): void {
    this.channelType = channelType;
  }

  /**
   * 读取通信通道
   *
   * @return 得到收到这个设备的通信通道类型
   */
  public getChannelType(): string {
    return this.channelType;
  }

  /**
   * 获取原始数据
   *
   * @return 返回原始数据数组
   */
  public getOriginData(): Uint8Array {
    return this.mOriginData;
  }

  /**
   * 设置原始字节数值
   *
   * @param mOriginData 原始字节数值
   */
  public setOriginData(mOriginData: Uint8Array): void {
    this.mOriginData = mOriginData;
  }

  /**
   * 获取唤醒词
   *
   * @return 唤醒词
   */
  public getWakeupWord(): number {
    return this.wakeupWord;
  }

  /**
   * 设置唤醒词
   *
   * @param wakeupWord 唤醒词
   */
  public setWakeupWord(wakeupWord: number): void {
    this.wakeupWord = wakeupWord;
  }

  /**
   * 获取ip地址
   *
   * @return ip地址
   */
  public getIps(): Uint8Array {
    return this.ips;
  }

  /**
   * 设置ip地址
   *
   * @param ips ip地址
   */
  public setIps(ips: Uint8Array): void {
    this.ips = ips;
  }

  public toString(): string {
    return "DeviceData{" + "mMessageId=" + this.mMessageId + ", mDeviceId=" + this.mDeviceId
      + ", mOriginData=" + this.mOriginData
      + ", HashCode=" + this.mDeviceId + ", mSceneInfo=" + this.mSceneInfo + ", mSequenceNumber="
      + this.mSequenceNumber + '}';
  }

  /**
   * 获取协同唤醒规则版本号（其实不应该放在这里，DeviceData，BasicDeviceParameter，SceneInfo待重构）
   *
   * @return 返回解析的版本号结果,如果原始数据数组为空返回无效值-1.
   */
  public getWakeUpRuleVersion(): number {
    if (this.mOriginData == null || this.mOriginData.length < Constants.DATA_LEN_DEVICE_INFO) {
      return INVALID_VERSION;
    }
    let ruleVersionIndex = 14;
    let versionOffset = 4;
    let result = (this.mOriginData[ruleVersionIndex] & Constants.HIGH_FOUR_BITS_MASK) >> versionOffset
    WakeUpLog.i(TAG, "getWakeUpRuleVersion:" + result);
    return result;
  }

  /**
   * 获取是否处于逃生通道抑制状态
   *
   * @return 1:抑制，0:未抑制
   */
  public getIsNegative(): number {
    if (this.mOriginData == null || this.mOriginData.length < Constants.DATA_LEN_DEVICE_INFO) {
      return 0;
    }
    let index = 12;
    return (this.mOriginData[index] & Constants.LOW_ONE_MASK) === 0 ? 1 : 0;
  }
}