/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import { Constants } from '../utils/Constants';

import WakeUpLog from '../utils/WakeUpLog';
import { BasicDeviceParameter } from './BasicDeviceParameter';
import { InnerDeviceParameter } from './InnerDeviceParameter';

const TAG:string = 'SceneInfo';

const MAX_VOICE_ENERGY = 127;

/**
 * 场景数据获取类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-02
 */
export class SceneInfo {
    /**
     * 语音助手是否在前台
     */
    private vaForegroundNow:number = 0;

    /**
     * 语音助手上次退出时间戳距现在的秒数
     */
    private mVaLastUseFromNow:number = 0;

    /**
     * 设备类型
     */
    private mDeviceType:number = 0;

    /**
     * 语音声强等级
     */
    private voiceEnergyLevel:number = 0;

    /**
     * 声强绝对值
     */
    private voiceAbsoluteEnergy:number = 0;

    /**
     * 空间谱
     */
    private spatialSpectrum:number = 0;

    /**
     * 声纹置信度
     */
    private voiceConfidence:number = 0;

    /**
     * 是否手动点亮屏幕(5s内)
     */
    private lightScreenManually:number = 0;

    /**
     * 高优先级设备状态预留位
     */
    private mHighPriority:number = 0;

    /**
     * 普通优先级设备状态预留位
     */
    private mNormalPriority:number = 0;

    /**
     * 负优先级设备状态预留位
     */
    private mNegativePriority:number = 0;

    /**
     * 大屏、音箱是否服务中
     */
    private inService:number = 0;

    /**
     * 降低优先级
     */
    private lowerPriority:number = 0;

    /**
     * 提高普通优先级
     */
    private switchTimeStamp:number = 0;

    /**
     * 用户主动降低优先级打点时间
     */
    private lowerPriorityRecord:number = 0;

    /**
     * 预留位
     */
    private reservedColumn:number = 0;

    /**
     * 是不是骨声纹耳机
     */
    private isBoneVoicePrintEarphone:boolean = false;

    /**
     * 是否在收音状态:1收音状态,0非收音状态
     */
    private inRadio:number = 0;

    private deviceName:string = '';

    public constructor(param:BasicDeviceParameter, isInnerCall?:boolean) {
        if (isInnerCall === undefined) {
            this.init(param, false);
        } else {
            this.init(param, isInnerCall);
        }
    }

    private init(param:BasicDeviceParameter, isInnerCall: boolean) : void {
        if (param === null || param === undefined) {
            return;
        }
        this.voiceEnergyLevel = param.getVoiceEnergyLevel();
        if (param.getBoneVoicePrintEarphone()) {
            this.voiceAbsoluteEnergy = MAX_VOICE_ENERGY;
        } else {
            this.voiceAbsoluteEnergy = param.getVoiceAbsoluteEnergy();
            this.spatialSpectrum = param.getSpatialSpectrum();
        }
        this.voiceConfidence = param.getVoiceConfidence();
        this.mDeviceType = param.getDeviceType();
        this.isBoneVoicePrintEarphone = param.getBoneVoicePrintEarphone();

        if (isInnerCall) {
            // 协同唤醒内部调用
            this.mVaLastUseFromNow = param.getLastUseTimeFromNow();
            if (param instanceof InnerDeviceParameter) {
                let innerDeviceParameter = param;
                this.mHighPriority = innerDeviceParameter.getHighPriority();
                this.mNormalPriority = innerDeviceParameter.getNormalPriority();
                this.mNegativePriority = innerDeviceParameter.getNegativePriority();
                this.reservedColumn = innerDeviceParameter.getReservedColumn();
            }
        } else {
            // 外部接口调用
            let oneSecondMs = 1000;
            this.vaForegroundNow = param.getForeground();
            this.lightScreenManually = param.getLightScreenManually();
            this.inService = param.getInService();
            let timeFromNow = Date.now() - param.getLastUseTimeFromNow();
            timeFromNow = timeFromNow / oneSecondMs;
            this.mVaLastUseFromNow = timeFromNow > Number.MAX_VALUE ? Number.MAX_VALUE : timeFromNow;
            this.lowerPriorityRecord = Date.now() - param.getLowerPriority();

            // 设备的唤醒检测时延最小值、最大值
            //this.setWakeupDelay(param.getMinWakeupDelay(), param.getMaxWakeupDelay(), param.getDeviceType());

            // 用户主动降低优先级
            WakeUpLog.i(TAG, "lowerPriority :: " + this.lowerPriorityRecord);
            WakeUpLog.i(TAG, "minWakeupDelay :: " + param.getMinWakeupDelay());
            WakeUpLog.i(TAG, "maxWakeupDelay :: " + param.getMaxWakeupDelay());
            let isLowerPriority =
                (Date.now() - param.getLowerPriority()) < Constants.LOWER_PRIORITY_TIME;
            this.lowerPriority = isLowerPriority ? 1 : 0;
            this.mNegativePriority = isLowerPriority ? 0 : 1;
            this.switchTimeStamp = param.getDialogSwitchTimeStamp();
            this.inRadio = param.getInRadio();
            this.deviceName = param.getDeviceName();
        }
    }

    /**
     * 获取唤醒时语音声强等级
     *
     * @return 音频能量值
     */
    public getVoiceEnergyLevel():number {
        return this.voiceEnergyLevel;
    }

    /**
     * 获取声强的绝对值，int整数
     *
     * @return 声强的绝对值
     */
    public getVoiceAbsoluteEnergy():number {
        return this.voiceAbsoluteEnergy;
    }

    /**
     * 根据读取到的声强增益值改变声强值
     *
     * @param voiceEnergy 声强增益值
     */
    public setVoiceAbsoluteEnergy(voiceEnergy:number): void {
    this.voiceAbsoluteEnergy = voiceEnergy;
    }

    /**
     * 获取空间谱，int整数
     *
     * @return 空间谱的值
     */
    public getSpatialSpectrum():number {
        return this.spatialSpectrum;
    }

    /**
     * 设置空间谱
     *
     * @param spatialSpectrum 空间谱的值
     */
    public setSpatialSpectrum(spatialSpectrum:number): void {
        this.spatialSpectrum = spatialSpectrum;
    }

    /**
     * 获取声纹置信度，int整数
     *
     * @return 声纹置信度
     */
    public getVoiceConfidence():number {
        return this.voiceConfidence;
    }

    /**
     * 获取设备类型
     *
     * @return 设备类型
     *
     */
    public getDeviceType():number {
        return this.mDeviceType;
    }

    /**
     * 设置设备类型
     *
     * @param deviceType 设备类型
     */
    public setDeviceType(deviceType:number): void {
        this.mDeviceType = deviceType;
    }

    /**
     * 语音助手是否在前台
     *
     * @return 如果在前台返回1，否则返回0。
     */
    public isVaForegroundNow():number {
        return this.vaForegroundNow;
    }

    /**
     * 是否是手动点亮屏幕
     *
     * @return 是则返回1，否则返回0。
     */
    public isLightScreenManually():number {
        return this.lightScreenManually;
    }

    public getInService():number {
        return this.inService;
    }

    /**
     * 获取语音助手上次使用时间
     *
     * @return 返回对应时间
     */
    public getVaLastUseFromNow():number {
        return this.mVaLastUseFromNow;
    }

    /**
     * 设置语音助手上次使用时间
     *
     * @param vaLastUseFromNow 待设置时间
     */
    public setVaLastUseFromNow(vaLastUseFromNow:number): void {
        this.mVaLastUseFromNow = vaLastUseFromNow;
    }

    /**
     * 获取普通优先级
     *
     * @return 返回普通优先级分值
     */
    public getNormalPriority():number {
        return this.mNormalPriority;
    }

    /**
     * 设置普通优先级分值
     *
     * @param normalPriority 待设置普通优先级
     */
    public setNormalPriority(normalPriority:number): void {
        this.mNormalPriority = normalPriority;
    }

    /**
     * 获取高优先级分值
     *
     * @return 返回对应优先级分值
     */
    public getHighPriority():number {
        return this.mHighPriority;
    }

    /**
     * 获取负优先级状态
     *
     * @return 返回负优先级状态值
     */
    public getNegativePriority():number {
        return this.mNegativePriority;
    }

    /**
     * 获取语音助手传入的上次逃生时间
     *
     * @return 返回语音助手逃生时间戳
     */
    public getLowerPriority():number {
        return this.lowerPriority;
    }

    /**
     * 获取是不是骨声纹耳机
     *
     * @return 骨声纹耳机的标记为
     */
    public getBoneVoicePrintEarphone():boolean {
        return this.isBoneVoicePrintEarphone;
    }

    public getLowerPriorityRecord():number {
        return this.lowerPriorityRecord;
    }

    /**
     * 获取语音助手传入的设备切换应答时间戳
     *
     * @return 设备切换应答时间戳
     */
    public getSwitchTimeStamp():number {
        return this.switchTimeStamp;
    }

    public getReservedColumn():number {
        return this.reservedColumn;
    }

    public setReservedColumn(reservedColumn:number): void {
        this.reservedColumn = reservedColumn;
    }

    public getInRadio():number {
        return this.inRadio;
    }

    public setInRadio(inRadio:number): void {
        this.inRadio = inRadio;
    }

    public getDeviceName():string {
        return this.deviceName;
    }

    public setDeviceName(deviceName:string): void {
        this.deviceName = deviceName;
    }

    public toString(): string {
        return "SceneInfo{" + "mVALastUseFromNow=" + this.mVaLastUseFromNow + ", isVAForegroundNow=" + this.vaForegroundNow
            + ", mDeviceType=" + this.mDeviceType + ", voiceEnergyLevel=" + this.voiceEnergyLevel + ", voiceAbsoluteEnergy="
            + this.voiceAbsoluteEnergy + ", voiceConfidence=" + this.voiceConfidence + ",isLightScreenManually="
            + this.lightScreenManually + ", mHighPriority=" + this.mHighPriority + ", mNormalPriority=" + this.mNormalPriority
            + ", mNegativePriority=" + this.mNegativePriority + ", lowerPriority=" + this.lowerPriority + ", inService="
            + this.inService + '}';
    }
}