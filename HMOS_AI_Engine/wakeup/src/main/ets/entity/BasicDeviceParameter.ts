/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import List from '@ohos.util.List';

const TAG:string = "BasicDeviceParameter";

/**
 * 场景数据获取类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-09
 */
export class BasicDeviceParameter {

    /**
     * 声强等级
     */
    private voiceEnergyLevel:number = 0;

    /**
     * 声强绝对值
     */
    private voiceAbsoluteEnergy:number = 0;

    /**
     * 空间谱
     */
    private spatialSpectrum:number = 0;

    /**
     * 声纹置信度
     */
    private voiceConfidence:number = 0;

    /**
     * 上次使用语音助手的时间毫秒值
     */
    private lastUseTimeFromNow:number = 0;

    /**
     * 设备类型
     */
    private deviceType:number = 0;

    /**
     * 语音助手是否在前台(1:在前台 0:不在前台)
     */
    private foreground:number = 0;

    /**
     * 5s内是否主动亮屏
     */
    private lightScreenManually:number = 0;

    /**
     * 公共设备是否使用中
     */
    private inService:number = 0;

    /**
     * 是否在收音状态:1收音状态,0非收音状态
     */
    private inRadio:number = 0;

    /**
     * 设备名称
     */
    private deviceName:string = '';

    /**
     * 用户主动降低优先级时间戳
     */
    private lowerPriority:number = 0;

    /**
     * 设备切换应答时间戳
     */
    private dialogSwitchTimeStamp:number = 0;

    /**
     * 最小唤醒时延
     */
    private minWakeupDelay:number = 0;

    /**
     * 最大唤醒时延
     */
    private maxWakeupDelay:number = 0;

    /**
     * 是不是骨声纹耳机
     */
    private isBoneVoicePrintEarphone:boolean = false;

    public getVoiceEnergyLevel():number {
        return this.voiceEnergyLevel;
    }

    /**
     * 设置声强等级
     *
     * @param voiceEnergyLevel 待设置的声强等级
     * @return 返回本对象
     */
    public setVoiceEnergyLevel(voiceEnergyLevel:number):BasicDeviceParameter {
        this.voiceEnergyLevel = voiceEnergyLevel;
        return this;
    }

    public getVoiceAbsoluteEnergy():number {
        return this.voiceAbsoluteEnergy;
    }

    /**
     * 设置声强绝对值
     *
     * @param energyAbsoluteValue 待设置的声强绝对值
     * @return 返回本对象
     */
    public setVoiceAbsoluteEnergy(energyAbsoluteValue:number):BasicDeviceParameter {
        this.voiceAbsoluteEnergy = energyAbsoluteValue;
        return this;
    }

    /**
     * 设置声纹置信度，由外部唤醒服务传入
     *
     * @param voiceprintConfidence 声纹置信度
     * @return 返回当前基本数据对象
     */
    public setVoiceConfidence(voiceprintConfidence:number):BasicDeviceParameter {
        this.voiceConfidence = voiceprintConfidence;
        return this;
    }

    /**
     * 获取声纹置信度
     *
     * @return 返回设置的声纹置信度否则返回0
     */
    public getVoiceConfidence():number {
        return this.voiceConfidence;
    }

    public getLastUseTimeFromNow():number {
        return this.lastUseTimeFromNow;
    }

    /**
     * 设置上次语音助手使用时间戳
     *
     * @param lastUseTimeFromNow 待设置上次语音助手使用时间戳
     * @return 返回本对象
     */
    public setLastUseTimeFromNow(lastUseTimeFromNow:number):BasicDeviceParameter {
        this.lastUseTimeFromNow = lastUseTimeFromNow;
        return this;
    }

    public getDeviceType():number {
        return this.deviceType;
    }

    /**
     * 设置设备类型
     *
     * @param deviceType 待设置设备类型
     * @return 返回本对象
     */
    public setDeviceType(deviceType:number):BasicDeviceParameter {
        this.deviceType = deviceType;
        return this;
    }

    public getForeground():number {
        return this.foreground;
    }

    /**
     * 设置语音助手是否在前台（当前2019-09-19未使用）
     *
     * @param foreground 默认为0，语音助手在前台设置为1
     * @return 返回本对象
     */
    public setForeground(foreground:number):BasicDeviceParameter {
        this.foreground = foreground;
        return this;
    }

    public getLightScreenManually():number {
        return this.lightScreenManually;
    }

    /**
     * 设置是否5s内亮屏，当前只有手机使用
     *
     * @param lightScreenManually 默认为0，唤醒时5s内亮屏为1
     * @return 返回本对象
     */
    public setLightScreenManually(lightScreenManually:number):BasicDeviceParameter {
        this.lightScreenManually = lightScreenManually;
        return this;
    }

    public getInService():number {
        return this.inService;
    }

    /**
     * 公有设备调用方设置是否在服务中
     *
     * @param inService 设备是否在服务中（音箱在播放音乐等），默认为0，在服务中设置为1
     * @return 返回本对象
     */
    public setInService(inService:number):BasicDeviceParameter {
        this.inService = inService;
        return this;
    }

    public getLowerPriority():number {
        return this.lowerPriority;
    }

    /**
     * 设置低优先级
     *
     * @param lowerPriority 传入语音助手逃生时的时间戳
     * @return 返回本对象
     */
    public setLowerPriority(lowerPriority:number):BasicDeviceParameter {
        this.lowerPriority = lowerPriority;
        return this;
    }

    public getDialogSwitchTimeStamp():number {
        return this.dialogSwitchTimeStamp;
    }

    /**
     * 设置设备切换应答时间戳
     *
     * @param dialogSwitchTimeStamp 传入切换到该设备进行应答的时间戳
     * @return 返回本对象
     */
    public setDialogSwitchTimeStamp(dialogSwitchTimeStamp:number):BasicDeviceParameter {
        this.dialogSwitchTimeStamp = dialogSwitchTimeStamp;
        return this;
    }

    public getMinWakeupDelay():number {
        return this.minWakeupDelay;
    }

    /**
     * 设置最小时延限制（当前未使用）
     *
     * @param minWakeupDelay 待设置最小时延
     * @return 返回本对象
     */
    public setMinWakeupDelay(minWakeupDelay:number):BasicDeviceParameter {
        this.minWakeupDelay = minWakeupDelay;
        return this;
    }

    public getMaxWakeupDelay():number {
        return this.maxWakeupDelay;
    }

    /**
     * 设置最大时延限制（当前未使用）
     *
     * @param maxWakeupDelay 待设置最大时延
     * @return 返回本对象
     */
    public setMaxWakeupDelay(maxWakeupDelay:number):BasicDeviceParameter {
        this.maxWakeupDelay = maxWakeupDelay;
        return this;
    }

    /**
     * 设置是不是骨声纹耳机
     *
     * @param isEarphone 骨声纹耳机标志位
     * @return 返回本对象
     */
    public setBoneVoicePrintEarphone(isEarphone:boolean):BasicDeviceParameter {
        this.isBoneVoicePrintEarphone = isEarphone;
        return this;
    }

    /**
     * 返回骨声纹耳机的标志位
     *
     * @return 是不是骨声纹耳机的标志位
     */
    public getBoneVoicePrintEarphone():boolean {
        return this.isBoneVoicePrintEarphone;
    }

    /**
     * 获取收音状态
     *
     * @return 收音状态
     */
    public getInRadio():number {
        return this.inRadio;
    }

    /**
     * 设置收音状态
     *
     * @param inRadio 收音状态
     *
     * @return 返回本对象,便于链式调用
     */
    public setInRadio(inRadio:number):BasicDeviceParameter {
        this.inRadio = inRadio;
        return this;
    }

    /**
     * 获取设备名称
     *
     * @return 设备名称
     */
    public getDeviceName():string {
        return this.deviceName;
    }

    /**
     * 设置设备名称
     *
     * @param deviceName 设备名称
     *
     * @return 返回本对象,便于链式调用
     */
    public setDeviceName(deviceName:string):BasicDeviceParameter {
        this.deviceName = deviceName;
        return this;
    }

    /**
     * 获取空间谱，int整数
     *
     * @return 空间谱值
     */
    public getSpatialSpectrum():number {
        return this.spatialSpectrum;
    }

    /**
     * 设置空间谱
     *
     * @param spatialSpectrum 空间谱值
     * @return 返回此实例
     */
    public setSpatialSpectrum(spatialSpectrum:number):BasicDeviceParameter {
        this.spatialSpectrum = spatialSpectrum;
        return this;
    }
}
