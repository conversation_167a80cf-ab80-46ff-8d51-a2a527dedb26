/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import { BasicDeviceParameter } from './BasicDeviceParameter';

const TAG = "InnerDeviceParameter";

/**
 * 内部设备信息对象，保存由外部传入的协同信息换算成的相关优先级分值
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-28
 */
export class InnerDeviceParameter extends BasicDeviceParameter {
    /**
     * 高优先级分值
     */
    private highPriority:number = 0;

    /**
     * 普通优先级分值
     */
    private normalPriority:number = 0;

    /**
     * 低优先级分值
     */
    private negativePriority:number = 0;

    /**
     * 预留位
     */
    private reservedColumn:number = 0;

    public getHighPriority():number {
        return this.highPriority;
    }

    /**
     * 设置高优先级
     *
     * @param highPriority 待设置高优先级
     * @return 返回本对象
     */
    public setHighPriority(highPriority:number):InnerDeviceParameter {
        this.highPriority = highPriority;
        return this;
    }

    public getNormalPriority():number {
        return this.normalPriority;
    }

    /**
     * 设置普通优先级分值
     *
     * @param normalPriority 待设置普通优先级
     * @return 返回本对象
     */
    public setNormalPriority(normalPriority:number):InnerDeviceParameter {
        this.normalPriority = normalPriority;
        return this;
    }

    public getNegativePriority():number {
        return this.negativePriority;
    }

    /**
     * 设置负优先级分值
     *
     * @param negativePriority 默认是1，设备处于负优先级时值为0
     * @return 返回本对象
     */
    public setNegativePriority(negativePriority:number):InnerDeviceParameter {
        this.negativePriority = negativePriority;
        return this;
    }

    public getReservedColumn():number {
        return this.reservedColumn;
    }

    /**
     * 0号规则设置预留位的参数值
     *
     * @param reservedColumn 0号规则数据格式解析得到的预留位数值
     * @return 返回本对象
     */
    public setReservedColumn(reservedColumn:number):InnerDeviceParameter {
        this.reservedColumn = reservedColumn;
        return this;
    }
}