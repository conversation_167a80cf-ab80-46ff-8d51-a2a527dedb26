/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

const TAG = "State";

/**
 * 状态机状态枚举类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-03-14
 */
export enum State {
    /**
     * 空闲状态
     */
    STANDBY,

    /**
     * 设备唤醒后，抑制其它设备约2s
     */
    INHIBITING,

    /**
     * 被唤醒的设备抑制
     */
    INHIBITED,

    /**
     * 唤醒设备发送工作包
     */
    WORKING,

    /**
     * 收到唤醒设备发送的工作包
     */
    WORKED
}