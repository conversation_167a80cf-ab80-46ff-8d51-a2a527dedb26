/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */

import List from '@ohos.util.List';

const TAG:string = 'CoordinatorResult';

/**
 * 协同结果收据类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-01-02
 */
export class CoordinatorResult {
    private isShouldResponse: boolean;

    private isCoordinator: boolean;

    private receivedDeviceCount: number;

    private deviceIdList: List<number>;

    private wakeUpRandomId: number;

    private responseDeviceName: string = "";

    private phoneReport: string = "";

    public setIsShouldResponse(value: boolean): void {
        this.isShouldResponse = value;
    }

    public getIsShouldResponse(): boolean {
        return this.isShouldResponse;
    }

    public setIsCoordinator(value: boolean): void {
        this.isCoordinator = value;
    }

    public getIsCoordinator(): boolean {
        return this.isCoordinator;
    }

    public setReceivedDeviceCount(value: number): void {
        this.receivedDeviceCount = value;
    }

    public getReceivedDeviceCount(): number {
        return this.receivedDeviceCount;
    }

    public setDeviceIdList(value: List<number>): void {
        this.deviceIdList = value;
    }

    public getGeviceIdList(): List<number> {
        return this.deviceIdList;
    }

    public setWakeUpRandomId(value: number): void {
        this.wakeUpRandomId = value;
    }

    public getWakeUpRandomId(): number {
        return this.wakeUpRandomId;
    }

    public setResponseDeviceName(value: string): void {
        this.responseDeviceName = value;
    }

    public getResponseDeviceName(): string {
        return this.responseDeviceName;
    }

    /**
     * 设置手机打点数据
     *
     * @param value 手机上次协同的HA打点数据
     */
    public setPhoneReport(value: string): void {
        this.phoneReport = value;
    }

    /**
     * 获取手机打点数据 （因内存问题，手机HA打点传给语音主进程打点）
     *
     * @returns 手机HA打点数据
     */
    public getPhoneReport(): string {
        return this.phoneReport;
    }

    /**
     * 协同结果数据构造函数
     *
     * @param isShouldResponse 协同结果，即当前设备是否需要响应用户。
     * @param isCoordinator 是否参与了协同
     * @param receivedDeviceCount 返回感知到的周围参与协同唤醒的设备个数，如果单设备响应返回0
     * @param deviceIdList 二级唤醒的设备id集合
     * @param wakeUpRandomId 唤醒随机Id
     */
    constructor(isShouldResponse: boolean, isCoordinator: boolean, receivedDeviceCount: number, deviceIdList: List<number>, wakeUpRandomId: number) {
        this.isShouldResponse = isShouldResponse;
        this.isCoordinator = isCoordinator;
        this.receivedDeviceCount = receivedDeviceCount;
        this.deviceIdList = deviceIdList;
        this.wakeUpRandomId = wakeUpRandomId;
    }
}