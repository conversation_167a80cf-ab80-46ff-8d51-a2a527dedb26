/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2024. All rights reserved.
 */
import { Constants } from '../utils/Constants';
import WakeUpLog from '../utils/WakeUpLog';
import { State } from './State'
import { AsyncCallback } from '@ohos.base';

const TAG: string = "StateMachine";

/**
 * 协同唤醒状态机
 *
 * <AUTHOR>
 * @version 3.0
 * @since 2024-01-03
 */
export class StateMachine {
  /**
   * 不在协同唤醒中
   */
  public static STATE_NA: number = -1;
  /**
   * 开始执行发送广播探测包
   */
  public static STATE_COORDINATOR_START_DETECTED: number = 3;
  /**
   * {@link DeviceData}本设备提前协同应答状态
   */
  public static STATE_DEVICE_INFO_COORDINATED_ADVANCE: number = 5;
  /**
   * {@link DeviceData}本设备二级唤醒失败包发送状态
   */
  public static STATE_DEVICE_INFO_START_FAILED_PACKAGE: number = 6;
  /**
   * 开始执行协同唤醒
   */
  private static STATE_COORDINATOR_START: number = 0;
  /**
   * 开始广播{@link DeviceData}数据
   */
  private static STATE_DEVICE_INFO_SEND_START: number = 1;
  /**
   * {@link DeviceData}数据广播结束
   */
  private static STATE_DEVICE_INFO_SEND_END: number = 2;
  /**
   * {@link DeviceData}数据广播探测包结束
   */
  private static STATE_DEVICE_INFO_DETECTED_SEND_END: number = 4;
  private static sNewState: State;
  private static sState: number;
  private static sVoiceAssistantState = Constants.DEFAULT_VALUE;

  public static getVoiceAssistantState(): number {
    return StateMachine.sVoiceAssistantState;
  }

  public static setVoiceAssistantState(state: number): void {
    StateMachine.sVoiceAssistantState = state;
  }

  public static setNewState(state: State): void {
    StateMachine.sNewState = state;
  }

  public static getNewState(): State {
    return StateMachine.sNewState;
  }

  public static setState(state: number): void {
    WakeUpLog.i(TAG, "setState::from " + StateMachine.sState + StateMachine.stateMapping(StateMachine.sState) + " to" + state + StateMachine.stateMapping(state));
    StateMachine.sState = state;
  }

  public static getState(): State {
    return StateMachine.sState;
  }

  private static stateMapping(state: number): string {
    switch (state) {
      case StateMachine.STATE_COORDINATOR_START:
        return "STATE_COORDINATOR_START";

      case StateMachine.STATE_DEVICE_INFO_SEND_START:
        return "STATE_DEVICE_INFO_SEND_START";

      case StateMachine.STATE_DEVICE_INFO_SEND_END:
        return "STATE_DEVICE_INFO_SEND_END";
      case StateMachine.STATE_COORDINATOR_START_DETECTED:
        return "STATE_COORDINATOR_START_DETECTED";
      case StateMachine.STATE_DEVICE_INFO_DETECTED_SEND_END:
        return "STATE_DEVICE_INFO_DETECTED_SEND_END";
      case StateMachine.STATE_DEVICE_INFO_COORDINATED_ADVANCE:
        return "STATE_DEVICE_INFO_COODINATED_BEFORE";
      case StateMachine.STATE_DEVICE_INFO_START_FAILED_PACKAGE:
        return "STATE_DEVICE_INFO_START_FAILED_PACKAGE";
      default:
        return "STATE_NA";
    }
  }
}