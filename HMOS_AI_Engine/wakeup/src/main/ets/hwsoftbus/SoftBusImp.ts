/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2025. All rights reserved.
 */

import { CoordinatorListenerImp } from '../CoordinatorListenerImp';
import { Context } from '@kit.AbilityKit';
import { distributedDeviceManager } from '@kit.DistributedServiceKit';
import WakeUpLog from '../utils/WakeUpLog';
import { BusinessError } from '@kit.BasicServicesKit';
const TAG:string = "SoftBusImp";

/**
 * 软总线数据监听
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-14
 */
export class SoftBusImp {
    private deviceManager: distributedDeviceManager.DeviceManager;
    private isBound: boolean = false;
    private softBusImp: string = "com.huawei.wakeup.coordination";

    constructor(context: Context, coordinatorListener: CoordinatorListenerImp) {
        WakeUpLog.i(TAG, 'SoftBusImp constructor.');
        this.bindDevMonitor();
    }

    /**
     * 绑定设备监视器
     */
    private bindDevMonitor(): void {
        if (this.isBound) {
            WakeUpLog.i(TAG, "isBound is true no need to bindDevMonitor");
            return;
        }
        try {
            this.deviceManager = distributedDeviceManager.createDeviceManager(this.softBusImp);
            this.isBound = true;
        } catch (err) {
            let e: BusinessError = err as BusinessError;
            console.error('createDeviceManager errCode:' + e.code + ',errMessage:' + e.message);
        }
    }

    /**
     * 获取软总线设备列表,保存在nodeBasicInfoList
     *
     * @return 设备列表
     */
    public getDeviceListBySoftBus(): distributedDeviceManager.DeviceBasicInfo[] {

        let nodeBasicInfoList:distributedDeviceManager.DeviceBasicInfo[] = [];
        if (!this.isBound) {
            WakeUpLog.i(TAG, "findDevices isBound is false");
            return nodeBasicInfoList;
        }
        let remoteNodesList: distributedDeviceManager.DeviceBasicInfo[] = [];
        try {
        remoteNodesList = this.deviceManager.getAvailableDeviceListSync();
        }catch (e){
            WakeUpLog.e(TAG, "without DM permission!");
        }

        if (!remoteNodesList) {
            WakeUpLog.i(TAG, "getDeviceListBySoftBus:: remoteNodeList is null");
        } else {
            // 添加远端设备
            nodeBasicInfoList = nodeBasicInfoList.concat(remoteNodesList);
            WakeUpLog.i(TAG, "remoteNodesList.getList()= " + JSON.stringify(remoteNodesList));
        }

        if (nodeBasicInfoList.length > 0) {
            nodeBasicInfoList = nodeBasicInfoList.filter(nodeBasicInfo => nodeBasicInfo !== null);
        }

        WakeUpLog.i(TAG, "filter list size = " + nodeBasicInfoList.length);
        for (let nodeBasicInfo of nodeBasicInfoList) {
            WakeUpLog.i(TAG, nodeBasicInfo.toString());
        }
        return nodeBasicInfoList;
    }

    /**
     * 与软总线断开连接
     */
    public unbindSoftBus(): void {
        if (this.deviceManager !== null) {
            this.deviceManager = undefined;
            this.isBound = false;
        }
    }
}
