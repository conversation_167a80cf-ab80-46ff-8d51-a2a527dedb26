/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 *
 * Script for compiling build behavior.
 */

const path = require("path");
const child_process = require('child_process');

const projectRootPath = process.cwd()
const userName = "w30071960";
const password = "!mzdxcfkwaj0801";
const osUrl = process.env.HOS_SIGN_JAR || process.env.onlineSignJarPath;

const onlineSignServer = 'http://cpki-kms.cbg.huawei.com:18888/sign';
const hapSignTool = 'hap-sign-tool.jar';
const hapSignOnlinePlugin = 'hapsign-online-plugin.jar';
let p7bFileName = '125664release.p7b';
const keyAlias = 'HiAIEngineForHarmonyOS';

const signMaterialPath = path.resolve(projectRootPath, 'hm_sign');
const onlineSignJarPath = process.env.onlineSignJarPath || path.resolve(projectRootPath, 'hm_sign');

// 调用签名工具执行签名的具体逻辑,需要根据各自需求和场景自行实现
// Tips: 在IDE场景下,在线签名工具生成的签名后的hap必须默认仍然放置到/build/default/outputs/default/目录下,且包名以signed.hap为后缀
function executeOnlineSign(inputFile, outputFile, assembleName = '', envType = '') {
    let command2 = [];
    if(assembleName === 'assembleApp' && envType){
        p7bFileName = `AgcCertificatesFiles/${envType}AgcAppGallery.p7b`;
        command2 = [
            "-appCertFile",
            path.resolve(signMaterialPath, `AgcCertificatesFiles/${envType}AgcAppGallery.cer`)
        ];
    }

    const signToolFile = path.resolve(onlineSignJarPath, hapSignTool);
    const p7bFile = path.resolve(signMaterialPath, p7bFileName);

    const command1 = [
        "-jar",
        signToolFile,
        "sign-app",
        "-mode",
        "remoteSign",
        "-signServer",
        onlineSignServer,
        "-signerPlugin",
        hapSignOnlinePlugin,
        "-onlineAuthMode",
        "account",
        "-username",
        userName,
        "-userPwd",
        password,
        "-profileFile",
        p7bFile
    ];

    const command3 = [
        "-compatibleVersion",
        "8",
        "-signAlg",
        "SHA256withECDSA",
        "-keyAlias",
        keyAlias,
        "-inFile",
        inputFile,
        "-outFile",
        outputFile
    ];

    const command = [
        ...command1,
        ...command2,
        ...command3
    ]

    const result = child_process.spawnSync("java", command, {
        encoding: 'utf-8',
        windowsHide: true
    });
    if (result.stderr) {
        console.error(result.stderr.trim());
    }
}

module.exports = {
    executeOnlineSign: executeOnlineSign
}