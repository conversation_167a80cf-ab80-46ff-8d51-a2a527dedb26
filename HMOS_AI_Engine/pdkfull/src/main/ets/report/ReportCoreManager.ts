/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2025. All rights reserved.
 */
import common from '@ohos.app.ability.common';
import hiAnalytics from '@hms.core.hiAnalytics';
import BundleManager from '@ohos.bundle.bundleManager'
import { BaseInfo,CallerInfo } from './MaintenanceReportInfo';
import MaintenanceReportInfo from './MaintenanceReportInfo'
import HiAi<PERSON>og from '../utils/log/HiAiLog';
import { ReportPlatformType } from './ReportPlatformType';
import ObjectUtil from '../utils/ObjectUtil'
import { GlobalContextGetKey, GlobalContextSetKey } from '../utils/GlobalContext';
import HaHelper from './HaManager'
import { HiAnalyticsReportType } from './HiAnalyticsReportType';
import { GetBaseInfo, GetAppInfo, IntegrateInfo } from './ReportModel'
import SystemParameterHelper from '../platform/oswrapper/SystemParameterHelper';
import { BusinessError } from '../interfaces/ICallback';
import { MaintenanceEventID } from './ReportNameSpace';
import buffer from '@ohos.buffer';


const TAG: string = "ReportCoreManager";
const defaultTag = 'HiAI'

type commonContext = common.UIAbilityContext | common.ServiceExtensionContext;

/**
 * This class is a report management class.
 * The information of reporting can be found { MaintenanceReportInfo, OperatorReportInfo }.
 * @see { MaintenanceReportInfo, OperatorReportInfo }
 * <AUTHOR>
 */
export default class ReportCoreManager {
    context: commonContext;

    /**
     * The constructor of this class.
     * @class
     */
    constructor(context: commonContext) {
        this.context = context;
    }

    /**
     * Get an instance object of this class.
     *
     * @param { Context.AbilityContext } the ability context
     * @returns { ReportCoreManager } the instance of ReportCoreManager
     */
    static async getInstance(context: commonContext): Promise<ReportCoreManager> {
        if (!GlobalContextGetKey('ReportCoreManager')) {
            await this.setConfig(context)
        }
        return (GlobalContextSetKey("ReportCoreManager", new ReportCoreManager(context)) as ReportCoreManager);
    }


    private static async setConfig(context: commonContext): Promise<void> {
        let myResourceManager = context.resourceManager;
        let urlBuffer = await myResourceManager.getRawFileContent("ReportUrl.json");
        let url: string = JSON.parse(buffer.from(urlBuffer.buffer).toString()).live_url;
        let configOptions = HaHelper.BuildSimpleConfig(defaultTag, url);
        await hiAnalytics.setConfigOptions(configOptions).then(() => {
            HiAiLog.info(TAG, "SetConfigOption   Success");
        }).catch(err => {
            HiAiLog.error(TAG, "SetConfigOption   Failed: " + err.message);
        })
    }

    /**
     * Called when maintenance data report. It is an synchronous method.
     *
     * @param maintenanceReportInfo the information of maintenance report
     * @param reportPlatformType the report platform type
     */
    onMaintenanceReport(maintenanceReportInfo: MaintenanceReportInfo, reportPlatformType?: string): void {
        HiAiLog.info(TAG, "start to maintenance report");
        if (ObjectUtil.isEmptyText(reportPlatformType)) {
            reportPlatformType = ReportPlatformType.HI_ANALYTICS_PLATFORM_TYPE;
            HiAiLog.info(TAG, "ReportPlatformType HiAnalytics success");
        }

        let eventId: string;
        let key: string = maintenanceReportInfo.eventId.toString();
        // Verify eventId
        for (const k in MaintenanceEventID) {
            if (MaintenanceEventID[k] === key) {
                eventId = MaintenanceEventID[k];
            }
        }
        if (!eventId) {
            HiAiLog.error(TAG, `event id get error,get key===${key}`);
        }

        let baseInfo: GetBaseInfo = this.getBaseInfo();
        let appInfo: GetAppInfo = this.getAppInfo();
        let callerBaseInfo: BaseInfo = maintenanceReportInfo.baseInfo;
        let callerInfo: CallerInfo = maintenanceReportInfo.callerInfo;
        let extraInfo: string = maintenanceReportInfo.extraInfo;
        let reportBaseValue: IntegrateInfo = new IntegrateInfo(baseInfo, appInfo, callerBaseInfo, callerInfo, extraInfo);

        hiAnalytics.onEvent(defaultTag, HiAnalyticsReportType.operatorReport, eventId, {
            ...reportBaseValue
        }, (err: BusinessError | null) => {
            HiAiLog.info(TAG, `reportBaseValue====${JSON.stringify(reportBaseValue)}`);
            if (err == null) {
                HiAiLog.info(TAG, "onEvent MaintenanceReport Success");
            } else {
                HiAiLog.info(TAG, "onEvent MaintenanceReport Failed: " + err.message);
            }
        })
    }

    /**
     * Called when get base device information.
     */
    private getBaseInfo(): GetBaseInfo {
        let deviceType: string = SystemParameterHelper.getDeviceType();
        let deviceName: string = SystemParameterHelper.getDeviceModel();
        let romVersion: string = SystemParameterHelper.getOhosVersion();
        let baseInfo: GetBaseInfo = new GetBaseInfo(deviceType, deviceName, romVersion);
        return baseInfo;
    }

    /**
     * Called when get base app information.
     */
    private getAppInfo(): GetAppInfo {
        let appInfo: GetAppInfo = new GetAppInfo();
        try{
            let bundleFlags = BundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_REQUESTED_PERMISSION;
            let data = BundleManager.getBundleInfoForSelfSync(bundleFlags);
            if (data) {
                appInfo.appVersion = data.versionName;
                appInfo.appName = data.name;
            }
            HiAiLog.info(TAG, 'getBundleInfoForSelfSync successfully，data====' + JSON.stringify(data));
        }catch(error){
            HiAiLog.info(TAG, 'getBundleInfoForSelfSync failed=====' + JSON.stringify(error));
        }
        return appInfo;
    }
}