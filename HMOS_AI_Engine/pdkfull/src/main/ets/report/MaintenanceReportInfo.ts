/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import Constants from '../utils/Constants';

/**
 * Indicates the maintenance report information
 * @class
 * <AUTHOR>
 */
export default class MaintenanceReportInfo {
    /**
     * Indicates the Maintenance eventId
     * @type { string }
     */
    eventId: string = '';
    /**
     * Indicates the baseInfo
     * @type { string }
     */
    baseInfo: BaseInfo = new BaseInfo();
    /**
     * Indicates the callInfo
     * @type { string }
     */
    callerInfo: CallerInfo = new CallerInfo();
    /*
     * Optional Subsegment extraInfo
     * @type { string }
     * */
    extraInfo?: string = '';
}

/**
 * Indicates the base information
 * @class
 * <AUTHOR>
 */

export class BaseInfo {
    /**
     * Indicates the module name
     * @type { string }
     */
    moduleName: string = Constants.EMPTY_STRING;
    /**
     * Indicates the interface name
     * @type { string }
     */
    interfaceName: string = Constants.EMPTY_STRING;
    /**
     * Indicates the service name
     * @type { string }
     */
    serviceName: string = Constants.EMPTY_STRING;
    /**
     * Indicates the run time
     * @type { string }
     */
    runtime: string = Constants.EMPTY_NUMBER.toString();
    /**
     * Indicates the rsult code
     * @type { string }
     */
    resultCode: string = Constants.EMPTY_NUMBER.toString();
    /**
     * Indicates the detail message
     * @type { string }
     */
    detailMessage: string = Constants.EMPTY_STRING;
}

/**
 * Indicates the maintenance report information
 * @class
 * <AUTHOR>
 */
export class CallerInfo {
    /**
     * Indicates the app name
     * @type { string }
     */
    appName: string = Constants.EMPTY_STRING;
    /**
     * Indicates the app version
     * @type { string }
     */
    appVersion: string = Constants.EMPTY_STRING;
}
