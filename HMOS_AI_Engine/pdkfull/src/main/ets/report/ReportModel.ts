/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { BaseInfo, CallerInfo } from './MaintenanceReportInfo'

/**
 * Indicates the Report Base Device Value
 * @class
 * <AUTHOR>
 */
export class GetBaseInfo {
    deviceType: string;
    deviceName: string;
    romVersion: string;

    constructor(deviceType: string, deviceName: string, romVersion: string) {
        this.deviceType = deviceType;
        this.deviceName = deviceName;
        this.romVersion = romVersion;
    }
}

/**
 * Indicates the Report Base App Value
 * @class
 * <AUTHOR>
 */
export class GetAppInfo {
    appName: string;
    appVersion: string;

    constructor(appName?: string, appVersion?: string) {
        this.appName = appName;
        this.appVersion = appVersion;
    }
}

/*
 * Integrate appInfo, baseInfo, and callerInfo into a {key:value} object.
 * the property name following the format of snake which is required by WiseDataOps cloud.
 * */
export class IntegrateInfo {
    device_type: string;
    device_model: string;
    interface_name: string
    module_name: string;
    result_code: string;
    os_version: string;
    run_duration: string;
    service_name: string;
    message: string;
    app_id: string;
    app_ver_code: string;
    app_name: string;
    app_ver: string;
    extra_info: string;

    constructor(baseInfo: GetBaseInfo, appInfo: GetAppInfo, callerBaseInfo: BaseInfo, callerInfo: CallerInfo, extraInfo?: string) {
        this.device_type = baseInfo.deviceType;
        this.device_model = baseInfo.deviceName;
        this.interface_name = callerBaseInfo.interfaceName;
        this.module_name = callerBaseInfo.moduleName;
        this.result_code = callerBaseInfo.resultCode.toString();
        this.os_version = baseInfo.romVersion;
        this.run_duration = callerBaseInfo.runtime.toString();
        this.service_name = callerBaseInfo.serviceName;
        this.message = callerBaseInfo.detailMessage;
        this.app_id = appInfo.appName;
        this.app_ver_code = appInfo.appVersion;
        this.app_name = callerInfo.appName;
        this.app_ver = callerInfo.appVersion;
        this.extra_info = extraInfo;
    }
}

