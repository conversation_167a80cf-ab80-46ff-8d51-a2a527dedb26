/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2025. All rights reserved.
 */

import hilog from '@ohos.hilog';
import prompt from '@ohos.promptAction';

/**
 * List of definitions for Report Types.
 * <AUTHOR>
 */
export default class HaHelper {
  static BuildSimpleConfig(tag: string, host: string, appId?: string): any {
    let config = {
      instanceTag: tag,
      appId: appId,
      operationConfig: {
        collectUrl: host
      },
      maintenanceConfig: {
        collectUrl: host
      }
    }
    return config;
  }
}