/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import Want from '@ohos.app.ability.Want';
import common from '@ohos.app.ability.common';
import RemoteDeathRecipient from './utils/RemoteDeathRecipient';
import { BusinessError, ErrorCallback } from './interfaces/ICallback';
import <PERSON><PERSON><PERSON><PERSON>og from './utils/log/HiAiLog';
import { ElementName } from './interfaces/abililtyBridge';
import Ability from '@ohos.ability.ability'

const TAG: string = "ServiceAbilityBridge";
const DEFAULT_CONNECT_NUM: number = -1;
const SYNCHRONOUS_REQUEST_FLAG: number = 0;
const ASYNCHRONOUS_REQUEST_FLAG: number = 1;

// wait 2 seconds timeout
const DEFAULT_WAIT_TIMEOUT: number = 2;
type CommonAbilityContext = common.UIAbilityContext | common.ServiceExtensionContext;

/**
 * This class is used to connect to services or send remote requests. Synchronous remote requests and asynchronous
 * remote requests are supported here. When a request is initiated, the connection status will be automatically judged.
 * If the remote service is not connected, the connection service will be initiated first.
 * <AUTHOR>
 */
export default class ServiceAbilityBridge {
    context: CommonAbilityContext;
    serviceRemote: rpc.IRemoteObject | null = null;
    remoteDeathRecipient: RemoteDeathRecipient;
    abilityConnectNum: number | null = null;
    want: Want;

    /**
     * Constructor of ServiceAbilityBridge
     * @param context { context: Context.AbilityContext } the ability context
     * @param want { Want } the ability want
     * @returns { Promise<rpc.IRemoteObject> } the instance of hiai framework remote object
     */
    constructor(context: CommonAbilityContext, want: Want) {
        this.context = context;
        this.want = want;
        this.remoteDeathRecipient = new RemoteDeathRecipient();
    }

    /**
     * Connect service ability
     * @param want { Want } the ability want
     * @returns { Promise<rpc.IRemoteObject> } the instance of remote object
     */
    async connectService(): Promise<rpc.IRemoteObject> {
        return new Promise((resolve: (value: rpc.IRemoteObject | PromiseLike<rpc.IRemoteObject>) => void, reject: (reason?: void | null | undefined) => void) => {
            if (this.serviceRemote !== null && this.serviceRemote !== undefined) {
                HiAiLog.info(TAG, `callback with cache`);
                resolve(this.serviceRemote);
                return;
            }
            let connectOption: Ability.ConnectOptions = {
                onConnect: (element: ElementName, remote: rpc.IRemoteObject): void => {
                    HiAiLog.info(TAG, `connectAbility onConnect:${element} and remote ${remote}`);
                    if (remote == null) {
                        HiAiLog.info(TAG, `connect with null`);
                        reject(null);
                        return;
                    }
                    this.serviceRemote = remote;
                    let deathHandle: ErrorCallback<BusinessError> = (businessError: BusinessError): void => {
                        HiAiLog.info(TAG, `connectAbility onConnect deathHandle :${businessError}`);
                        this.abilityConnectNum = DEFAULT_CONNECT_NUM;
                    };
                    this.remoteDeathRecipient.registerRemoteObject(remote, deathHandle);
                    resolve(this.serviceRemote);
                },
                onDisconnect: (element: ElementName): void => {
                    HiAiLog.info(TAG, `connectHiAIAbility onDisconnect :${element}`);
                    this.serviceRemote = null;
                    this.abilityConnectNum = DEFAULT_CONNECT_NUM;
                    reject(null);
                },
                onFailed: (code: number): void => {
                    HiAiLog.info(TAG, `connectAbility onFailed errCode:${code}`);
                    reject(null);
                }
            } as Ability.ConnectOptions;
            let connectName: string = this.want.abilityName;
            HiAiLog.info(TAG, `connect ability :${connectName}`);
            this.abilityConnectNum = this.context.connectServiceExtensionAbility(this.want, connectOption);
        });
    }

    /**
     * Disconnect service
     */
    disconnectService(): void {
        HiAiLog.info(TAG, `disconnect service ability`);
        if (this.abilityConnectNum) {
            this.context.disconnectServiceExtensionAbility(this.abilityConnectNum);
        }
    }

    /**
     * Sends a {@link rpc.MessageSequence} message to the peer process synchronously.
     * @param code the capability id
     * @param data {@link rpc.MessageSequence} object holding the data to send.
     * @param reply {@link rpc.MessageSequence} object that receives the response.
     * @param options operations Indicates the synchronous or asynchronous mode to send messages.
     * @returns Promise used to return the {@link rpc.RequestResult} instance.
     */
    async sendRequestSync(code: number, requestData: rpc.MessageSequence, reply: rpc.MessageSequence, options?: rpc.MessageOption): Promise<rpc.RequestResult> {
        HiAiLog.info(TAG, `sendRequestSync send remote request ID :${code}`);
        if (options === null || options === undefined) {
            options = new rpc.MessageOption(SYNCHRONOUS_REQUEST_FLAG);
        }
        options.setWaitTime(DEFAULT_WAIT_TIMEOUT);
        return this.initRemoteSendRequest(code, requestData, reply, options);
    }

    /**
     * Sends a {@link MessageSequence} message to the peer process asynchronously.
     * @param code the capability id
     * @param data {@link rpc.MessageSequence} object holding the data to send.
     * @param reply {@link rpc.MessageSequence} object that receives the response.
     * @param options operations Indicates the synchronous or asynchronous mode to send messages.
     * @returns Promise used to return the {@link rpc.RequestResult} instance.
     */
    sendRequestAsync(code: number, requestData: rpc.MessageSequence, reply: rpc.MessageSequence, options?: rpc.MessageOption): void {
        HiAiLog.info(TAG, `sendRequestAsync send remote request ID :${code}`);
        if (options === null || options === undefined) {
            options = new rpc.MessageOption(ASYNCHRONOUS_REQUEST_FLAG);
        }
        options.setWaitTime(DEFAULT_WAIT_TIMEOUT);
        this.initRemoteSendRequest(code, requestData, reply, options).finally(() => {
            reply.readException();
            requestData.reclaim();
            reply.reclaim();
            HiAiLog.info(TAG, `send sendRequest success`);
        });
    }

    private async initRemoteSendRequest(code: number, requestData: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): Promise<rpc.RequestResult> {
        if (this.serviceRemote !== null && this.serviceRemote !== undefined) {
            HiAiLog.info(TAG, `send request with cache`);
            return this.sendRequest(this.serviceRemote, code, requestData, reply, options);
        } else {
            let remote: rpc.IRemoteObject = await this.connectService();
            if (remote != null) {
                return this.sendRequest(remote, code, requestData, reply, options);
            } else {
                HiAiLog.info(TAG, `connetct fialid`);
                return Promise.reject(null);
            }
        }
    }

    private async sendRequest(remoteObject: rpc.IRemoteObject, code: number, requestData: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): Promise<rpc.RequestResult> {
        return remoteObject.sendMessageRequest(code, requestData, reply, options);
    }
}