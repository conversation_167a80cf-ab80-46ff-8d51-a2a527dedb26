/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import common from '@ohos.app.ability.common';
import Want from '@ohos.app.ability.Want';
import HiAiLog from './utils/log/HiAiLog';
import ServiceAbilityBridge from './ServiceAbilityBridge';
import { CommonMethod } from './interfaces/FrameworkCapability';
import rpc from '@ohos.rpc';
import { GlobalContextSetKey } from './utils/GlobalContext';

const TAG: string = "HiAIServiceAbilityBridge";
type CommonAbilityContext = common.UIAbilityContext | common.ServiceExtensionContext;

const HIAI_SERVICE_WANT: Want = {
    bundleName: 'com.huawei.hmsapp.hiai',
    moduleName: 'support',
    abilityName: 'HiAIServiceAbility',
};

/**
 * The HiAI framework Ability bridge is used to bind the basic services of the framework.
 * <AUTHOR>
 */
export default class HiAIServiceAbilityBridge extends ServiceAbilityBridge {
    private static instance: HiAIServiceAbilityBridge;
    isInitSuccess: boolean;

    constructor(context: CommonAbilityContext) {
        super(context, HIAI_SERVICE_WANT);
        this.isInitSuccess = false;
    }

    /**
     * Get an instance object of this class.
     * @returns { HiAIServiceAbilityBridge } the instance of HiAIServiceAbilityBridge
     */
    static getInstance(context: CommonAbilityContext): HiAIServiceAbilityBridge {

        return (GlobalContextSetKey("HiAIServiceAbilityBridge", new HiAIServiceAbilityBridge(context)) as HiAIServiceAbilityBridge);
    }

    /**
     * init hiai service ability
     */
    initHiAIServiceAbility(): void {
        HiAiLog.info(TAG, "init HiAI service");
        let requestData: rpc.MessageSequence = rpc.MessageSequence.create();
        let reply: rpc.MessageSequence = rpc.MessageSequence.create();
        this.sendRequestAsync(CommonMethod.INITIALIZATION, requestData, reply);
    }

    /**
     * Release hiai resources
     */
    releaseHiAIService(): void {
        HiAiLog.info(TAG, "release HiAI service");
        let requestData: rpc.MessageSequence = rpc.MessageSequence.create();
        let reply: rpc.MessageSequence = rpc.MessageSequence.create();
        this.sendRequestAsync(CommonMethod.RELEASE, requestData, reply);
    }
}