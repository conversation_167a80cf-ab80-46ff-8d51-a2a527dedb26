/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import HiAiLog from '../utils/log/HiAiLog';
import { IModelManagerCallback } from './IModelManagerCallback';
import rpc from '@ohos.rpc';
import ResultInfo from './ModelManagerResultInfo';
import { ModelDownloadCallbackType } from '../modelDownload/ModelDownloadConstant';

const TAG = "ModelManagerCallbackProxy";

/**
 *  This class is an implementation of IModelManagerCallback and is used to process data on the server.
 *
 * * <AUTHOR>
 */
export class ModelManagerCallbackProxy implements IModelManagerCallback {
  private remote: rpc.IRemoteObject;

  /*
   * constructor
   *
   * @param { remote } Remote Object
   */
  public constructor(remote: rpc.IRemoteObject) {
    HiAiLog.info(TAG, "ModelManagerCallbackProxy constructor");
    this.remote = remote;
  }

  /*
   * @Override
   *
   */
  public asObject(): rpc.IRemoteObject {
    HiAiLog.info(TAG, "ModelManagerCallbackProxy asObject");
    return this.remote;
  }

  /**
   * @Override
   */
  onResult(isAllSuccess: boolean, resultMessage: string, resultList: Array<ResultInfo>): void {
    HiAiLog.info(TAG,
      "ModelManagerCallbackProxy onResult; isAllSuccess: " + isAllSuccess + ', resultMessage: ' + resultMessage +
        ', resultList:' + JSON.stringify(resultList));
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeBoolean(isAllSuccess);
    requestData.writeString(resultMessage);

    let length: number = 0;
    if (resultList !== null && resultList !== undefined) {
      length = resultList.length;
    }
    requestData.writeInt(length);

    if (length !== 0) {
      for (let resultInfo of resultList) {
        requestData.writeParcelable(resultInfo);
      }
    }
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    this.remote.sendMessageRequest(ModelDownloadCallbackType.MODEL_MANAGER_ON_RESULT, requestData, reply, option)
      .then(() => {
        reply.readException();
        requestData.reclaim();
        reply.reclaim();
        HiAiLog.info(TAG, " onResult sendRequest success ");
      }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(TAG, " onResult error is:" + JSON.stringify(err));
    });
  }
}