/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import rpc from '@ohos.rpc';
import HiAiLog from '../../utils/log/HiAiLog';

const TAG: string = "UpdateModelWindowResultInfo";

/**
 * Indicates the information of model about deleting、subscribing and unsubscribing.
 *
 * <AUTHOR>
 */
export default class UpdateModelWindowResultInfo implements rpc.Parcelable {
  /**
   * Indicates the model ID.
   * @type { string }
   */
  resId: string;
  /**
   * Indicates the domain of model.
   * @type { string }
   */
  domain: string;
  /**
   * Indicates the current version of the model.
   *
   * @type { string }
   */
  resVersion: string;
  /**
   * Indicates the compatible versions of model with plugin.
   *
   * @type { string }
   */
  resCompatibleVersion: string;
  /**
   * When an error occurs, this field returns the error code of the operation on the model.
   *
   * @type { string }
   */
  errorCode: number;
  /**
   * When an error occurs, this field returns the operation error description of the model.
   *
   * @type { string }
   */
  errorMessage: string;

  /**
   * @override
   */
  marshalling(messageSequence: rpc.MessageSequence): boolean {
    messageSequence.writeString(this.resId);
    messageSequence.writeString(this.domain);
    messageSequence.writeString(this.resVersion);
    messageSequence.writeString(this.resCompatibleVersion);
    messageSequence.writeInt(this.errorCode);
    messageSequence.writeString(this.errorMessage);
    HiAiLog.info(TAG, "ModelManagerResultInfo marshalling.");
    return true;
  }

  /**
   * @override
   */
  unmarshalling(messageSequence: rpc.MessageSequence): boolean {
    this.resId = messageSequence.readString();
    this.domain = messageSequence.readString();
    this.resVersion = messageSequence.readString();
    this.resCompatibleVersion = messageSequence.readString();
    this.errorCode = messageSequence.readInt();
    this.errorMessage = messageSequence.readString();
    HiAiLog.info(TAG, "ModelManagerResultInfo unmarshalling.");
    return true;
  }
}