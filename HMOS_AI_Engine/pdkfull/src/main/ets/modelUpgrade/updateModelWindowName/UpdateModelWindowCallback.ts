/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import { rpc } from '@kit.IPCKit';
import QueryResultInfo from './ModelQueryResultInfo';

/**
 * This interface defines various callback functions during model query.
 *
 * @interface
 * <AUTHOR>
 */
export interface IModelQueryCallback extends rpc.IRemoteBroker{
  /**
   * the callback of query.
   *
   * @param{ boolean } isAllSuccess - Indicates whether the query is successful.
   * @param{ string } resultMessage - Indicates the result message.
   * @param{ Array<QueryResultInfo> } queryResult - Indicates the query result.
   */
  onResult(isAllSuccess: boolean, resultMessage: string, queryResult: Array<QueryResultInfo>);
}