/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import HiAiLog from '../utils/log/HiAiLog';
import { IModelManagerCallback } from './IModelManagerCallback';
import { ModelManagerCallbackProxy } from './ModelManagerCallbackProxy'
import { ModelDownloadCallbackType } from '../modelDownload/ModelDownloadConstant';
import rpc from '@ohos.rpc';
import ResultInfo from './ModelManagerResultInfo';


const TAG = "ModelManagerCallbackStub";

/**
 * This abstract class serves as the implementation of IModelDownloadCallback and is used to receive callback data on the client.
 *
 * <AUTHOR>
 */
export default abstract class ModelManagerCallbackStub extends rpc.RemoteObject implements IModelManagerCallback {

    /**
     * The constructor.
     *
     * @param des the descriptor
     */
    constructor(des: string) {
        super(des)
    }

    /**
     * @Override
     */
    asObject(): rpc.IRemoteObject {
        return this;
    }


    /**
     * Get the proxy object.
     *
     * @param { rpc.IRemoteObject } object - the remote object.
     * @return { IModelManagerCallback } the proxy object
     */
    public static asInterface(object: rpc.IRemoteObject): IModelManagerCallback {
        return new ModelManagerCallbackProxy(object);
    }

    /**
     * @Override
     */
    onRemoteMessageRequest(code: number, data: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): boolean {
        HiAiLog.info(TAG, "ModelManagerCallbackStub onRemoteRequest code is:" + code);
        switch (code) {
            case ModelDownloadCallbackType.MODEL_MANAGER_ON_RESULT: {
                HiAiLog.info(TAG, `ModelManagerCallbackStub onRemoteMessageRequest onResult`);
                let isAllSuccess: boolean = data.readBoolean();
                let resultMessage: string = data.readString();
                let resultList: Array<ResultInfo> = new Array();
                let resultListLength: number = data.readInt();
                for(let i = 1; i <= resultListLength; i++) {
                    let resultInfo: ResultInfo = new ResultInfo();
                    data.readParcelable(resultInfo);
                    resultList.push(resultInfo);
                }
                this.onResult(isAllSuccess, resultMessage, resultList);
                return true;
            }
            default:
                return false;
        }
    }

    getMessageCode(): void {
        throw new Error('ModelManagerCallbackStub Method not implemented.');
    }

    /**
     * @Override
     */
    onResult(isAllSuccess: boolean, resultMessage: string, resultList: Array<ResultInfo>): void {
        HiAiLog.info(TAG, "ModelManagerCallbackStub onResult; isAllSuccess: " + isAllSuccess + ', resultMessage: ' + resultMessage + ', resultList:' + JSON.stringify(resultList));
    }
}