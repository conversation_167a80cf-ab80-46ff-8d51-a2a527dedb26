/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import { rpc } from '@kit.IPCKit';
import ResultInfo from './ModelManagerResultInfo';

/**
 * This interface defines various callback functions of delete, subscribe or unsubscribe.
 *
 * @interface
 * <AUTHOR>
 */
export interface IModelManagerCallback extends rpc.IRemoteBroker{
  /**
   * the callback of result.
   *
   * @param{ boolean } isAllSuccess - Indicates whether the query is successful.
   * @param{ string } resultMessage - Indicates the result message.
   * @param{ Array<ResultInfo> } resultList - Indicates the result.
   */
  onResult(isAllSuccess: boolean, resultMessage: string, resultList: Array<ResultInfo>);
}