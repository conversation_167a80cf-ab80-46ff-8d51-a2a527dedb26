/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import rpc from '@ohos.rpc';
import { Capability } from '../interfaces/FrameworkCapability';
import HiAiLog from '../utils/log/HiAiLog';
import ObjectUtil from '../utils/ObjectUtil'
import ModelInfo from '../modelDownload/ModelDownloadInfo';
import { common } from '@kit.AbilityKit';
import HiAIServiceAbilityBridge from '../HiAIServiceAbilityBridge';
import { ModelDomain, ModelDownloadType, NetworkType } from '../modelDownload/ModelDownloadConstant'
import { NetWorkUtil } from '../utils/NetWorkUtil'
import { IModelDownloadCallback} from '../modelDownload/IModelDownloadCallback';
import { ModelDownloadResultCode } from '../utils/ResCode';
import { IModelManagerCallback } from './IModelManagerCallback';
import { IModelQueryCallback } from './modelQuery/IModelQueryCallback';
import { bundleManager } from '@kit.AbilityKit';
import DownloadConfig from '../modelDownload/ModelDownloadConfig';

const TAG: string = "ModelUpgradeManager";
type commonContext = common.UIAbilityContext|common.ServiceExtensionContext;

/**
 * This is a model upgrade management class.
 * It provides the delete, query, subscribe and unsubscribe interface. You can invoke the interfaces to upgrade and manager models.
 *
 * <AUTHOR>
 */
export default class ModelUpgradeManager {
    context: commonContext;

    /**
     * The constructor of this class.
     *
     */
    constructor(context: commonContext) {
        if (ObjectUtil.isNullObject(context)) {
            HiAiLog.info(TAG, "context is null.");
            return;
        } else {
            this.context = context;
        }
    }

    /**
     * Get an instance object of this class.
     *
     * @param { commonContext } context - the ability context.
     * @returns { ModelUpgradeManager } the instance of ModelUpgradeManager.
     */
    static getInstance(context: commonContext): ModelUpgradeManager {
        if(!globalThis.ModelUpgradeManager) {
            globalThis.ModelUpgradeManager = new ModelUpgradeManager(context);
        }
        return globalThis.ModelUpgradeManager;
    }

    /**
     * This method provides the subscribe function.
     *
     * Note:
     * 1. Only OTA platform is used.
     *
     * @param { Array<ModelInfo> } subscribeInfoList - the subscribe model information.
     * @param { IModelDownloadCallback } subscribeCallback - the subscribe callback.
     */
    public subscribe(subscribeInfoList: Array<ModelInfo>, subscribeCallback: IModelManagerCallback): void{
        HiAiLog.info(TAG, "start to subscribe");
        if(!subscribeCallback) {
            HiAiLog.error(TAG, `downloadCallback is null`);
            return;
        }
        let length: number = subscribeInfoList.length;
        if(length === 0) {
            HiAiLog.info(TAG, "length is 0");
            subscribeCallback.onResult(false, "Incorrect parameter specifications of subscribeInfoList.", null);
            return;
        }
        let reportRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        let modelDownloadType: number = ModelDownloadType.MODEL_SUBSCRIBE;
        let modelDomain: string = ModelDomain.MODEL_TYPE_AI;
        reportRequest.writeInt(modelDownloadType);
        reportRequest.writeString(modelDomain);
        reportRequest.writeInt(length);
        for(let subscribeInfo of subscribeInfoList) {
            reportRequest.writeParcelable(subscribeInfo);
        }
        reportRequest.writeRemoteObject(subscribeCallback.asObject());
        let reportReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.MODEL_DOWNLOAD_CAPABILITY, reportRequest, reportReply);
        HiAiLog.info(TAG, "subscribe start success");
    }

    /**
     * This method provides the unSubscribe function.
     *
     * Note:
     * 1. Only OTA platform is used.
     *
     * @param { Array<ModelInfo> } unSubscribeInfoList - the unSubscribe model information.
     * @param { IModelDownloadCallback } unSubscribeCallback - the unSubscribe callback.
     */
    public unSubscribe(unSubscribeInfoList: Array<ModelInfo>, unSubscribeCallback: IModelManagerCallback): void{
        HiAiLog.info(TAG, "start to unSubscribe");
        if(!unSubscribeCallback) {
            HiAiLog.error(TAG, `downloadCallback is null`);
            return;
        }
        let length: number = unSubscribeInfoList.length;
        if(length === 0) {
            HiAiLog.info(TAG, "length is 0");
            unSubscribeCallback.onResult(false, "Incorrect parameter specifications of unSubscribeInfoList.", null);
            return;
        }
        let reportRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        let modelDownloadType: number = ModelDownloadType.MODEL_UNSUBSCRIBE;
        let modelDomain: string = ModelDomain.MODEL_TYPE_AI;
        reportRequest.writeInt(modelDownloadType);
        reportRequest.writeString(modelDomain);
        reportRequest.writeInt(length);
        for(let unSubscribeInfo of unSubscribeInfoList) {
            reportRequest.writeParcelable(unSubscribeInfo);
        }
        reportRequest.writeRemoteObject(unSubscribeCallback.asObject());
        let reportReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.MODEL_DOWNLOAD_CAPABILITY, reportRequest, reportReply);
        HiAiLog.info(TAG, "unSubscribe start success");
    }

    /**
     * This method provides the upgrade function in background.
     * We will compare the local version and query the cloud version. If there is a version update, background download will be triggered.
     *
     * Note:
     * 1. Only OTA platform is used.
     * 2. This method only allows the caller of the plugin to be a system app, and only supported with WLAN.
     * 3. The plugin must ensure security and privacy.
     *
     * @param { Array<ModelInfo> } upgradeInfoList - the upgrade model information.
     * @param { IModelDownloadCallback } upgradeCallback - the upgrade callback.
     */
    public upgrade(upgradeInfoList: Array<ModelInfo>, upgradeCallback: IModelDownloadCallback,upgradeConfig?:DownloadConfig): void{
        HiAiLog.info(TAG, "start to upgrade");
        if(!upgradeCallback) {
            HiAiLog.error(TAG, `upgradeCallback is null`);
            return;
        }
        let downloadInfo: ModelInfo = null;
        if(upgradeInfoList.length === 1) {
            HiAiLog.info(TAG, "length is 1");
            downloadInfo = upgradeInfoList[0];
        } else {
            upgradeCallback.onError("", ModelDownloadResultCode.PARAMATER_INVALID,
                "Incorrect parameter specifications of upgradeInfoList.");
            return;
        }

        if(!this.checkParamater(downloadInfo, upgradeCallback)){
            return;
        }

        if(!this.isSystemApplication()) {
            upgradeCallback.onError(downloadInfo.resId, ModelDownloadResultCode.NO_SYSTEM_APPLICATION, "the caller is not a system application.");
            return;
        }

        if(upgradeConfig){
            if(upgradeConfig.networkType!== NetworkType.WLAN && upgradeConfig.networkType!== NetworkType.ANY){
                upgradeCallback.onError(downloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, "download config network is invalid");
                return;
            }
            if(!this.checkNetworkType(upgradeConfig.networkType ?? NetworkType.WLAN, downloadInfo.resId, upgradeCallback)) {
                return;
            }
        }else {
            if(!this.checkNetworkType(NetworkType.WLAN, downloadInfo.resId, upgradeCallback)) {
                return;
            }
        }

        let reportRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        let modelDownloadType: number = ModelDownloadType.MODEL_UPGRADE;
        let modelDomain: string = ModelDomain.MODEL_TYPE_AI;

        reportRequest.writeInt(modelDownloadType);
        reportRequest.writeParcelable(downloadInfo);
        reportRequest.writeRemoteObject(upgradeCallback.asObject());
        reportRequest.writeString(modelDomain);
        if(upgradeConfig){
            reportRequest.writeParcelable(upgradeConfig)
        }
        let reportReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.MODEL_DOWNLOAD_CAPABILITY, reportRequest, reportReply);
        HiAiLog.info(TAG, "upgrade start success");
    }

    /**
     * This method provides the delete local model function.
     *
     * @param { Array<ModelInfo> } deleteInfoList - the delete model information.
     * @param { IModelDownloadCallback } deleteCallback - the delete callback.
     */
    public deleteLocalModel(deleteInfoList: Array<ModelInfo>, deleteCallback: IModelManagerCallback): void{
        HiAiLog.info(TAG, "start to deleteLocalModel");
        if(!deleteCallback) {
            HiAiLog.error(TAG, `deleteCallback is null`);
            return;
        }
        let length: number = deleteInfoList.length;
        if(length === 0) {
            HiAiLog.info(TAG, "length is 0");
            deleteCallback.onResult(false, "Incorrect parameter specifications of deleteInfoList.", null);
            return;
        }
        let reportRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        let modelDownloadType: number = ModelDownloadType.MODEL_DELETE;
        let modelDomain: string = ModelDomain.MODEL_TYPE_AI;
        reportRequest.writeInt(modelDownloadType);
        reportRequest.writeString(modelDomain);
        reportRequest.writeInt(length);
        for(let deleteInfo of deleteInfoList) {
            reportRequest.writeParcelable(deleteInfo);
        }
        reportRequest.writeRemoteObject(deleteCallback.asObject());
        let reportReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.MODEL_DOWNLOAD_CAPABILITY, reportRequest, reportReply);
        HiAiLog.info(TAG, "deleteLocalModel start success");
    }

    /**
     * This method provides the delete local model function.
     *
     * @param { Array<ModelInfo> } queryInfoList - the query model information.
     * @param { IModelQueryCallback } queryCallback - the query callback.
     */
    public queryLocalModel(queryInfoList: Array<ModelInfo>, queryCallback: IModelQueryCallback): void{
        HiAiLog.info(TAG, "start to queryLocalModel");
        if(!queryCallback) {
            HiAiLog.error(TAG, `queryCallback is null`);
            return;
        }
        let length: number = queryInfoList.length;
        if(length === 0) {
            HiAiLog.info(TAG, "length is 0");
            queryCallback.onResult(false, "Incorrect parameter specifications of queryInfoList.", null);
            return;
        }

        let reportRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        let modelDownloadType: number = ModelDownloadType.MODEL_QUERY_LOCAL;
        let modelDomain: string = ModelDomain.MODEL_TYPE_AI;
        reportRequest.writeInt(modelDownloadType);
        reportRequest.writeString(modelDomain);
        reportRequest.writeInt(length);
        for(let queryInfo of queryInfoList) {
            reportRequest.writeParcelable(queryInfo);
        }
        reportRequest.writeRemoteObject(queryCallback.asObject());
        let reportReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.MODEL_DOWNLOAD_CAPABILITY, reportRequest, reportReply);
        HiAiLog.info(TAG, "queryLocalModel start success");
    }

    /**
     * Check whether the parameter is empty.
     *
     * @param { boolean } The value of false indicates that the parameter is empty, and true indicates that the parameter is not empty.
     */
    private checkParamater( downloadInfo: ModelInfo, downloadCallback: IModelDownloadCallback): boolean{
        if (!downloadCallback) {
            HiAiLog.error(TAG, `downloadCallback is null`);
            return false;
        }
        if(ObjectUtil.isEmptyText(downloadInfo.resId) || ObjectUtil.isEmptyText(downloadInfo.domain)) {
            HiAiLog.error(TAG, "downloadInfo is empty");
            downloadCallback.onError(downloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, "downloadInfo is empty");
            return false;
        }
        return true;
    }

    /**
     * Check the caller whether is a system application.
     *
     * @returns { boolean } the value of true indicates that it's a system application.
     */
    private isSystemApplication(): boolean{
        HiAiLog.info(TAG, `isSystemApplication  start`);
        let uid: number = rpc.IPCSkeleton.getCallingUid();
        let bundleName: string = bundleManager.getBundleNameByUidSync(uid);
        HiAiLog.info(TAG, "the caller bundleName is: "+ bundleName);
        let applicationInfo: bundleManager.ApplicationInfo
            = bundleManager.getApplicationInfoSync(bundleName, bundleManager.ApplicationFlag.GET_APPLICATION_INFO_DEFAULT);
        return applicationInfo? applicationInfo.systemApp: false;
    }

    /**
     * Check if the current network type matches the required network type for download
     *
     * @param { number } networkType - The required network type (0 for WLAN only, 1 for any network)
     * @param { string } resId - The model ID for error reporting
     * @param { IModelDownloadCallback } downloadCallback - The download callback
     * @returns { boolean } true if network type is valid, false otherwise
     */
    private checkNetworkType(networkType: number, resId: string, downloadCallback: IModelDownloadCallback): boolean {
        if (!NetWorkUtil.isConnectNetwork()) {
            downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "No network connection available.");
            return false;
        }

        if (networkType === NetworkType.WLAN) {
            if (!NetWorkUtil.isWifiNetwork()) {
                downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "WiFi is not connected. Only WiFi is allowed for this download.");
                return false;
            }
        } else if (networkType === NetworkType.ANY) {
            if (!NetWorkUtil.isWifiNetwork() && !NetWorkUtil.isCellularNetwork()) {
                downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "Neither WiFi nor cellular network is connected.");
                return false;
            }
        }
        return true;
    }
}