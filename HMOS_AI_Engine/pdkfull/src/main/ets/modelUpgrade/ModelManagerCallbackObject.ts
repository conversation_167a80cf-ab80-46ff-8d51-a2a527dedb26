/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import HiAiLog from '../utils/log/HiAiLog';
import ModelManagerCallbackStub from './ModelManagerCallbackStub';
import ResultInfo from './ModelManagerResultInfo';

const TAG = "ModelManagerCallbackObject";

/**
 * This class inherits the abstract class ModelManagerCallbackStub.
 * You can refer to this class to complete the operation after you receive the callback.
 *
 * <AUTHOR>
 */
export class ModelManagerCallbackObject extends ModelManagerCallbackStub {
    /**
     * @override
     */
    onResult(isAllSuccess: boolean, resultMessage: string, resultList: Array<ResultInfo>): void {
        HiAiLog.info(TAG, "ModelManagerCallbackObject onResult; isAllSuccess: " + isAllSuccess + ', resultMessage: ' + resultMessage + ', resultList:' + JSON.stringify(resultList));
        /**
         * Here, you will get the result of model deletion, subscription, or unsubscription.
         * You can perform business logic processing based on the result.
         */
    }
}