/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import rpc from '@ohos.rpc';
import ModelDownloadInfo from '../../modelDownload/ModelDownloadInfo';
import HiAiLog from '../../utils/log/HiAiLog';

const TAG: string = "QueryResultInfo";

/**
 * Indicates the query information of model.
 *
 * <AUTHOR>
 */
export default class QueryResultInfo implements rpc.Parcelable {
    /**
     * Indicates the model ID.
     * @type { string }
     */
    resId: string;
    /**
     * Indicates the domain of model.
     * @type { string }
     */
    domain: string;
    /**
     * Indicates the current version of the model.
     *
     * @type { string }
     */
    resVersion: string;
    /**
     * Indicates the compatible versions of model with plugin.
     *
     * @type { string }
     */
    resCompatibleVersion: string;
    /**
     * Indicates the model path in local.
     *
     * @type { string }
     */
    sourcePath: string;
    /**
     * When an error occurs, this field returns the error code of the operation on the model.
     *
     * @type { string }
     */
    errorCode: number;

    /**
     * When an error occurs, this field returns the operation error description of the model.
     *
     * @type { string }
     */
    errorMessage: string;

    /**
     * @override
     */
    marshalling(messageSequence: rpc.MessageSequence): boolean {
        messageSequence.writeString(this.resId);
        messageSequence.writeString(this.domain);
        messageSequence.writeString(this.resVersion);
        messageSequence.writeString(this.resCompatibleVersion);
        messageSequence.writeString(this.sourcePath);
        messageSequence.writeInt(this.errorCode);
        messageSequence.writeString(this.errorMessage);
        HiAiLog.info(TAG, "QueryResultInfo marshalling.");
        return true;
    }

    /**
     * @override
     */
    unmarshalling(messageSequence: rpc.MessageSequence): boolean {
        this.resId = messageSequence.readString();
        this.domain = messageSequence.readString();
        this.resVersion = messageSequence.readString();
        this.resCompatibleVersion = messageSequence.readString();
        this.sourcePath = messageSequence.readString();
        this.errorCode = messageSequence.readInt();
        this.errorMessage = messageSequence.readString();
        HiAiLog.info(TAG, "QueryResultInfo unmarshalling.");
        return true;
    }
}