/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import <PERSON><PERSON><PERSON>Log from '../../utils/log/HiAiLog';
import { ModelDownloadCallbackType } from '../../modelDownload/ModelDownloadConstant';
import rpc from '@ohos.rpc';
import { IModelQueryCallback } from './IModelQueryCallback';
import QueryResultInfo from './ModelQueryResultInfo';
import { ModelQueryCallbackProxy } from './ModelQueryCallbackProxy';


const TAG = "ModelQueryCallbackStub";

/**
 * This abstract class serves as the implementation of IModelQueryCallback and is used to receive callback data on the client.
 *
 * <AUTHOR>
 */
export default abstract class ModelQueryCallbackStub extends rpc.RemoteObject implements IModelQueryCallback {

    /**
     * The constructor.
     *
     * @param des the descriptor
     */
    constructor(des: string) {
        super(des)
    }

    /**
     * @Override
     */
    asObject(): rpc.IRemoteObject {
        return this;
    }

    /**
     * Get the proxy object.
     *
     * @param { rpc.IRemoteObject } object - the remote object.
     * @return { IModelManagerCallback } the proxy object
     */
    public static asInterface(object: rpc.IRemoteObject): IModelQueryCallback {
        return new ModelQueryCallbackProxy(object);
    }

    /**
     * @Override
     */
    onRemoteMessageRequest(code: number, data: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): boolean {
        HiAiLog.info(TAG, "ModelQueryCallbackStub onRemoteRequest code is:" + code);
        switch (code) {
            case ModelDownloadCallbackType.MODEL_QUERY_ON_RESULT: {
                HiAiLog.info(TAG, `ModelQueryCallbackStub onRemoteMessageRequest onResult`);
                let isAllSuccess: boolean = data.readBoolean();
                let resultMessage: string = data.readString();
                let queryResultList: Array<QueryResultInfo> = new Array();
                let resultListLength: number = data.readInt();
                for(let i = 1; i <= resultListLength; i++) {
                    let queryResultInfo: QueryResultInfo = new QueryResultInfo();
                    data.readParcelable(queryResultInfo);
                    queryResultList.push(queryResultInfo);
                }
                this.onResult(isAllSuccess, resultMessage, queryResultList);
                return true;
            }
            default:
                return false;
        }
    }

    getMessageCode(): void {
        throw new Error('ModelQueryCallbackStub Method not implemented.');
    }

    /**
     * @Override
     */
    onResult(isAllSuccess: boolean, resultMessage: string, queryResult: Array<QueryResultInfo>): void {
        HiAiLog.info(TAG, "ModelQueryCallbackStub onResult; isAllSuccess: " + isAllSuccess + ', resultMessage'+ resultMessage +', queryResult' + JSON.stringify(queryResult));
    }
}