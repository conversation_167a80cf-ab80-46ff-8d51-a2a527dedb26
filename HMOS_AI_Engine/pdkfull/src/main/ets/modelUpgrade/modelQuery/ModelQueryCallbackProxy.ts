/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import HiAiLog from '../../utils/log/HiAiLog';
import rpc from '@ohos.rpc';
import { IModelQueryCallback } from './IModelQueryCallback';
import QueryResultInfo from './ModelQueryResultInfo';
import { ModelDownloadCallbackType } from '../../modelDownload/ModelDownloadConstant';

const TAG = "ModelQueryCallbackProxy";

/**
 *  This class is an implementation of IModelQueryCallback and is used to process data on the server.
 *
 * * <AUTHOR>
 */
export class ModelQueryCallbackProxy implements IModelQueryCallback {
  private remote: rpc.IRemoteObject;

  /*
   * constructor
   *
   * @param { remote } Remote Object
   */
  public constructor(remote: rpc.IRemoteObject) {
    HiAiLog.info(TAG, "ModelQueryCallbackProxy constructor");
    this.remote = remote;
  }

  /*
   * @Override
   *
   */
  public asObject(): rpc.IRemoteObject {
    HiAiLog.info(TAG, "ModelQueryCallbackProxy asObject");
    return this.remote;
  }

  /**
   * @Override
   */
  onResult(isAllSuccess: boolean, resultMessage: string, queryResult: Array<QueryResultInfo>): void {
    HiAiLog.info(TAG, "ModelQueryCallbackProxy onResult");
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeBoolean(isAllSuccess);
    requestData.writeString(resultMessage);
    let length: number = 0;
    if (queryResult !== null && queryResult !== undefined) {
      length = queryResult.length;
    }
    requestData.writeInt(length);
    if (length !== 0) {
      for (let queryInfo of queryResult) {
        requestData.writeParcelable(queryInfo);
      }
    }
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    this.remote.sendMessageRequest(ModelDownloadCallbackType.MODEL_QUERY_ON_RESULT, requestData, reply, option)
      .then(() => {
        reply.readException();
        requestData.reclaim();
        reply.reclaim();
        HiAiLog.info(TAG, " onResult sendRequest success ");
      }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(TAG, " onResult error is:" + JSON.stringify(err));
    });
  }
}