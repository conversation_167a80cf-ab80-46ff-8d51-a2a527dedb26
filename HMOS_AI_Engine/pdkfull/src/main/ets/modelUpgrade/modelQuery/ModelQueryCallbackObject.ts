/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import HiAiLog from '../../utils/log/HiAiLog';
import ModelQueryCallbackStub from './ModelQueryCallbackStub';
import QueryResultInfo from './ModelQueryResultInfo';

const TAG = "ModelQueryCallbackObject";

/**
 * This class inherits the abstract class ModelQueryCallbackStub.
 * You can refer to this class to complete the operation after you receive the callback.
 *
 * <AUTHOR>
 */
export class ModelQueryCallbackObject extends ModelQueryCallbackStub {
    /**
     * @override
     */
    onResult(isAllSuccess: boolean, resultMessage: string, queryResult: Array<QueryResultInfo>): void {
        HiAiLog.info(TAG, "ModelQueryCallbackObject onResult; isAllSuccess: " + isAllSuccess + ', resultMessage'+ resultMessage +', queryResult' + JSON.stringify(queryResult));
    }
}