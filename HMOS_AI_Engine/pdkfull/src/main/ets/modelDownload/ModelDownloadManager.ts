/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import rpc from '@ohos.rpc';
import { Capability } from '../interfaces/FrameworkCapability';
import HiAiLog from '../utils/log/HiAiLog';
import ObjectUtil from '../utils/ObjectUtil'
import DownloadInfo from './ModelDownloadInfo';
import DownloadConfig from './ModelDownloadConfig';
import { common } from '@kit.AbilityKit';
import { IModelDownloadCallback} from './IModelDownloadCallback';
import HiAIServiceAbilityBridge from '../HiAIServiceAbilityBridge';
import { ModelDownloadResultCode } from '../utils/ResCode';
import {  ModelDownloadType, ModelType, NetworkType } from './ModelDownloadConstant'
import { NetWorkUtil } from '../utils/NetWorkUtil'
import { bundleManager } from '@kit.AbilityKit';
import SystemParameterHelper from '../platform/oswrapper/SystemParameterHelper'

const TAG: string = "ModelDownloadManager";
type commonContext = common.UIAbilityContext|common.ServiceExtensionContext;

/**
 * This is a model download management class.
 * It provides the foreground download and background download interface. You can invoke the interfaces to download models.
 *
 * <AUTHOR>
 */
export default class ModelDownloadManager {
    context: commonContext;

    /**
     * The constructor of this class.
     *
     * @param { commonContext } the ability context.
     */
    constructor(context: commonContext) {
        if (ObjectUtil.isNullObject(context)) {
            HiAiLog.info(TAG, "context is null.");
            return;
        } else {
            this.context = context;
        }
    }

    /**
     * Get an instance object of this class.
     *
     * @param { commonContext } the ability context.
     * @returns { ModelDownloadManager } the instance of ModelDownloadManager.
     */
    static getInstance(context: commonContext): ModelDownloadManager {
        if(!globalThis.DownloadManager) {
            globalThis.DownloadManager = new ModelDownloadManager(context);
        }
        return globalThis.DownloadManager;
    }

    /**
     * This method provides the foreground download function.
     * When this method is invoked, a dialog box is displayed for asking the user to authorize the access traffic.
     *
     * Note:
     * 1. The OTA platform is preferentially used for model download. If the OTA platform does not have a corresponding model, the IDS platform is used for model download.
     * 2. This method only allows the caller of the plugin to be a Foreground application.
     * 3. You need to apply for permission:ohos.permission.START_ABILITIES_FROM_BACKGROUND.
     *
     * @param { Array<DownloadInfo> } downloadInfoList - the model information.
     * @param { string } moduleName - the model name show in dialog.
     * @param { IModelDownloadCallback } downloadCallback - the download callback.
     */
    public ForegroundDownload(downloadInfoList: Array<DownloadInfo>, moduleName: string, downloadCallback: IModelDownloadCallback,downloadConfig?:DownloadConfig): void{
        HiAiLog.info(TAG, "start to ForegroundDownload");
        if(!downloadCallback) {
            HiAiLog.error(TAG, `downloadCallback is null`);
            return;
        }
        let downloadInfo: DownloadInfo | null = null;
        HiAiLog.info(TAG, "start to ForegroundDownload downloadInfoList.length  " + downloadInfoList.length );

        if(downloadInfoList.length === 1) {
            downloadInfo = downloadInfoList[0];
        } else {
            downloadCallback.onError('', ModelDownloadResultCode.PARAMATER_INVALID,
                "Incorrect parameter specifications of downloadInfoList.");
            return;
        }

        if(!this.checkParamater(downloadInfo, downloadCallback)){
            return;
        }

        let moduleType = ModelType.MODEL_COMPONENT
        if(this.isLanguageType(downloadInfo.resId)) {
            moduleType = ModelType.MODEL_LANGUAGE_PACKAGE;
        }

        if(ObjectUtil.isEmptyText(moduleName)) {
            downloadCallback.onError(downloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, "moduleName is empty.");
            return;
        }

        if(downloadConfig){
            if(downloadConfig.networkType!== NetworkType.WLAN && downloadConfig.networkType!== NetworkType.ANY){
                downloadCallback.onError(downloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, "download config network is invalid");
                return;
            }
            if(!this.checkNetworkType(downloadConfig.networkType ?? NetworkType.WLAN, downloadInfo.resId, downloadCallback)) {
                return;
            }
        }else {
            if (!NetWorkUtil.isConnectNetwork() || !NetWorkUtil.isAvailableNetwork()) {
                downloadCallback.onError(downloadInfo.resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "No network connection available.");
                return;
            }
        }

        let deviceType: string = SystemParameterHelper.getDeviceType();

        let reportRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        let modelDownloadType: number = ModelDownloadType.MODEL_FORE_DOWNLOAD;
        reportRequest.writeInt(modelDownloadType);
        reportRequest.writeParcelable(downloadInfo);
        reportRequest.writeRemoteObject(downloadCallback.asObject())
        reportRequest.writeString(moduleName);
        reportRequest.writeInt(moduleType);
        reportRequest.writeString(deviceType);
        if (downloadConfig) {
            reportRequest.writeParcelable(downloadConfig);
        }
        let reportReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.MODEL_DOWNLOAD_CAPABILITY, reportRequest, reportReply);
        HiAiLog.info(TAG, "ForegroundDownload start success");
    }

    /**
     * This method provides the background download function.
     *
     * Note:
     * 1. The OTA platform is preferentially used for model download. If the OTA platform does not have a corresponding model, the IDS platform is used for model download.
     * 2. This method only allows the caller of the plugin to be a system app, and only supported with WLAN.
     * 3. The plugin must ensure security and privacy.
     *
     * @param { Array<DownloadInfo> } downloadInfoList - the download model information.
     * @param { IModelDownloadCallback } downloadCallback - the download callback.
     */
    public BackgroundDownload(downloadInfoList: Array<DownloadInfo>, downloadCallback: IModelDownloadCallback,downloadConfig?:DownloadConfig): void{
        HiAiLog.info(TAG, "start to BackgroundDownload");
        if(!downloadCallback) {
            HiAiLog.error(TAG, `downloadCallback is null`);
            return;
        }
        let downloadInfo: DownloadInfo | null = null;
        if(downloadInfoList.length === 1) {
            HiAiLog.info(TAG, "length is 1");
            downloadInfo = downloadInfoList[0];
        } else {
            downloadCallback.onError("", ModelDownloadResultCode.PARAMATER_INVALID,
                "Incorrect parameter specifications of downloadInfoList.");
            return;
        }

        if(!this.checkParamater(downloadInfo, downloadCallback)){
            return;
        }

        if(!this.isSystemApplication()) {
            downloadCallback.onError(downloadInfo.resId, ModelDownloadResultCode.NO_SYSTEM_APPLICATION, "the caller is not a system application.");
            return;
        }

        if(downloadConfig){
            if(downloadConfig.networkType!== NetworkType.WLAN && downloadConfig.networkType!== NetworkType.ANY){
                downloadCallback.onError(downloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, "download config network is invalid");
                return;
            }
            if(!this.checkNetworkType(downloadConfig.networkType ?? NetworkType.WLAN, downloadInfo.resId, downloadCallback)) {
                return;
            }
        }else {
            if(!this.checkNetworkType(NetworkType.WLAN, downloadInfo.resId, downloadCallback)) {
                return;
            }
        }

        let reportRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        let modelDownloadType: number = ModelDownloadType.MODEL_BACK_DOWNLOAD;
        reportRequest.writeInt(modelDownloadType);
        reportRequest.writeParcelable(downloadInfo);
        reportRequest.writeRemoteObject(downloadCallback.asObject())
        if (downloadConfig) {
            reportRequest.writeParcelable(downloadConfig);
        }
        let reportReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.MODEL_DOWNLOAD_CAPABILITY, reportRequest, reportReply);
        HiAiLog.info(TAG, "BackgroundDownload start success");
    }

    /**
     * Check whether the parameter is empty.
     *
     * @param { DownloadInfo } downloadInfoList - the model information.
     * @param { IModelDownloadCallback } downloadCallback - the download callback.
     * @returns { boolean } the value of false indicates that the parameter is empty, and true indicates that the parameter is not empty.
     */
    private checkParamater( downloadInfo: DownloadInfo, downloadCallback: IModelDownloadCallback): boolean{
        if (!downloadCallback) {
            HiAiLog.error(TAG, `downloadCallback is null`);
            return false;
        }
        if(ObjectUtil.isEmptyText(downloadInfo.resId) || ObjectUtil.isEmptyText(downloadInfo.domain)) {
            HiAiLog.error(TAG, "downloadInfo is empty");
            downloadCallback.onError(downloadInfo.resId, ModelDownloadResultCode.PARAMATER_INVALID, "downloadInfo is empty");
            return false;
        }
        return true;
    }

    /**
     * Check the caller whether is a system application.
     *
     * @param { string } resId - the model ID.
     * @returns { boolean } the value of true indicates that it's a language package, and false indicates that it's a resource package.
     */
    private isLanguageType(resId: string): boolean{
        let domain: string = ''
        const regex = /^([^_]+)/;
        const match = resId.match(regex);
        if (match && match.length > 1) {
            domain = match[1];
            HiAiLog.info(TAG, "domain is: "+ domain);
        }
        if(domain.toLowerCase() === 'tts' || domain.toLowerCase() === 'translation' || domain.toLowerCase() === 'asr' || domain.toLowerCase() === 'nlu') {
            return true;
        }
        return false;
    }

    /**
     * Check the caller whether is a system application.
     *
     * @returns { boolean } the value of true indicates that it's a system application.
     */
    private isSystemApplication(): boolean{
        HiAiLog.info(TAG, `isSystemApplication  start`);
        let uid: number = rpc.IPCSkeleton.getCallingUid();
        let bundleName: string = bundleManager.getBundleNameByUidSync(uid);
        HiAiLog.info(TAG, "the caller bundleName is: "+ bundleName);
        let applicationInfo: bundleManager.ApplicationInfo
            = bundleManager.getApplicationInfoSync(bundleName, bundleManager.ApplicationFlag.GET_APPLICATION_INFO_DEFAULT);
        return applicationInfo? applicationInfo.systemApp: false;
    }

    /**
     * Check if the current network type matches the required network type for download
     * 
     * @param { number } networkType - The required network type (0 for WLAN only, 1 for any network)
     * @param { string } resId - The model ID for error reporting
     * @param { IModelDownloadCallback } downloadCallback - The download callback
     * @returns { boolean } true if network type is valid, false otherwise
     */
    private checkNetworkType(networkType: number, resId: string, downloadCallback: IModelDownloadCallback): boolean {
        if (!NetWorkUtil.isConnectNetwork() || !NetWorkUtil.isAvailableNetwork()) {
            downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "No network connection available.");
            return false;
        }

        if (networkType === NetworkType.WLAN) {
            if (!NetWorkUtil.isWifiNetwork()) {
                downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "WiFi is not connected. Only WiFi is allowed for this download.");
                return false;
            }
        } else if (networkType === NetworkType.ANY) {
            if (!NetWorkUtil.isWifiNetwork() && !NetWorkUtil.isCellularNetwork()) {
                downloadCallback.onError(resId, ModelDownloadResultCode.NO_NETWORK_STATUS, "Neither WiFi nor cellular network is connected.");
                return false;
            }
        }
        return true;
    }
}