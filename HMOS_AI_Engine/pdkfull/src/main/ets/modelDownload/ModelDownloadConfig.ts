/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import rpc from '@ohos.rpc';
import HiAiLog from '../utils/log/HiAiLog';

const TAG: string = "ModelDownloadConfig";

/**
 * Indicates the model download information
 *
 * <AUTHOR>
 */
export default class ModelDownloadConfig implements rpc.Parcelable {
  /**
   * Indicates the network type when download.
   *
   * @type { number }
   */
  networkType?: number = 0;

  /**
   * @override
   */
  marshalling(messageSequence: rpc.MessageSequence): boolean {
    messageSequence.writeInt(this.networkType);
    HiAiLog.info(TAG, "DownloadConfig marshalling.");
    return true;
  }

  /**
   * @override
   */
  unmarshalling(messageSequence: rpc.MessageSequence): boolean {
    this.networkType = messageSequence.readInt();
    HiAiLog.info(TAG, "DownloadConfig unmarshalling.");
    return true;
  }

}