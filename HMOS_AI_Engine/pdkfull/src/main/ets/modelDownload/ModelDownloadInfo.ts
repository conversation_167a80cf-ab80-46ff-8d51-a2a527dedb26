/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */
import rpc from '@ohos.rpc';
import HiAiLog from '../utils/log/HiAiLog';

const TAG: string = "ModelDownloadInfo";

/**
 * Indicates the model download information
 *
 * <AUTHOR>
 */
export default class ModelDownloadInfo implements rpc.Parcelable {
    /**
     * Indicates the model ID.
     * @type { string }
     */
    resId: string;
    /**
     * Indicates the domain of model.
     * @type { string }
     */
    domain: string;
    /**
     * Indicates the current version of the model.
     *
     * The resVersion is used in the following situations:
     * 1. When downloading the model from the IDS platform, note that the resVersion must be earlier than the IDS cloud version.
     * 2. When subscribing the model for updating, the silent upgrade is triggered when the version on the cloud is later than the resVersion.
     * 3. When querying the model information of a specific version in the local.
     * 4. When deleting the model and the model information of a specific version in the local.
     * @type { string }
     */
    resVersion?: string = '';
    /**
     * Indicates the compatible versions of model with plugin.
     *
     * The resCompatibleVersion is used when there are compatibility changes to the model.
     * @type { string }
     */
    resCompatibleVersion?: string = '';

    /**
     * @override
     */
    marshalling(messageSequence: rpc.MessageSequence): boolean {
        HiAiLog.info(TAG, `Marshalling resVersion: ${this.resVersion}`);
        messageSequence.writeString(this.resId);
        messageSequence.writeString(this.domain);
        messageSequence.writeString(this.resVersion);
        if(!this.isIdsVersion(this.resVersion)) {
            HiAiLog.info(TAG, `Writing OTA fields - resCompatibleVersion: ${this.resCompatibleVersion}`);
            messageSequence.writeString(this.resCompatibleVersion);
        }
        return true;
    }

    /**
     * @override
     */
    unmarshalling(messageSequence: rpc.MessageSequence): boolean {
        this.resId = messageSequence.readString();
        this.domain = messageSequence.readString();
        this.resVersion = messageSequence.readString();
        if(!this.isIdsVersion(this.resVersion)) {
            this.resCompatibleVersion = messageSequence.readString();
            HiAiLog.info(TAG, "DownloadInfo unmarshalling some download infomation about OTA.");
        }
        HiAiLog.info(TAG, "DownloadInfo unmarshalling.");
        return true;
    }

    private isIdsVersion(resVersion: string): boolean {
        let pattern = new RegExp("^0x\\d+");
        if (!pattern.test(resVersion)) {
            HiAiLog.info(TAG, `addModelDownloadTask queryModelCloud not idIdsVersion`);
            return false;
        }
        HiAiLog.info(TAG, `addModelDownloadTask queryModelCloud is idIdsVersion`);
        return true;
    }
}