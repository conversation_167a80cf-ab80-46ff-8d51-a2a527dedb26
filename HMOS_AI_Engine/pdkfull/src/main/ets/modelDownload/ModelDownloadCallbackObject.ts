/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import HiAiLog from '../utils/log/HiAiLog';
import ModelDownloadCallbackStub from './ModelDownloadCallbackStub';

const TAG = "ModelDownloadCallbackObject";

/**
 * This class inherits the abstract class ModelDownloadCallbackStub.
 * You can refer to this class to complete the operation after you receive the callback.
 *
 * <AUTHOR>
 */
export class ModelDownloadCallbackObject extends ModelDownloadCallbackStub {
    /**
     * @override
     */
    onStart(resId: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackObject onStart; resId: " + resId);
    }

    /**
     * @override
     */
    onComplete(resId: string, resVersion: string, modelPath: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackObject onComplete; resId: " + resId + ", resVersion: "
            + resVersion);
        /**
         * Here you will get the storage path of the downloaded model.
         * You can access this path to decompress and load the resources and start your subsequent operations.
         *
         * Notice: Models will be managed in a unified manner. Do not copy models to other paths.
         */
    }

    /**
     * @override
     */
    onCancel(resId: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackObject onCancel; resId: " + resId);
    }

    /**
     * @override
     */
    onError(resId:string , errorCode: number, message: string): void {
        HiAiLog.error(TAG, "ModelDownloadCallbackObject onError; resId: " + resId + ", errorCode: " + errorCode +
            ", message: " + message);
    }

    /**
     * @override
     */
    onSchedule(resId: string, scheduleInfo: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackObject onSchedule; resId: " + resId + ", scheduleInfo: "
            + scheduleInfo );
    }
}