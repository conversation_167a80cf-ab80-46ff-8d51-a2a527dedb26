/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

/**
 * Model download type, including foreground download and background download.
 */
export enum ModelDownloadType {

  // Download a Model in the Background
  MODEL_BACK_DOWNLOAD = 0,

  // Download a Model in the Foreground
  MODEL_FORE_DOWNLOAD = 1,

  // Upgrade a Model in the Background
  MODEL_UPGRADE = 2,

  // Delete Model in the local
  MODEL_DELETE = 3,

  // Query Model in the local
  MODEL_QUERY_LOCAL = 4,

  // Subscribe Model in the Background
  MODEL_SUBSCRIBE = 5,

  // UnSubscribe Model in the Background
  MODEL_UNSUBSCRIBE = 6

}

/**
 * Model type, including language dependent and non-language dependent.
 */
export enum ModelType {

  // non-language dependent
  MODEL_COMPONENT = 0,

  // language dependent，e.g tts、asr、translation、nlu
  MODEL_LANGUAGE_PACKAGE = 1
}


/**
 * Code information about IPC communication during model download and callback
 */
export enum ModelDownloadCallbackType {

  // code of the onStart callback.
  MODEL_DOWNLOAD_ON_START = 1,

  // code of the onComplete callback.
  MODEL_DOWNLOAD_ON_COMPLETE = 2,

  // code of the onCancel callback.
  MODEL_DOWNLOAD_ON_CANCEL = 3,

  // code of the onError callback.
  MODEL_DOWNLOAD_ON_ERROR = 4,

  // code of the onSchedule callback.
  MODEL_DOWNLOAD_ON_SCHEDULE = 5,

  // code of the onResult callback.
  MODEL_MANAGER_ON_RESULT = 6,

  // code of the onResult callback when querying the information about a local model.
  MODEL_QUERY_ON_RESULT = 7,
}

/**
 * Model Domain, including "AI model" and "Lora model".
 */
export enum ModelDomain {

  // code of the AI model.
  MODEL_TYPE_AI = '0',

  // code of the lora model.
  MODEL_TYPE_LORA = '1'
}

/**
 * Model download net type
 */
export enum NetworkType{

  // code of permitting download network type is wlan
  WLAN = 0,

  // any network could download model
  ANY = 1
}