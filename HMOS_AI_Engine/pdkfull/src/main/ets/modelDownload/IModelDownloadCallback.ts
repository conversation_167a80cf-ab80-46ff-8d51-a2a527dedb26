/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import { rpc } from '@kit.IPCKit';

/**
 * This interface defines various callback functions during model download, including model download start, download
 * completion, download cancellation, download progress, and error information during download.
 *
 * @interface
 * <AUTHOR>
 */
export interface IModelDownloadCallback extends rpc.IRemoteBroker{
  /**
   * the callback of start download.
   *
   * @param{ string } model ID
   */
  onStart(resId: string);

  /**
   * the callback of complete download.
   *
   * @param{ string } model ID
   * @param{ string } model Version
   * @param{ string } model storage path
   */
  onComplete(resId: string, resVersion: string, modelPath: string);

  /**
   * the callback of cancel download.
   *
   * @param{ string } model ID
   */
  onCancel(resId: string);

  /**
   * the callback of error during model download.
   *
   * @param{ string } model ID
   * @param{ number } error code
   * @param{ string } error message
   */
  onError(resId:string , errorCode: number, message: string);

  /**
   * the callback of model download progress.
   *
   * @param{ string } model ID
   * @param{ string } download progress. e.g 10%, 20%, 78%, 100%...
   */
  onSchedule(resId: string, scheduleInfo: string);
}