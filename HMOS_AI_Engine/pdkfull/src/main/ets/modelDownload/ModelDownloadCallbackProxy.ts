/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import rpc from '@ohos.rpc';
import HiA<PERSON>Log from '../utils/log/HiAiLog';
import { IModelDownloadCallback } from './IModelDownloadCallback';
import { ModelDownloadCallbackType } from './ModelDownloadConstant'

const TAG = "ModelDownloadCallbackProxy";

/**
 *  This class is an implementation of IModelDownloadCallback and is used to process data on the server.
 *
 * * <AUTHOR>
 */
export class ModelDownloadCallbackProxy implements IModelDownloadCallback {
  private remote: rpc.IRemoteObject;

  /*
   * constructor
   *
   * @param { remote } Remote Object
   */
  public constructor(remote: rpc.IRemoteObject) {
    HiAiLog.info(TAG, "ModelDownloadCallbackProxy constructor");
    this.remote = remote;
  }

  /*
   * @Override
   */
  public asObject(): rpc.IRemoteObject {
    HiAiLog.info(TAG, "ModelDownloadCallbackProxy asObject");
    return this.remote;
  }

  /**
   * @Override
   */
  onStart(resId: string): void {
    HiAiLog.info(TAG, "ModelDownloadCallbackProxy onStart");
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeString(resId);
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    this.remote.sendMessageRequest(ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_START, requestData, reply, option)
      .then(() => {
        reply.readException();
        requestData.reclaim();
        reply.reclaim();
        HiAiLog.info(TAG, " onStart sendRequest success ");
      }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(TAG, "onResult error is:" + JSON.stringify(err));
    });
  }

  /**
   * @Override
   */
  onComplete(resId: string, resVersion: string, modelPath: string): void {
    HiAiLog.info(TAG, "ModelDownloadCallbackProxy onComplete");
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeString(resId);
    requestData.writeString(resVersion);
    requestData.writeString(modelPath);
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    this.remote.sendMessageRequest(ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_COMPLETE, requestData, reply, option)
      .then(() => {
        reply.readException();
        requestData.reclaim();
        reply.reclaim();
        HiAiLog.info(TAG, " onComplete sendRequest success ");
      }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(TAG, " onComplete error is:" + JSON.stringify(err));
    });
  }

  /**
   * @Override
   */
  onCancel(resId: string): void {
    HiAiLog.info(TAG, "ModelDownloadCallbackProxy onCancel");
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeString(resId);
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    this.remote.sendMessageRequest(ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_CANCEL, requestData, reply, option)
      .then(() => {
        reply.readException();
        requestData.reclaim();
        reply.reclaim();
        HiAiLog.info(TAG, " onCancel sendRequest success ");
      }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(TAG, " onCancel error is:" + JSON.stringify(err));
    });
  }

  /**
   * @Override
   */
  onError(resId: string, errorCode: number, message: string): void {
    HiAiLog.info(TAG, "ModelDownloadCallbackProxy onError");
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeString(resId);
    requestData.writeInt(errorCode);
    requestData.writeString(message);
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    this.remote.sendMessageRequest(ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_ERROR, requestData, reply, option)
      .then(() => {
        reply.readException();
        requestData.reclaim();
        reply.reclaim();
        HiAiLog.info(TAG, " onError sendRequest success ");
      }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(TAG, " onError error is:" + JSON.stringify(err));
    });
  }

  /**
   * @Override
   */
  onSchedule(resId: string, sheduleInfo: string): void {
    HiAiLog.info(TAG, "ModelDownloadCallbackProxy onSchedule");
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeString(resId);
    requestData.writeString(sheduleInfo);
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    this.remote.sendMessageRequest(ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_SCHEDULE, requestData, reply, option)
      .then(() => {
        reply.readException();
        requestData.reclaim();
        reply.reclaim();
        HiAiLog.info(TAG, " onSchedule sendRequest success ");
      }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(TAG, " onSchedule error is:" + JSON.stringify(err));
    });
  }
}