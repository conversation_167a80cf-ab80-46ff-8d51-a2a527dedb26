/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import HiAiLog from '../utils/log/HiAiLog';
import { IModelDownloadCallback } from './IModelDownloadCallback';
import rpc from '@ohos.rpc';
import { ModelDownloadCallbackProxy } from './ModelDownloadCallbackProxy'
import { ModelDownloadCallbackType } from './ModelDownloadConstant';

const TAG = "ModelDownloadCallbackStub";

/**
 * This abstract class serves as the implementation of IModelDownloadCallback and is used to receive callback data on the client.
 *
 * <AUTHOR>
 */
export default abstract class ModelDownloadCallbackStub extends rpc.RemoteObject implements IModelDownloadCallback {

    /**
     * The constructor.
     *
     * @param des the descriptor
     */
    constructor(des: string) {
        super(des)
    }

    /**
     * @Override
     */
    asObject(): rpc.IRemoteObject {
        return this;
    }


    /**
     * Get the proxy object.
     *
     * @param remote the remote object
     * @return the proxy object
     */
    public static asInterface(object: rpc.IRemoteObject): IModelDownloadCallback {
        return new ModelDownloadCallbackProxy(object);
    }

    /**
     * @Override
     */
    onRemoteMessageRequest(code: number, data: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): boolean {
        HiAiLog.info(TAG, "ModelDownloadCallbackObject onRemoteRequest code is:" + code);
        switch (code) {
            case ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_START: {
                HiAiLog.info(TAG, `ModelDownloadCallbackObject onRemoteMessageRequest onStart`);
                let resId: string = data.readString();
                this.onStart(resId);
                return true;
            }
            case ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_COMPLETE: {
                HiAiLog.info(TAG, `ModelDownloadCallbackObject onRemoteMessageRequest onComplete`);
                let resId: string = data.readString();
                let resVersion: string = data.readString();
                let modelPath: string = data.readString();
                this.onComplete(resId, resVersion, modelPath);
                return true;
            }
            case ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_CANCEL: {
                HiAiLog.info(TAG, `ModelDownloadCallbackObject onRemoteMessageRequest onCancel`);
                let resId: string = data.readString();
                this.onCancel(resId);
                return true;
            }
            case ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_ERROR: {
                HiAiLog.info(TAG, `onRemoteMessageRequest onError`);
                let resId: string = data.readString();
                let errorCode: number = data.readInt();
                let message: string = data.readString();
                this.onError(resId, errorCode, message);
                return true;
            }
            case ModelDownloadCallbackType.MODEL_DOWNLOAD_ON_SCHEDULE: {
                HiAiLog.info(TAG, `ModelDownloadCallbackObject onRemoteMessageRequest onSchedule`);
                let resId: string = data.readString();
                let scheduleInfo: string = data.readString();
                this.onSchedule(resId, scheduleInfo);
                return true;
            }
            default:
                return false;
        }
    }

    getMessageCode(): number {
        throw new Error('ModelDownloadCallbackStub Method not implemented.');
    }

    /**
     * @Override
     */
    onStart(resId: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackStub onStart; resId: " + resId);
    }

    /**
     * @Override
     */
    onComplete(resId: string, resVersion: string, modelPath: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackStub onComplete; resId: " + resId + ", resVersion: " + resVersion );
    }

    /**
     * @Override
     */
    onCancel(resId: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackStub onCancel; resId: " + resId);
    }

    /**
     * @Override
     */
    onError(resId:string , errorCode: number, message: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackStub onError; resId: " + resId+ ", errorCode: " + errorCode + ", " +
            "message: " + message);
    }

    /**
     * @Override
     */
    onSchedule(resId: string, scheduleInfo: string): void {
        HiAiLog.info(TAG, "ModelDownloadCallbackStub onSchedule; resId: " + resId + ", scheduleInfo: " + scheduleInfo );
    }

}