/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import <PERSON><PERSON><PERSON><PERSON><PERSON> from './log/HiAiLog';
import Constants from './Constants';

/**
 * Tool class of json transformation.
 *
 * <AUTHOR>
 * @since 2022-01-28
 */
export default class JsonUtil {
  private static readonly TAG: string = "JsonUtil";

  private constructor() {
  }

  /**
   * String to object.
   *
   * @param text the string to be converted
   * @return the converted object
   */
  public static stringToClass<T>(text: string): T {
    let result;
    try {
      result = JSON.parse(text);
    } catch (error) {
      HiAiLog.error(JsonUtil.TAG, "text JSON error" + error);
    }
    return result;
  }

  /**
   * class to string.
   *
   * @param data the class to be converted
   * @return the result string
   */
  public static classToString<T>(data: T): string {
    let result: string = Constants.EMPTY_STRING;
    try {
      result = JSON.stringify(data);
    } catch (error) {
      HiAiLog.error(JsonUtil.TAG, "class to object JSON error" + error);
    }
    return result;
  }
}
