/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import { BusinessError } from '../interfaces/ICallback';

/**
 * Common Errors Class to define result codes
 */
export enum AbilityStatusCode {

    // ability onConnect status
    ON_CONNECT_STATUS = 1,

    // ability onDisconnect status
    DISCONNECT_STATUS = 2,

    // ability onFail status
    CONNECT_FAIL_STATUS = 3,
}

/**
 * Common rpc request result codes.
 */
export enum RpcRequestCode {

    // request type ID corresponding to the remote
    CONNECT_ABILITY_SUCCESS = 1,

    // Request failed
    REQUEST_FAILED = 2,

    // Request success
    REQUEST_SUCCESS = 3,

    // Remote ability death
    REMOTE_ABILITY_DEATH = 4
}

/**
 * Model download result codes.
 */
export enum ModelDownloadResultCode {
    // 成功，包括查询成功、删除成功等
    SUCCESS = 0,

    // 参数错误
    PARAMATER_INVALID = 401,

    // 无网络、断网时返回
    NO_NETWORK_STATUS = 1,

    // 调用方非系统应用，仅可判断是否为系统应用调用pdkfull，插件的调用方是否为系统应用需插件自行判断
    NO_SYSTEM_APPLICATION = 2,

    // 模型云侧资源不存在，可能原因：1、云侧未上传资源，2、打桩环境设置不正确
    NO_MODEL = 3,

    // 拷贝文件失败
    COPY_FILE_FAILED = 4,

    // 下载通知弹窗，用户不同意下载
    DOWNLOAD_NOT_ALLOWED = 5,

    // 下载超时，当前设置的超时时间为5.5min
    DOWNLOAD_TIME_OUT = 6,

    // 下载异常
    DOWNLOAD_EXCEPTION = 7,

    // 下载通知弹窗，返回桌面，退出弹窗
    DOWNLOAD_BACK_TO_DESKTOP = 8,

    // 正在下载弹窗，退至后台实况弹窗状态
    DOWNLOAD_BACKGROUND = 9,

    // OTA服务不可用，或OTA服务连接失败
    SERVICE_UNAVAILABLE = 10,

    // 内存不足
    OUT_OF_MEMORY = 11,

    // 完整性校验失败
    INTEGRITY_CHECK_FAILED = 12,

    // 解压失败，包括模型源文件解压失败、模型包解压失败
    UNZIP_FAILED = 13,

    // 删除本地模型失败，包括模型源文件删除失败、数据库删除失败
    DELETE_LOCAL_FAILED = 14,

    // 查询本地模型失败
    QUERY_LOCAL_FAILED = 15,

    // 任务繁忙，请稍后重试，例如当前仅支持一个模型前台下载
    TASK_BUSY = 16,

    // 立即更新失败，已经是最新版本
    IS_LATEST_VERSION = 17,

    // 下载中断，包括网络中断等异常情况,可支持断点重传，从断点处继续下载
    DOWNLOAD_INTERRUPTION = 18
}

/**
 * Report result codes.
 */
export enum ReportResultCode {
    // 成功，包括查询成功、删除成功等
    SUCCESS = 0,

    // 参数错误
    PARAMATER_INVALID = 401,

    // 无网络、断网时返回
    NO_NETWORK_STATUS = 1,

    // 调用方非系统应用，仅可判断是否为系统应用调用pdkfull，插件的调用方是否为系统应用需插件自行判断
    NO_SYSTEM_APPLICATION = 2,

    // 模型云侧资源不存在，可能原因：1、云侧未上传资源，2、打桩环境设置不正确
    NO_MODEL = 3,

    // 拷贝文件失败
    COPY_FILE_FAILED = 4,

    // 下载通知弹窗，用户不同意下载
    DOWNLOAD_NOT_ALLOWED = 5,

    // 下载超时，当前设置的超时时间为5.5min
    DOWNLOAD_TIME_OUT = 6,

    // 下载异常
    DOWNLOAD_EXCEPTION = 7,

    // 下载通知弹窗，返回桌面，退出弹窗
    DOWNLOAD_BACK_TO_DESKTOP = 8,

    // 正在下载弹窗，退至后台实况弹窗状态
    DOWNLOAD_BACKGROUND = 9,

    // OTA服务不可用，或OTA服务连接失败
    SERVICE_UNAVAILABLE = 10,

    // 内存不足
    OUT_OF_MEMORY = 11,

    // 完整性校验失败
    INTEGRITY_CHECK_FAILED = 12,

    // 解压失败，包括模型源文件解压失败、模型包解压失败
    UNZIP_FAILED = 13,

    // 删除本地模型失败，包括模型源文件删除失败、数据库删除失败
    DELETE_LOCAL_FAILED = 14,

    // 查询本地模型失败
    QUERY_LOCAL_FAILED = 15,

    // 任务繁忙，请稍后重试，例如当前仅支持一个模型前台下载
    TASK_BUSY = 16,

    // 立即更新，已经是最新版本
    IS_LATEST_VERSION = 17,

    // 下载中断，包括网络中断等异常情况,可支持断点重传，从断点处继续下载
    DOWNLOAD_INTERRUPTION = 18,

    // 下载通知弹窗，用户同意下载
    DOWNLOAD_ALLOWED = 19,

    // 下载进度弹窗，用户取消下载
    DOWNLOAD_CANCELED = 20
}

/**
 * Report result message.
 */
export enum ReportResultCodeMessage {
    // 成功，包括查询成功、删除成功等
    SUCCESS = 'success',

    // 无网络、断网时返回
    NO_NETWORK_STATUS = 'network is not connected.',

    // 下载通知弹窗，用户不同意下载
    DOWNLOAD_NOT_ALLOWED = 'The user does not agree to download.',

    // 下载通知弹窗，返回桌面，退出弹窗
    DOWNLOAD_BACK_TO_DESKTOP = "The user returns to the desktop, and the window is closed.",

    // 正在下载弹窗，退至后台实况弹窗状态
    DOWNLOAD_BACKGROUND = 'The user returns to the background for downloading.',

    // 下载通知弹窗，用户同意下载
    DOWNLOAD_ALLOWED = 'The user agree to download.',

    // 下载进度弹窗，用户取消下载
    DOWNLOAD_CANCELED = 'The user cancel the download.',

    // 下载、解压时存储空间不足
    SPACE_INSUFFICIENT = 'The storage is insufficient',

    // 模型文件受损
    MODEL_HAS_BEEN_DAMAGED = 'The model zip have been damaged',

    // 检查模型压缩包时出错
    CHECK_ZIP_FAILED = 'Check zip file failed',

    // 解压模型时出错
    DEPRESS_FAILED = 'The update package depressed failed',

    // 模型兼容版本为空
    COMPATIBLE_VERSION_IS_EMPTY  = 'The model compatible version is empty.',

    // 当前版本为空
    CURRENT_VERSION_IS_EMPTY = 'The model current version is empty',

    // 模型已经是最新的了，不需要更新
    MODEL_IS_LATEST = 'The model is of the latest version and does not need to be updated',

    // 下载进度长时间未更新，可能是数据服务进程、OTA进程死亡
    DOWNLOAD_TIME_OUT = 'Download timeout'
}

/**
 * Model download result codes.
 */
export enum ModelUpgradeResultCode {
    // 参数错误
    PARAMATER_INVALID = 401,

    // 用户未同意小艺隐私及打开WLAN自动下载开关，不可使用静默升级功能
    NO_UPGRADE_PERMISSION = -200,

    // 语音视觉服务没有使用OTA权限
    APPLICATION_NO_OTA_PERMISSION = -203,

    // 语音视觉服务没有使用IDS权限
    APPLICATION_NO_PERMISSION = -103,

    // 网络类型错误，例如蜂窝数据查询IDS测试网/开发网数据
    NETWORK_TYPE_ERROR = -117,

    // 测试网查询现网数据时触发
    UNIFIED_ACCESS_ERROR = -121,

    // 测试网查询现网数据时触发
    UNIFIED_CLOUD_BUSY = -100,
}

/**
 * Model download status code which is used in report.
 */
export enum ModelDownloadStatus {
    // 进入后台实框弹窗
    ENTER_PUBLISH_WINDOW = 1,

    // 下载通知弹窗，用户同意下载
    DOWNLOAD_ALLOWED = 0,

    // 下载通知弹窗，用户不同意下载
    DOWNLOAD_NOT_ALLOWED = -1,

    // 正在下载弹窗，用户取消下载
    DOWNLOAD_CANCELED = -2,

    // 后台实框弹窗，用户取消下载
    DOWNLOAD_CANCELED_IN_PUBLISH = -3,
}

/**
 * Model download status code which is used in report.
 */
export enum ModelManagerResult {
    // 前台下载成功
    ENTER_PUBLISH_WINDOW = 1,

    // 下载通知弹窗，用户同意下载
    DOWNLOAD_ALLOWED = 0,

    // 下载通知弹窗，用户不同意下载
    DOWNLOAD_NOT_ALLOWED = -1,

    // 正在下载弹窗，用户取消下载
    DOWNLOAD_CANCELED = -2,

    // 后台实框弹窗，用户取消下载
    DOWNLOAD_CANCELED_IN_PUBLISH = -3,
}

/**
 * Error codes during program running.
 */
export enum RunningErrorCode {
    // ID of Query failed
    QUERY_FAILED_ID = -1,

    // ID of variable invalid.
    INVALID = 0,

    // ID of running success
    SUCCESS = 1,

    // ID of running failed
    FAILED = 2,

    // The result is empty
    EMPTY = 3,
}

/**
 * Error codes during program running.
 */
export class ResultCode {
    // ID of Query failed
    public static readonly QUERY_FAILED_ID: BusinessError = {
        code: RunningErrorCode.QUERY_FAILED_ID
    };

    // ID of variable invalid.
    public static readonly INVALID: BusinessError = {
        code: RunningErrorCode.INVALID
    };

    // ID of running success
    public static readonly SUCCESS: BusinessError = {
        code: RunningErrorCode.SUCCESS
    };

    // ID of running failed
    public static readonly FAILED: BusinessError = {
        code: RunningErrorCode.FAILED
    };

    // ID of running failed
    public static readonly EMPTY: BusinessError = {
        code: RunningErrorCode.EMPTY
    };

    public static getError(code: number): BusinessError {
        if (code === null || code === undefined) {
            return ResultCode.FAILED;
        }
        switch (code) {
            case RunningErrorCode.SUCCESS:
                return ResultCode.SUCCESS;
                break;
            case RunningErrorCode.INVALID:
                return ResultCode.INVALID;
                break;
            case RunningErrorCode.QUERY_FAILED_ID:
                return ResultCode.QUERY_FAILED_ID;
                break;
            case RunningErrorCode.EMPTY:
                return ResultCode.EMPTY;
                break;
            default:
                return ResultCode.FAILED;
                break;
        }
    }
}