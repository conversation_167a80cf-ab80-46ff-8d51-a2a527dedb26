/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import HiAiLog from '../utils/log/HiAiLog';
import connection from '@ohos.net.connection';

const TAG: string = 'NetWorkUtil'

/**
 * NetWork util.
 *
 * <AUTHOR>
 */
export class NetWorkUtil {
  /*
   * Check if net work is a wifi network or a cellular network.
   *
   * @returns true if cellular network, false otherwise.
   */
  static isConnectNetwork(): boolean{
    try {
      return connection.hasDefaultNetSync();
    } catch (error) {
      HiAiLog.error(TAG, "checkNetwork connection failed, error is: "+ JSON.stringify(error));
      return false;
    }
  }

  /*
 * Check if network is Available.
 *
 * @returns true if cellular network, false otherwise.
 */
  static isAvailableNetwork(): boolean{
    try {
      let netHandle = connection.getDefaultNetSync();
      let netCapValue = connection.getNetCapabilitiesSync(netHandle).networkCap;
      HiAiLog.error(TAG, "checkNetwork connection netCapValue is: "+ netCapValue.some);
      if(netCapValue.some(num => (num === 16))) {
        HiAiLog.error(TAG, "checkNetwork connection netCapValue is true ");
        return true;
      } else {
        HiAiLog.error(TAG, "checkNetwork connection netCapValue is false ");
        return false;
      }
    } catch (error) {
      HiAiLog.error(TAG, "checkNetwork status failed, error is: "+ JSON.stringify(error));
      return false;
    }
  }

  /**
   * Check if net work is a cellular network, can not distinguish hotpot connection by far.
   *
   * @returns true if cellular network, false otherwise.
   */
  public static isCellularNetwork(): boolean {
    try {
      const netWorkType: connection.NetBearType =
        connection.getNetCapabilitiesSync(connection.getDefaultNetSync())?.bearerTypes?.[0];
      return netWorkType === connection.NetBearType.BEARER_CELLULAR;
    } catch (err) {
      HiAiLog.warn(TAG, 'check isNetWorkMetered failed: %s' + err?.code );
      return false;
    }
  }

  /**
   * Check if network is a WiFi network.
   *
   * @returns true if WiFi network, false otherwise.
   */
  public static isWifiNetwork(): boolean {
    try {
      const netWorkType: connection.NetBearType =
        connection.getNetCapabilitiesSync(connection.getDefaultNetSync())?.bearerTypes?.[0];
      return netWorkType === connection.NetBearType.BEARER_WIFI;
    } catch (err) {
      HiAiLog.warn(TAG, 'check isWifiNetwork failed: %s' + err?.code );
      return false;
    }
  }
}
