/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import HiAiLog from './log/HiAiLog';

/*
 * Constructing a Singleton Object
 * */
class GlobalContext {
    private static instance: GlobalContext;
    private _objects = new Map<string, Object>();

    private constructor() {
    }

    public static getContext(): GlobalContext {
        if (!GlobalContext.instance) {
            GlobalContext.instance = new GlobalContext();
        }
        return GlobalContext.instance;
    }

    getObject(value: string): Object | undefined {
        return this._objects.get(value);
    }

    setObject(key: string, objectClass: Object): void {
        this._objects.set(key, objectClass);
    }
}

/*
 * global context set
 * @param { key: string, value: Object }
 * return GlobalContextGetKey
 * */
export function GlobalContextSetKey(key: string, value: Object): Object | undefined {
    let globalContextGetKey: Object | undefined = GlobalContextGetKey(key)
    if (!globalContextGetKey) {
        (GlobalContext.getContext().setObject(key, value)) as void;
        HiAiLog.info(key, `${key} set succeed`);
        return GlobalContextGetKey(key)
    }
    HiAiLog.info(key, `${key} set failed, The original value is used.`);
    return globalContextGetKey;
}

/*
 * global context get
 * @param { key: string }
 * */
export function GlobalContextGetKey(key: string): Object | undefined {
    let globalContextGetKey: Object | undefined = GlobalContext.getContext().getObject(key) as Object | undefined
    if (!globalContextGetKey) {
        return undefined
    }
    HiAiLog.info(key, `${key} get succeed`);
    return globalContextGetKey;
}

/*
 * global context update
 * @param { key: string, value: Object }
 * return GlobalContextGetKey
 * */
export function GlobalContextUpdateKey(key: string, value: Object): Object | undefined {
    GlobalContext.getContext().setObject(key, value);
    HiAiLog.info(key, `${key} update succeed`);
    return GlobalContextGetKey(key);
}
