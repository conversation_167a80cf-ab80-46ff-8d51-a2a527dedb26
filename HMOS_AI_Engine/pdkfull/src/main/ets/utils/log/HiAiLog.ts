/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import { HiLogNode, LOG, Logger, LogLevel } from '@hw-hmf/logger'

const TAG_PREFIX = "HiAI_";
const HIAI_LOG_DOMAIN_ID: number = 0x0001;

/**
 * This class is a tool class for log printing of engineering codes. Contains 5 levels of log printing methods.
 *
 * <AUTHOR>
 */
export default class HiAiLog {
    // Set the default prefix tag
    private static prefixTag: string = TAG_PREFIX;

    /**
     * Used to modify the default log prefix.
     * For example, the prefix of the NLU module can be set to "HiAI_NLU_"
     *
     * @param prefixTag the prefix of the module
     */
    static setPrefixTag(prefixTag: string): void {
        HiAiLog.prefixTag = prefixTag;
    }

    /**
     * Outputs debug-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static debug(tag: string, message: string): void {
        Logger.config({
            node: new HiLogNode(HIAI_LOG_DOMAIN_ID),
            level: LogLevel.DEBUG
        })
        LOG.d(HiAiLog.prefixTag + tag, message);
    }

    /**
     * Outputs info-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static info(tag: string, message: string): void {
        Logger.config({
            node: new HiLogNode(HIAI_LOG_DOMAIN_ID)
        })
        LOG.i(HiAiLog.prefixTag + tag, message);
    }

    /**
     * Outputs warn-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static warn(tag: string, message: string): void {
        Logger.config({
            node: new HiLogNode(HIAI_LOG_DOMAIN_ID)
        })
        LOG.w(HiAiLog.prefixTag + tag, message);
    }

    /**
     * Outputs error-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static error(tag: string, message: string): void {
        Logger.config({
            node: new HiLogNode(HIAI_LOG_DOMAIN_ID)
        })
        LOG.e(HiAiLog.prefixTag + tag, message);
    }

    /**
     * Outputs fatal-level logs.
     *
     * @param tag tag name
     * @param message log message
     */
    static fatal(tag: string, message: string): void {
        Logger.config({
            node: new HiLogNode(HIAI_LOG_DOMAIN_ID)
        })
        LOG.f(tag, HiAiLog.prefixTag + message);
    }
}