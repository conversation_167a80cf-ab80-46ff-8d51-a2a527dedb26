/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

import <PERSON><PERSON><PERSON><PERSON>og from './log/HiAiLog';

const TAG: string = "ObjectUtil";

/**
 * Tool class for object-related information acquisition.
 * <AUTHOR>
 */
export default class ObjectUtil {
  /**
   * Determines whether an object is an empty object.
   * @param object Object to be judged
   * @return Return true to indicate that the object is empty, and false to indicate that it is not empty.
   */
  static isNullObject<T>(object: T): boolean {
    if (object === undefined || object === null) {
      return true;
    }
    try {
      if (JSON.stringify(object) === "{}") {
        return true;
      }
    } catch (error) {
      HiAiLog.error(TAG, `Execute JSON.stringify() failed! Cause: ${error}, Object: ${object}.`)
    }
    return false;
  }

  /**
   * Check if a text is empty.
   * @param text text to be judged
   * @return Return true to indicate that the text is empty, and false to indicate that it is not empty.
   */
  static isEmptyText(text: string): boolean {
    if (text === "" || text === null || text === undefined) {
      return true;
    }
    return false;
  }
}