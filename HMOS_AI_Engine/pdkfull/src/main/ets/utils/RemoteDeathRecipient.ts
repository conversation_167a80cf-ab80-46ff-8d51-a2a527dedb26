/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import { ErrorCallback, BusinessError } from '../interfaces/ICallback';
import { RpcRequestCode } from './ResCode';
import Hi<PERSON><PERSON>Log from './log/HiAiLog';

const TAG: string = "RemoteDeathRecipient";
const DEATH_FLAG: number = 0;

/**
 * Class of death recipient about remote object
 * <AUTHOR>
 */
export default class RemoteDeathRecipient implements rpc.DeathRecipient {
    callback: ErrorCallback<BusinessError> = (): void => {
    };

    /**
     * Register remote callback
     * @param remoteObject remote object
     * @param callback Callback object when remote death occurs
     */
    registerRemoteObject(remoteObject: rpc.IRemoteObject, callback: ErrorCallback<BusinessError>): void {
        this.callback = callback;
        remoteObject.registerDeathRecipient(this, DEATH_FLAG);
    }

    /**
     * Remote death callback method
     */
    onRemoteDied(): void {
        HiAiLog.info(TAG, `onRemoteDied called`);
        let errorInfo: BusinessError = {
            code: RpcRequestCode.REMOTE_ABILITY_DEATH,
        };
        this.callback(errorInfo);
    }
}