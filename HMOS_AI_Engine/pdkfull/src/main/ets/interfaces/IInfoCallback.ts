/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { IRemoteCallback } from './ICallback';

/**
 * Callback related to information acquisition. It should be noted that this callback only obtains string
 * type information. After obtaining the information, it needs to be converted into the corresponding information
 * type before it can be used.
 * @param { string } the callback information
 * <AUTHOR>
 */
export default interface IInfoCallback extends IRemoteCallback<string> {
    onResult(code: number, data: string): void;
}