/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import HiAiLog from '../utils/log/HiAiLog';

/**
 * The proxy of InfoCallback.
 *
 * <AUTHOR>
 */
export default abstract class InfoCallbackProxy {
  private static readonly TAG: string = "InfoCallbackProxy";

  private remote: rpc.IRemoteObject;

  /**
   * The constructor.
   *
   * @param remote the remote object
   */
  constructor(remote: rpc.IRemoteObject) {
    this.remote = remote;
  }

  /**
   * @Override
   */
  asObject(): rpc.IRemoteObject {
    return this.remote;
  }

  /**
   * @Override
   */
  onResult(code: number, data: string): void {
    HiAiLog.info(InfoCallbackProxy.TAG, " onResult start ");
    if (this.remote == null) {
      HiAiLog.warn(InfoCallbackProxy.TAG, "remote is null");
      return;
    }
    let requestData: rpc.MessageSequence = new rpc.MessageSequence();
    requestData.writeInterfaceToken(this.getDescriptor());
    requestData.writeInt(code);
    requestData.writeString(data);
    let reply: rpc.MessageSequence = new rpc.MessageSequence();
    let option: rpc.MessageOption = new rpc.MessageOption();
    HiAiLog.info(InfoCallbackProxy.TAG, " onResult sendRequest ");
    this.remote.sendMessageRequest(this.getMessageCode(), requestData, reply, option).then(<T>(ret: T): void => {
      reply.readException();
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(InfoCallbackProxy.TAG, " onResult sendRequest success ");
    }).catch((err) => {
      requestData.reclaim();
      reply.reclaim();
      HiAiLog.info(InfoCallbackProxy.TAG, "onResult error is:" + JSON.stringify(err));
    });
  }

  /**
   * Get the remote request code. This code needs to be defined in the subclass.
   * @return { number } the remote request code
   */
  abstract getMessageCode(): number;

  /**
   * Get the stub descriptor.
   * @return { string } the remote remote descriptor
   */
  abstract getDescriptor(): string;
}