/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/*
 * interface ElementName
 * Input parameter for connectOption.
 * */
export interface ElementName {
    /**
     * device id
     *
     * @default -
     * @syscap SystemCapability.BundleManager.BundleFramework
     * @since 7
     * @deprecated since 9
     */
    deviceId?: string;

    /**
     * bundle name
     *
     * @default -
     * @syscap SystemCapability.BundleManager.BundleFramework
     * @since 7
     * @deprecated since 9
     */
    bundleName: string;

    /**
     * ability name
     *
     * @default ability class name.
     * @syscap SystemCapability.BundleManager.BundleFramework
     * @since 7
     * @deprecated since 9
     */
    abilityName: string;

    /**
     * uri
     *
     * @default -
     * @syscap SystemCapability.BundleManager.BundleFramework
     * @since 7
     * @deprecated since 9
     */
    uri?: string;

    /**
     * shortName
     *
     * @default -
     * @syscap SystemCapability.BundleManager.BundleFramework
     * @since 7
     * @deprecated since 9
     */
    shortName?: string;
}