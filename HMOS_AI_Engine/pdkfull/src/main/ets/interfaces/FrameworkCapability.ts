/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * This class defines the framework capability list, and through this list and IPC communication capability,
 * you can access the relevant capabilities of the framework server.
 * <AUTHOR>
 */
export enum Capability {
    // Get label capabilities
    LABELING_CAPABILITY = 1,

    // Ability to get plugin information.
    PLUGIN_INFO_MANAGER_CAPABILITY = 2,

    // Ability to download.
    MODEL_DOWNLOAD_CAPABILITY = 3
}

/**
 *
 */
export enum CommonMethod {
    // Initialize related settings.
    INITIALIZATION = 100,

    // Release related resources.
    RELEASE = 101
}