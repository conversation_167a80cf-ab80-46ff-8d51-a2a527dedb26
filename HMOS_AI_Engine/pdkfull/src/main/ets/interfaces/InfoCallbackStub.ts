/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import Hi<PERSON><PERSON>Log from '../utils/log/HiAiLog';
import IInfoCallback from './IInfoCallback';
import rpc from '@ohos.rpc';
import PluginInfoCallbackStub from '../abilityservice/information/PluginInfoCallbackStub';

/**
 * The stub of InfoCallback.
 *
 * <AUTHOR>
 */
export default abstract class InfoCallbackStub extends rpc.RemoteObject implements IInfoCallback {
    private static readonly TAG: string = "InfoCallbackStub";

    /**
     * The constructor.
     *
     * @param des the descriptor
     */
    constructor(des: string) {
        super(des)
    }

    /**
     * @Override
     */
    asObject(): rpc.IRemoteObject {
        return this;
    }

    /**
     * @Override
     */
    onRemoteMessageRequest(code: number, data: rpc.MessageSequence, reply: rpc.MessageSequence, options: rpc.MessageOption): boolean {
        HiAiLog.info(InfoCallbackStub.TAG, "onRemoteRequest code is:" + code);
        let token: string = data.readInterfaceToken();
        if (this.getDescriptor() !== token) {
            HiAiLog.error(InfoCallbackStub.TAG, "token is error");
            return false;
        }
        if (code === this.getMessageCode()) {
            let code: number = data.readInt();
            let dataContent: string = data.readString();
            HiAiLog.info(InfoCallbackStub.TAG, "onRemoteRequest is:" + JSON.stringify(dataContent));
            this.onResult(code, dataContent);
            reply.writeNoException()
        } else {
            return (super.onRemoteMessageRequest(code, data, reply, options) as boolean);
        }
        return true;
    }

    /**
     * Callback method for processing results. This method needs to be implemented in the proxy.
     * @return { number } the processing results code
     * @return { string } the processing results
     */
    abstract onResult(code: number, data: string): void;

    /**
     * Get the remote request code. This code needs to be defined in the subclass.
     * @return { number } the remote request code
     */
    abstract getMessageCode(): number;

    /**
     * Get the stub descriptor.
     * @return { string } the remote remote descriptor
     */
    abstract getDescriptor(): string;
}