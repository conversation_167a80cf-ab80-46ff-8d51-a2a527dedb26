/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import common from '@ohos.app.ability.common';
import RegisterFrameworkInfo from '../abilityservice/RegisterFrameworkInfo';

/**
 * All exposed abilities must implement this interface and actively register with the framework. For example,
 * NLU is a plugin that provides text analysis capabilities for third-party applications. The ability of this plugin
 * is exposed to the third-party applications, so the ability of the NLU must implement this interface.
 */
export default interface IPlugin {
    /**
     * Interface of initializing plugin which is used to load the so, model and shared resources of related services.
     */
    init();

    /**
     * Connect framework services
     */
    connectFrameworkService(context: common.ServiceExtensionContext);

    /**
     * Register the information of the caller to the framework.
     */
    registerFramework(registerInfo: RegisterFrameworkInfo);

    /**
     * Unregister the information of the caller to the framework.
     */
    unregisterFramework(unregisterInfo: RegisterFrameworkInfo);

    /**
     * Release service-related resources.
     */
    onRelease();
}