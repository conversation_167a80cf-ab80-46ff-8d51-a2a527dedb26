/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * Defines the basic error callback.
 * <AUTHOR>
 */
export type ErrorCallback<T extends BusinessError> = (err: T) => void;

/*
 * Defines the death recipient async callback.
 * */
export type AsyncCallback<T, E = void> = (err: BusinessError<E>, data: T | null) => void;

/**
 * Defines the remote request callback
 * @param { T } the callback type of the remote request
 * <AUTHOR>
 */
export interface IRemoteCallback<T> {
    onResult(code: number, data: T): void;
}

/**
 * Defines the error interface.
 * <AUTHOR>
 */
export interface BusinessError<T = void> {
    /**
     * Defines the basic error code.
     * @type { number }
     */
    code: number;

    /**
     * Defines the additional information for business
     * @type { ?T }
     */
    data?: T;

    /**
     * Defines the message for business
     * @type { string }
     */
    message?: string;
}