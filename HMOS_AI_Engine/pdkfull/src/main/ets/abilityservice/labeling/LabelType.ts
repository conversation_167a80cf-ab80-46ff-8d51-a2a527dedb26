/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * List of definitions for HiAI tag types.
 * <AUTHOR>
 */
export enum LabelType {
    // Chip type, eg CPU, NPU. A chip has and can only have one operating system.
    CHIP_LABEL_TYPE = "CHIP",
    // Region type, eg china, overseas.
    REGION_LABEL_TYPE = "REGION",
    // Product type, eg Full, Lite
    PRODUCT_LABEL_TYPE = "PRODUCE"
}