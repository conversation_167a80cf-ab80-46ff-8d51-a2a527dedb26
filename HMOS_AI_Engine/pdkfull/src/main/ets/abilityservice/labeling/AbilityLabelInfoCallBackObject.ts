/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { AsyncCallback } from '../../interfaces/ICallback';
import HiAiLog from '../../utils/log/HiAiLog';
import { ResultCode, RunningErrorCode } from '../../utils/ResCode';
import AbilityLabelManager from './AbilityLabelManager';
import LabelInfo from './LabelInfo';

import LabelInfoCallbackStub from './LabelInfoCallbackStub';

/*
 * Callback of the device label list
 * @param { des: string, TAG: string, callback: AsyncCallback<Array<LabelInfo>> }
 * */
export class AbilityLabelInfoCallBackObject extends LabelInfoCallbackStub {
    TAG: string;
    callback: AsyncCallback<Array<LabelInfo>>

    constructor(des: string, tag: string, callback: AsyncCallback<Array<LabelInfo>>) {
        super(des);
        this.TAG = tag;
        this.callback = callback;
    }

    onResult(code: number, data: string): void {
        HiAiLog.info(this.TAG, "getAbilityLabel callback code is " + code);
        HiAiLog.info(this.TAG, "getAbilityLabel callback data is " + JSON.stringify(data));
        if (code === RunningErrorCode.SUCCESS) {
            let moduleInfoList: Array<LabelInfo> = AbilityLabelManager.parseLabelInfoList(data);
            if (moduleInfoList === null || moduleInfoList === undefined || moduleInfoList.length === 0) {
                this.callback(ResultCode.EMPTY, null);
            } else {
                this.callback(ResultCode.SUCCESS, moduleInfoList);
            }
            HiAiLog.info(this.TAG, "getAbilityLabel success");
        } else {
            HiAiLog.info(this.TAG, "getAbilityLabel fail");
            this.callback(ResultCode.getError(code), null);
        }
    }
}