/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import InfoCallbackProxy from '../../interfaces/InfoCallbackProxy';
import rpc from '@ohos.rpc';

/**
 * The proxy of InfoCallbackProxy.
 *
 * <AUTHOR>
 */
export default class LabelInfoCallbackProxy extends InfoCallbackProxy {
    /**
     * The index of onFinished method.
     */
    public static readonly COMMAND_ON_RESULT: number = 0;

    /**
     * The descriptor.
     */
    public static readonly DESCRIPTOR: string = "HiAI.openapi.ILabelInfoCallback";

    /**
     * The constructor.
     *
     * @param remote the remote object
     */
    constructor(remote: rpc.IRemoteObject) {
        super(remote);
    }

    /**
     * @override
     */
    getMessageCode(): number {
        return LabelInfoCallbackProxy.COMMAND_ON_RESULT;
    }

    /**
     * @override
     */
    getDescriptor(): string {
        return LabelInfoCallbackProxy.DESCRIPTOR;
    }
}