/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc'
import { LabelType } from './LabelType';
import HiAiLog from '../../utils/log/HiAiLog';
import ObjectUtil from '../../utils/ObjectUtil';
import JsonUtil from '../../utils/JsonUtil';
import { DEFAULT } from '@ohos/hypium/src/main/Constant';

const TAG: string = "LabelInfo";

/**
 * The basic information class of the label.
 * @class
 * <AUTHOR>
 */
export default class LabelInfo implements rpc.Parcelable {
    labelName: string;
    labelValue: string;

    /**
     * Constructor of { LabelInfo }
     * @param { string | LabelInfo } the label content
     */
    constructor(labelInfo?: string | LabelInfo) {
        if (labelInfo !== null) {
            if (typeof labelInfo === "string") {
                HiAiLog.info(TAG, "labelInfo is string");
                this.initStringContent(labelInfo);
            } else if (labelInfo instanceof Object) {
                HiAiLog.info(TAG, "labelInfo is object");
                this.initLabelInfo(labelInfo);
            }
        }
    }

    /**
     * Get the label type according to the tag name.
     * @return { LabelType } the type of label
     */
    getLabelType(): LabelType {
        switch (this.labelName) {
            case LabelType.CHIP_LABEL_TYPE:
                return LabelType.CHIP_LABEL_TYPE;
            case LabelType.PRODUCT_LABEL_TYPE:
                return LabelType.PRODUCT_LABEL_TYPE;
            case LabelType.REGION_LABEL_TYPE:
                return LabelType.REGION_LABEL_TYPE;
            default:
                return LabelType.CHIP_LABEL_TYPE;
        }
    }

    /**
     * @override
     */
    marshalling(messageSequence: rpc.MessageSequence): boolean {
        messageSequence.writeString(this.labelName);
        messageSequence.writeString(this.labelValue);
        return true;
    }

    /**
     * @override
     */
    unmarshalling(messageSequence: rpc.MessageSequence): boolean {
        this.labelName = messageSequence.readString();
        this.labelValue = messageSequence.readString();
        return true;
    }

    private initLabelInfo(labelInfo: LabelInfo): void {
        HiAiLog.info(TAG, "initModuleInfo object");
        if (labelInfo === null || labelInfo === undefined) {
            HiAiLog.info(TAG, "labelInfo is null");
            return;
        }
        this.labelName = labelInfo.labelName;
        this.labelValue = labelInfo.labelValue;
    }

    private initStringContent(labelInfoContent?: string): void {
        if (ObjectUtil.isEmptyText(labelInfoContent)) {
            HiAiLog.info(TAG, "labelInfoContent is null");
            return;
        }
        let labelInfoObject = JsonUtil.stringToClass(labelInfoContent) as LabelInfo;
        if (labelInfoObject === null || labelInfoObject === undefined) {
            HiAiLog.info(TAG, "moduleInfoObject is null");
            return;
        }
        this.initLabelInfo(labelInfoObject);
    }
}