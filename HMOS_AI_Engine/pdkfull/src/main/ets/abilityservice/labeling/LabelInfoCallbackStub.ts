/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import LabelInfoCallbackProxy from './LabelInfoCallbackProxy';
import rpc from '@ohos.rpc';
import InfoCallbackStub from '../../interfaces/InfoCallbackStub';
import InfoCallback from '../../interfaces/IInfoCallback'

/**
 * The stub of LabelInfoCallback.
 *
 * <AUTHOR>
 */
export default abstract class LabelInfoCallbackStub extends InfoCallbackStub {
    /**
     * The constructor.
     *
     * @param des the descriptor
     */
    constructor(des: string) {
        super(des)
    }

    /**
     * Get the proxy object.
     *
     * @param remote the remote object
     * @return the proxy object
     */
    public static asInterface(object: rpc.IRemoteObject): InfoCallback {
        if (object == null) {
            return null;
        }
        return new LabelInfoCallbackProxy(object);
    }

    /**
     * @override
     */
    abstract onResult(code: number, data: string): void;

    /**
     * @override
     */
    getMessageCode(): number {
        return LabelInfoCallbackProxy.COMMAND_ON_RESULT;
    }

    /**
     * @override
     */
    getDescriptor(): string {
        return LabelInfoCallbackProxy.DESCRIPTOR;
    }
}