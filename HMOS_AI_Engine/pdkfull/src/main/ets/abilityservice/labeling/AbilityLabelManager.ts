/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import common from '@ohos.app.ability.common';
import rpc from '@ohos.rpc';
import LabelInfo from './LabelInfo';
import HiAiLog from '../../utils/log/HiAiLog';
import HiAIServiceAbilityBridge from '../../HiAIServiceAbilityBridge';
import { Capability } from '../../interfaces/FrameworkCapability';
import LabelInfoCallbackStub from './LabelInfoCallbackStub';
import LabelInfoCallbackProxy from './LabelInfoCallbackProxy';
import JsonUtil from '../../utils/JsonUtil';
import { RunningErrorCode, ResultCode } from '../../utils/ResCode';
import { AsyncCallback } from '../../interfaces/ICallback';
import { GlobalContextGetKey, GlobalContextSetKey } from '../../utils/GlobalContext';
import { AbilityLabelInfoCallBackObject } from './AbilityLabelInfoCallBackObject';

const TAG: string = "AbilityLabelManager";
type CommonAbilityContext = common.UIAbilityContext | common.ServiceExtensionContext;

/**
 * This class is a tag management class, which is used to obtain HiAI's general labels.
 * Label definitions can be found { LabelType }.
 * @see LabelType
 * <AUTHOR>
 */
export default class AbilityLabelManager {
    /**
     * The descriptor.
     */
    public static readonly DESCRIPTOR: string = "HiAI.openapi.labelInfo";
    private static readonly REQUEST_METHOD: string = "getAllLabel";
    labels: Array<LabelInfo>;
    context: CommonAbilityContext;

    /**
     * The constructor of this
     * @class
     */
    private constructor(context: CommonAbilityContext) {
        this.context = context;
    }

    /**
     * Get an instance object of this class.
     *
     * @param { Context.AbilityContext } the ability context
     * @returns { AbilityLabelManager } the instance of AbilityLabelManager
     */
    static getInstance(context: CommonAbilityContext): AbilityLabelManager {

        return (GlobalContextSetKey("AbilityLabelManager", new AbilityLabelManager(context)) as AbilityLabelManager);
    }

    public static parseLabelInfoList(labelContent: string): Array<LabelInfo> {
        let labelInfoArray = JsonUtil.stringToClass(labelContent) as Array<LabelInfo>;
        let labelInfoList: Array<LabelInfo> = new Array<LabelInfo>();
        labelInfoArray.forEach((labelInfo: LabelInfo) => {
            let labelInfoObject: LabelInfo = new LabelInfo(labelInfo);
            labelInfoList.push(labelInfoObject);
        })
        if (AbilityLabelManager.getInstance(null) != null) {
            let instance: AbilityLabelManager = AbilityLabelManager.getInstance(null);
            instance.labels = labelInfoArray as Array<LabelInfo>;
        }
        return labelInfoArray;
    }

    /**
     * Get a list of tags associated with this device.
     * @param { AsyncCallback<Array<LabelInfo>> } the callback of labels.
     */
    getAbilityLabelWithCache(callback: AsyncCallback<Array<LabelInfo>>): void {
        HiAiLog.info(TAG, "start to ability label with cache");
        const globalContextAbilityLabelManager = (GlobalContextGetKey("AbilityLabelManager") as AbilityLabelManager);
        if (globalContextAbilityLabelManager !== null) {
            HiAiLog.info(TAG, "get label from cache");
            if (globalContextAbilityLabelManager.labels !== null && globalContextAbilityLabelManager.labels !== undefined) {
                callback(ResultCode.SUCCESS, globalContextAbilityLabelManager.labels)
                return;
            }
        }
        this.getAbilityLabel(callback);
    }

    /**
     * Get a list of tags associated with this device.
     * @param { AsyncCallback<Array<LabelInfo>> } the callback of labels.
     */
    getAbilityLabel(callback: AsyncCallback<Array<LabelInfo>>): void {
        HiAiLog.info(TAG, "start to ability label");
        let callbackRemoteObject: LabelInfoCallbackStub = new AbilityLabelInfoCallBackObject(LabelInfoCallbackProxy.DESCRIPTOR, TAG, callback)
        let labelRequest: rpc.MessageSequence = rpc.MessageSequence.create();
        labelRequest.writeInterfaceToken(AbilityLabelManager.DESCRIPTOR);
        labelRequest.writeString(AbilityLabelManager.REQUEST_METHOD);
        labelRequest.writeRemoteObject(callbackRemoteObject);
        let labelReply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.LABELING_CAPABILITY, labelRequest, labelReply);
    }
}