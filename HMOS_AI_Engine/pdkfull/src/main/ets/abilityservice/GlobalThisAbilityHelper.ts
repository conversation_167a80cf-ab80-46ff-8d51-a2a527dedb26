/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { GlobalContextGetKey, GlobalContextSetKey } from '../utils/GlobalContext';

import HiAiLog from '../utils/log/HiAiLog';

const TAG: string = "GlobalThisAbilityHelper"

/**
 * This class is used to initialize global objects related to Ability. The value is assigned by the framework.
 * <AUTHOR>
 */
export default class GlobalThisAbilityHelper {
    private static registerKeys: Array<string> = new Array<string>();

    /**
     * Initialize the global object.
     *
     * @param moduleKeys The module name
     */
    public static initModuleKey(moduleKeys: Array<string>): void {
        moduleKeys.forEach((moduleKey: string) => {
            GlobalThisAbilityHelper.registerKeys.push(moduleKey);
        });
    }

    /**
     * To mount the variable value to the globalThis instance, the key needs to be registered to registerKeys first,
     * otherwise the mount fails.
     *
     * @param key The ability name
     * @param value the global this object
     * @returns If the mount succeeds, it will return value, if it fails, it will return undefined.
     */
    public static set<T>(key: string, value: T): T {
        let element: string | undefined = GlobalThisAbilityHelper.registerKeys.find((ele: string) => ele === key);
        if (element === undefined) {
            HiAiLog.info(TAG, `Can't find register key: ${key}`);
            return undefined;
        }

        return (GlobalContextSetKey(key, value) as T);
    }

    /**
     * Get all variables mounted on global.
     *
     * @returns Returns value successfully, or returns undefined if it has not been mounted before.
     */
    public static get<T>(key: string): T {
        return (GlobalContextGetKey(key) as T);
    }
}