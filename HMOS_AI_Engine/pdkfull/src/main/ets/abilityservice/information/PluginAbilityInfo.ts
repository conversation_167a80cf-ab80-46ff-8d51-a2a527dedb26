/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc'
import HiAi<PERSON>og from '../../utils/log/HiAiLog';
import Metadata from './MetaData';

const TAG: string = "PluginAbilityInfo";

/**
 * Extension information about an ability of hiai
 * @typedef PluginAbilityInfo
 * @class
 * <AUTHOR>
 */
export default class PluginAbilityInfo implements rpc.Parcelable {
    /**
     * Indicates the ability name
     * @type { string }
     */
    name: string;

    /**
     * Indicates the name of the module
     * @type { string }
     */
    moduleName: string;

    /**
     * Indicates the name of the bundle
     * @type { string }
     */
    bundleName: string;

    /**
     * Indicates the ability type
     * @type { number }
     */
    abilityType: number;

    /**
     * Indicates whether it is visible to third-party applications
     * @type { boolean }
     */
    isVisible: boolean;

    /**
     * Indicates the metadata of ability
     * @type { Array<Metadata> }
     */
    metadata: Array<Metadata>;

    /**
     * The remote object key instance of this ability. Note that it may be empty when the ability is not
     * activated or has been destroyed.
     * @see { GlobalThisAbilityHelper }
     * @type { string }
     */
    pluginRemoteKey: string;

    /**
     * Constructor of { PluginAbilityInfo }
     * @param { PluginAbilityInfo } the abilityInfo content
     */
    public constructor(abilityInfo?: PluginAbilityInfo) {
        if (abilityInfo === null || abilityInfo === undefined) {
            return;
        }
        this.name = abilityInfo.name;
        this.moduleName = abilityInfo.moduleName;
        this.bundleName = abilityInfo.bundleName;
        this.abilityType = abilityInfo.abilityType;
        this.isVisible = abilityInfo.isVisible;
        this.metadata = abilityInfo.metadata;
        this.pluginRemoteKey = abilityInfo.pluginRemoteKey;
    }

    /**
     * @override
     */
    marshalling(messageSequence: rpc.MessageSequence): boolean {
        messageSequence.writeString(this.name);
        messageSequence.writeString(this.moduleName);
        messageSequence.writeString(this.bundleName);
        messageSequence.writeInt(this.abilityType);
        messageSequence.writeBoolean(this.isVisible);
        messageSequence.writeParcelableArray(this.metadata);
        messageSequence.writeString(this.pluginRemoteKey);
        return true;
    }

    /**
     * @override
     */
    unmarshalling(messageSequence: rpc.MessageSequence): boolean {
        this.name = messageSequence.readString();
        this.moduleName = messageSequence.readString();
        this.bundleName = messageSequence.readString();
        this.abilityType = messageSequence.readInt();
        this.isVisible = messageSequence.readBoolean();
        messageSequence.readParcelableArray(this.metadata);
        this.pluginRemoteKey = messageSequence.readString();
        return true;
    }
}