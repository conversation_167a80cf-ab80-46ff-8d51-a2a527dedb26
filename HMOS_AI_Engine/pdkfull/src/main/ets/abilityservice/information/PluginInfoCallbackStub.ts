/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import PluginInfoCallbackProxy from './PluginInfoCallbackProxy';
import rpc from '@ohos.rpc';
import InfoCallbackStub from '../../interfaces/InfoCallbackStub';
import InfoCallback from '../../interfaces/IInfoCallback'

export default abstract class PluginInfoCallbackStub extends InfoCallbackStub {
    /**
     * The constructor.
     *
     * @param des the descriptor
     */
    constructor(des: string) {
        super(des)
    }

    /**
     * Get the proxy object.
     *
     * @param remote the remote object
     * @return the proxy object
     */
    public static asInterface(object: rpc.IRemoteObject): InfoCallback {

        return new PluginInfoCallbackProxy(object);
    }

    /**
     * @override
     */
    abstract onResult(code: number, data: string): void;

    /**
     * @override
     */
    getMessageCode(): number {
        return PluginInfoCallbackProxy.COMMAND_ON_RESULT;
    }

    /**
     * @override
     */
    getDescriptor(): string {
        return PluginInfoCallbackProxy.DESCRIPTOR;
    }
}