/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import InfoCallbackProxy from '../../interfaces/InfoCallbackProxy';
import rpc from '@ohos.rpc';

export default class PluginInfoCallbackProxy extends InfoCallbackProxy {
    /**
     * The index of onFinished method.
     */
    public static readonly COMMAND_ON_RESULT: number = 0;

    /**
     * The descriptor.
     */
    public static readonly DESCRIPTOR: string = "HiAI.openapi.IPluginInfoCallback";

    /**
     * The constructor.
     *
     * @param remote the remote object
     */
    constructor(remote: rpc.IRemoteObject) {
        super(remote);
    }

    getMessageCode(): number {
        return PluginInfoCallbackProxy.COMMAND_ON_RESULT;
    }

    getDescriptor(): string {
        return PluginInfoCallbackProxy.DESCRIPTOR;
    }
}