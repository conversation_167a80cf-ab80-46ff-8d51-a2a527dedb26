/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { AsyncCallback } from '../../interfaces/ICallback';
import HiAiLog from '../../utils/log/HiAiLog';
import { ResultCode, RunningErrorCode } from '../../utils/ResCode';
import ModuleInfo from './ModuleInfo';
import PluginInfoCallbackStub from './PluginInfoCallbackStub';
import PluginInfoManager from './PluginInfoManager';

/*
 * Callback of the device label list. specific module info
 * @param { des: string, TAG: string, moduleName: string, callback: AsyncCallback<ModuleInfo> }
 * */
export class PluginInfoCallBackObjectInfo extends PluginInfoCallbackStub {
    TAG: string;
    moduleName: string;
    callback: AsyncCallback<ModuleInfo>

    constructor(des: string, tag: string, moduleName: string, callback: AsyncCallback<ModuleInfo>) {
        super(des);
        this.TAG = tag;
        this.moduleName = moduleName;
        this.callback = callback;
    }

    onResult(code: number, data: string): void {
        HiAiLog.info(this.TAG, "getModuleInfoByName callback code is " + code);
        HiAiLog.info(this.TAG, "getModuleInfoByName callback data is " + JSON.stringify(data));
        if (code === RunningErrorCode.SUCCESS) {
            let moduleInfoList: Array<ModuleInfo> = PluginInfoManager.parseModuleInfoList(data);
            let specificModuleInfo: ModuleInfo | undefined = moduleInfoList.find((moduleInfo: ModuleInfo) => moduleInfo.name === this.moduleName);
            if (specificModuleInfo === null || specificModuleInfo === undefined) {
                this.callback(ResultCode.EMPTY, null);
            } else {
                this.callback(ResultCode.SUCCESS, specificModuleInfo);
            }
            HiAiLog.info(this.TAG, "getModuleInfoByName success");
        } else {
            HiAiLog.info(this.TAG, "getModuleInfoByName fail");
            this.callback(ResultCode.getError(code), null);
        }
    }
}

/*
 * Callback for the list of installed module names.
 * @param { des: string, TAG: string, callback: AsyncCallback<Array<ModuleInfo>> }
 * */
export class PluginInfoCallBackObjectInfoStub extends PluginInfoCallbackStub {
    TAG: string;
    callback: AsyncCallback<Array<ModuleInfo>>

    constructor(des: string, tag: string, callback: AsyncCallback<Array<ModuleInfo>>) {
        super(des);
        this.TAG = tag;
        this.callback = callback;
    }

    onResult(code: number, data: string): void {
        HiAiLog.info(this.TAG, "getInstalledModuleList callback code is " + code);
        HiAiLog.info(this.TAG, "getInstalledModuleList callback data is " + JSON.stringify(data));
        if (code === RunningErrorCode.SUCCESS) {
            let moduleInfoList: Array<ModuleInfo> = PluginInfoManager.parseModuleInfoList(data);
            if (moduleInfoList === null || moduleInfoList === undefined || moduleInfoList.length === 0) {
                this.callback(ResultCode.EMPTY, null);
            } else {
                this.callback(ResultCode.SUCCESS, moduleInfoList);
            }
            HiAiLog.info(this.TAG, "getModuleInfoByName success");
        } else {
            HiAiLog.info(this.TAG, "getModuleInfoByName fail");
            this.callback(ResultCode.getError(code), null);
        }
    }
}
