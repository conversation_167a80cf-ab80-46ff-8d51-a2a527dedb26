/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc';
import <PERSON><PERSON>iLog from '../../utils/log/HiAiLog';

const TAG: string = "Metadata";

/**
 * Indicates the Metadata
 * @class
 * <AUTHOR>
 */
export default class Metadata implements rpc.Parcelable {
    /**
     * Indicates the metadata name
     * @type { string }
     */
    name: string;
    /**
     * Indicates the metadata value
     * @type { string }
     */
    value: string;
    /**
     * Indicates the metadata resource
     * @type { string }
     */
    resource: string;

    /**
     * Constructor of { Metadata }
     * @param { Metadata } the metadata content
     */
    public constructor(metadata?: Metadata) {
        if (metadata === null || metadata === undefined) {
            return;
        }
        this.name = metadata.name;
        this.value = metadata.value;
        this.resource = metadata.resource;
    }

    /**
     * @override
     */
    marshalling(messageSequence: rpc.MessageSequence): boolean {
        messageSequence.writeString(this.name);
        messageSequence.writeString(this.value);
        messageSequence.writeString(this.resource);
        return true;
    }

    /**
     * @override
     */
    unmarshalling(messageSequence: rpc.MessageSequence): boolean {
        this.name = messageSequence.readString();
        this.value = messageSequence.readString();
        this.resource = messageSequence.readString();
        return true;
    }
}