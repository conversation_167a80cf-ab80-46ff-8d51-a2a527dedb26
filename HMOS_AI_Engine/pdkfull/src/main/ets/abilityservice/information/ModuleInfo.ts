/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc'
import PluginAbilityInfo from './PluginAbilityInfo';
import HiAiLog from '../../utils/log/HiAiLog';
import JsonUtil from '../../utils/JsonUtil';
import ObjectUtil from '../../utils/ObjectUtil';
import Metadata from './MetaData';

const TAG: string = "ModuleInfo";

/**
 * Configuration information about a hap module.
 * @typedef ModuleInfo
 * @class
 * <AUTHOR>
 */
export default class ModuleInfo implements rpc.Parcelable {
    /**
     * Indicates the name of the module
     * @type { string }
     */
    name: string;

    /**
     * Indicates the name of the bundle
     * @type { string }
     */
    bundleName: string;

    /**
     * Indicates the version code of the bundle
     * @type { number }
     */
    versionCode: number;

    /**
     * Indicates the version name of the bundle
     * @type { string }
     */
    versionName: string;

    /**
     * Indicates the metadata of ability
     * @type { Array<Metadata> }
     */
    metadata: Metadata[];

    /**
     * Plugin ability information under this module.
     * @type { Array<PluginAbilityInfo> }
     */
    extensionAbilityInfo: PluginAbilityInfo[];

    /**
     * Constructor of { ModuleInfo }
     * @param { string | ModuleInfo } the metadata content
     */
    public constructor(moduleInfo?: string | ModuleInfo) {
        if (moduleInfo != null) {
            if (typeof moduleInfo === "string") {
                HiAiLog.info(TAG, "moduleInfo is string");
                this.initStringContent(moduleInfo);
            } else if (moduleInfo instanceof Object) {
                HiAiLog.info(TAG, "moduleInfo is object");
                this.initModuleInfo(moduleInfo);
            }
        }
    }

    /**
     * @override
     */
    marshalling(messageSequence: rpc.MessageSequence): boolean {
        messageSequence.writeString(this.name);
        messageSequence.writeString(this.bundleName);
        messageSequence.writeLong(this.versionCode);
        messageSequence.writeString(this.versionName);
        messageSequence.writeParcelableArray(this.metadata);
        messageSequence.writeParcelableArray(this.extensionAbilityInfo);
        return true;
    }

    /**
     * @override
     */
    unmarshalling(messageSequence: rpc.MessageSequence): boolean {
        this.name = messageSequence.readString();
        this.bundleName = messageSequence.readString();
        this.versionCode = messageSequence.readLong();
        this.versionName = messageSequence.readString();
        this.metadata = new Array<Metadata>(10).fill(new Metadata());
        messageSequence.readParcelableArray(this.metadata);
        this.extensionAbilityInfo = new Array<PluginAbilityInfo>(10).fill(new PluginAbilityInfo());
        messageSequence.readParcelableArray(this.extensionAbilityInfo);
        return true;
    }

    private initModuleInfo(moduleInfo: ModuleInfo): void {
        HiAiLog.info(TAG, "initModuleInfo object");
        if (moduleInfo === null || moduleInfo === undefined) {
            HiAiLog.info(TAG, "moduleInfo is null");
            return;
        }
        this.name = moduleInfo.name;
        this.bundleName = moduleInfo.bundleName;
        this.versionName = moduleInfo.versionName;
        this.versionCode = moduleInfo.versionCode;
        this.metadata = new Array<Metadata>();
        moduleInfo.metadata.forEach((meta: Metadata) => {
            this.metadata.push(new Metadata(meta));
        });
        this.extensionAbilityInfo = new Array<PluginAbilityInfo>();
        moduleInfo.extensionAbilityInfo.forEach((abilityInfo: PluginAbilityInfo) => {
            this.extensionAbilityInfo.push(new PluginAbilityInfo(abilityInfo));
        });
    }

    private initStringContent(moduleInfoContent?: string): void {
        if (ObjectUtil.isEmptyText(moduleInfoContent)) {
            HiAiLog.info(TAG, "moduleInfoContent is null");
            return;
        }
        let moduleInfoObject = JsonUtil.stringToClass(moduleInfoContent) as ModuleInfo;
        if (moduleInfoObject === null || moduleInfoObject === undefined) {
            HiAiLog.info(TAG, "moduleInfoObject is null");
            return;
        }
        this.initModuleInfo(moduleInfoObject);
    }
}