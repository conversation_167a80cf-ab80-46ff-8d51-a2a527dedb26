/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import Want from '@ohos.app.ability.Want';
import rpc from '@ohos.rpc';
import common from '@ohos.app.ability.common';
import HiAiLog from '../../utils/log/HiAiLog';
import HiAIServiceAbilityBridge from '../../HiAIServiceAbilityBridge';
import ObjectUtil from '../../utils/ObjectUtil';
import { Capability } from '../../interfaces/FrameworkCapability';
import ModuleInfo from './ModuleInfo';
import GlobalThisAbilityHelper from '../GlobalThisAbilityHelper';
import ServiceAbilityBridge from '../../ServiceAbilityBridge';
import PluginInfoCallbackStub from './PluginInfoCallbackStub';
import JsonUtil from '../../utils/JsonUtil';
import PluginInfoCallbackProxy from './PluginInfoCallbackProxy';
import { RunningErrorCode, ResultCode } from '../../utils/ResCode';
import { AsyncCallback } from '../../interfaces/ICallback';
import Constants from '../../utils/Constants';
import { GlobalContextGetKey, GlobalContextSetKey } from '../../utils/GlobalContext';
import { AbilityWant } from '../../interfaces/InfoManager';
import { PluginInfoCallBackObjectInfo, PluginInfoCallBackObjectInfoStub } from './PluginInfoCallBackObject';

const TAG: string = "PluginInfoManager";
type CommonAbilityContext = common.UIAbilityContext | common.ServiceExtensionContext;

/**
 * This class is a plugin information management class, which is used to obtain and store plugin related information.
 * For example, the version number, whether it is installed, etc. This plugin is equivalent to a standalone hap.
 * <AUTHOR>
 */
export default class PluginInfoManager {
    static moduleInfoList: Array<ModuleInfo>;
    /**
     * The descriptor.
     */
    public static readonly DESCRIPTOR: string = "HiAI.openapi.pluginInfo";
    private static readonly REQUEST_METHOD: string = "getModuleInfoList";
    context: CommonAbilityContext;

    /**
     * The constructor of this
     */
    constructor(context: CommonAbilityContext) {
        this.context = context;
    }

    /**
     * Get an instance object of this class.
     * @param { Context.AbilityContext } the ability context
     * @returns { PluginInfoManager } the instance of PluginInfoManager
     */
    static getInstance(context: CommonAbilityContext): PluginInfoManager {

        HiAiLog.info(TAG, "1111");
        return (GlobalContextSetKey("PluginInfoManager", new PluginInfoManager(context)) as PluginInfoManager);
    }

    public static parseModuleInfoList(moduleInfoContent: string): Array<ModuleInfo> {
        let moduleInfoArray = JsonUtil.stringToClass(moduleInfoContent) as Array<ModuleInfo>;
        let moduleInfoList: Array<ModuleInfo> = new Array<ModuleInfo>();
        moduleInfoArray.forEach((moduleInfo: ModuleInfo) => {
            let moduleInfoObject: ModuleInfo = new ModuleInfo(moduleInfo);
            moduleInfoList.push(moduleInfoObject);
        })
        PluginInfoManager.moduleInfoList = moduleInfoList as Array<ModuleInfo>;
        return moduleInfoArray;
    }

    /**
     * Get a list of tags with cache.
     * @param { AsyncCallback<ModuleInfo> } the callback of labels
     */
    getModuleInfoByNameWithCache(moduleName: string, callback: AsyncCallback<ModuleInfo>): void {
        HiAiLog.info(TAG, "start to get module info with cache");
        if (callback === null || callback === undefined) {
            HiAiLog.info(TAG, "callback is null");
            return;
        }
        if (ObjectUtil.isEmptyText(moduleName)) {
            HiAiLog.info(TAG, "moduleName is null");
            callback(ResultCode.INVALID, null);
            return;
        }
        if (!this.isCacheInfoEmpty()) {
            HiAiLog.info(TAG, "return module info with cache");
            let specificModuleInfo: ModuleInfo | undefined = PluginInfoManager.moduleInfoList.find((moduleInfo: ModuleInfo) => moduleInfo.name === moduleName);
            if (specificModuleInfo !== null && specificModuleInfo !== undefined) {
                callback(ResultCode.SUCCESS, specificModuleInfo);
                return;
            }
        }
        this.getModuleInfoByName(moduleName, callback);
    }

    /**
     * Get a list of tags associated with this device.
     * @param { AsyncCallback<ModuleInfo> } the callback of labels
     */
    getModuleInfoByName(moduleName: string, callback: AsyncCallback<ModuleInfo>): void {
        HiAiLog.info(TAG, "start to get module info by name");
        if (callback === null || callback === undefined) {
            HiAiLog.info(TAG, "callback is null");
            callback(ResultCode.INVALID, null);
            return;
        }
        if (ObjectUtil.isEmptyText(moduleName)) {
            HiAiLog.info(TAG, "moduleName is null");
            callback(ResultCode.INVALID, null);
            return;
        }
        let requestData: rpc.MessageSequence = rpc.MessageSequence.create();
        requestData.writeInterfaceToken(PluginInfoManager.DESCRIPTOR);
        requestData.writeString(PluginInfoManager.REQUEST_METHOD);

        let callbackRemoteObject: PluginInfoCallbackStub = new PluginInfoCallBackObjectInfo(PluginInfoCallbackProxy.DESCRIPTOR, TAG, moduleName, callback);
        requestData.writeRemoteObject(callbackRemoteObject.asObject());
        let reply: rpc.MessageSequence = rpc.MessageSequence.create();
        HiAIServiceAbilityBridge.getInstance(this.context)
            .sendRequestAsync(Capability.PLUGIN_INFO_MANAGER_CAPABILITY, requestData, reply);
    }

    /**
     * Get a list of installed module names with cache.
     * @param { AsyncCallback<Array<ModuleInfo>> } the callback of  module info list
     */
    getInstalledModuleListWithCache(callback: AsyncCallback<Array<ModuleInfo>>): void {
        HiAiLog.info(TAG, "start to get module info list with cache");
        if (!this.isCacheInfoEmpty()) {
            HiAiLog.info(TAG, "return module info list with cache");
            let moduleInfoList: Array<ModuleInfo> = PluginInfoManager.moduleInfoList;
            if (moduleInfoList !== null && moduleInfoList !== undefined && moduleInfoList.length !== 0) {
                callback(ResultCode.SUCCESS, PluginInfoManager.moduleInfoList);
                return;
            }
        }
        this.getInstalledModuleList(callback);
    }

    /**
     * Get a list of installed module names.
     * @param { AsyncCallback<Array<ModuleInfo>> } the callback of  module info list
     */
    getInstalledModuleList(callback: AsyncCallback<Array<ModuleInfo>>): void {
        HiAiLog.info(TAG, "start to get module info list");
        let requestData: rpc.MessageSequence = rpc.MessageSequence.create();
        requestData.writeInterfaceToken(PluginInfoManager.DESCRIPTOR);
        requestData.writeString(PluginInfoManager.REQUEST_METHOD);
        let reply: rpc.MessageSequence = rpc.MessageSequence.create();
        let callbackRemoteObject: PluginInfoCallbackStub = new PluginInfoCallBackObjectInfoStub(PluginInfoCallbackProxy.DESCRIPTOR, TAG, callback)
        requestData.writeRemoteObject(callbackRemoteObject.asObject());
        HiAIServiceAbilityBridge.getInstance(this.context).sendRequestAsync(Capability.PLUGIN_INFO_MANAGER_CAPABILITY, requestData, reply);
    }

    /**
     * A remote object used to obtain a certain capability. Note that this method is only used internally by the app.
     * @param context { context: Context.AbilityContext } the ability context
     * @param abilityName { string } the ability name
     * @param moduleName { string } the module name
     * @returns { Promise<rpc.IRemoteObject> } the instance of remote object
     */
    async getAbilityRemote(context: CommonAbilityContext, abilityName: string, moduleName: string): Promise<rpc.IRemoteObject> {
        HiAiLog.info(TAG, "222");
        let abilityRemoteObject = (GlobalThisAbilityHelper.get<rpc.IRemoteObject>(abilityName)) as rpc.IRemoteObject;
        if (abilityRemoteObject !== undefined) {
            HiAiLog.info(TAG, "Returns remote from cache");
            return abilityRemoteObject;
        } else {
            let abilityWant: AbilityWant = {
                bundleName: Constants.HIAI_PACKAGE_NAME,
                moduleName: moduleName,
                abilityName: abilityName
            };
            let abilityBridge: ServiceAbilityBridge = new ServiceAbilityBridge(context, abilityWant);
            return await abilityBridge.connectService();
        }
    }

    private isCacheInfoEmpty(): boolean {
        let globalContextPluginInfoManager = (GlobalContextGetKey("PluginInfoManager")) as PluginInfoManager;
        if (globalContextPluginInfoManager !== null && globalContextPluginInfoManager !== undefined) {
            if (PluginInfoManager.moduleInfoList !== null && PluginInfoManager.moduleInfoList !== undefined) {
                HiAiLog.info(TAG, "cache is not empty");
                return false;
            }
            HiAiLog.info(TAG, "cache is empty");
            return true;
        }
        HiAiLog.info(TAG, "cache is empty");
        return true;
    }
}