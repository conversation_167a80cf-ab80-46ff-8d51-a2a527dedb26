/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import rpc from '@ohos.rpc'

/**
 * Information class for registration with the framework. The information of the caller and the reported Ability
 * information must be assigned before use.
 * <AUTHOR>
 */
export default class RegisterFrameworkInfo implements rpc.Parcelable {
    callerUid: string;
    callerPid: string;
    moduleName: string;
    abilityName: string;

    /**
     * Constructor of RegisterFrameworkInfo
     * @param callerUid { string } the uid of caller
     * @param callerPid { string } the pid of caller
     * @param moduleName { string } the module name of HiAI
     * @param abilityName { string } the ability name of HiAI
     */
    constructor(callerUid: string, callerPid: string, moduleName: string, abilityName: string) {
        this.callerUid = callerUid;
        this.callerPid = callerPid;
        this.moduleName = moduleName;
        this.abilityName = abilityName;
    }

    /**
     * @override
     */
    marshalling(messageSequence: rpc.MessageSequence): boolean {
        messageSequence.writeString(this.callerUid);
        messageSequence.writeString(this.callerPid);
        messageSequence.writeString(this.moduleName);
        messageSequence.writeString(this.abilityName);
        return true;
    }

    /**
     * @override
     */
    unmarshalling(messageSequence: rpc.MessageSequence): boolean {
        this.callerUid = messageSequence.readString();
        this.callerPid = messageSequence.readString();
        this.moduleName = messageSequence.readString();
        this.abilityName = messageSequence.readString();
        return true;
    }
}