/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import BundleManager from '@ohos.bundle.bundleManager'
import HashMap from '@ohos.util.HashMap';
import HiAiLog from '../../utils/log/HiAiLog';
import ObjectUtil from '../../utils/ObjectUtil';
import { ResultCode, RunningErrorCode } from '../../utils/ResCode';
import PluginAbilityInfo from '../../abilityservice/information/PluginAbilityInfo';
import ModuleInfo from '../../abilityservice/information/ModuleInfo';
import MetaData from '../../abilityservice/information/MetaData';
import { AsyncCallback } from '../../interfaces/ICallback';
import { BusinessError } from '../../interfaces/ICallback';
import {
    GlobalContextGetKey,
    GlobalContextSetKey,
    GlobalContextUpdateKey
} from '../../utils/GlobalContext';
import Metadata from '../../abilityservice/information/MetaData';
import bundleManager from '@ohos.bundle.bundleManager'

const TAG: string = "BundleResourceManager";

/**
 * Management class for obtaining application-related information.
 * <AUTHOR>
 */
export default class BundleResourceManager {
    private static instance: BundleResourceManager | null = null;
    isInitSuccess: boolean | null = null;
    bundleInfo: BundleManager.BundleInfo | null = null;
    private moduleInfoMap = new HashMap<string, ModuleInfo>();

    /**
     * The constructor of this
     */
    private constructor() {
        this.initBundleInfo((error, data) => {
            HiAiLog.info(TAG, "initBundleInfo over ");
        });
    }

    /**
     * Get an instance object of this class.
     *
     * @returns { BundleResourceManager } the instance of BundleResourceManager
     */
    public static getInstance(): BundleResourceManager {
        return (GlobalContextSetKey("BundleResourceManager", new BundleResourceManager()) as BundleResourceManager);
    }

    /**
     * Get information list of all modules.
     * @return { Array<ModuleInfo> } Module Information List
     */
    getModuleInfoList(callback: AsyncCallback<Array<ModuleInfo>>): void {
        HiAiLog.info(TAG, "getModuleInfoList start ");
        let moduleInfoList: ModuleInfo[] = new Array<ModuleInfo>();
        this.initBundleInfo((error: BusinessError, data: BundleManager.BundleInfo | null): void => {
            if (data) {
                let moduleInfoMap: HashMap<string, ModuleInfo> = this.convertBundleInfo(data);
                moduleInfoMap.forEach((value: ModuleInfo | undefined): void => {
                    if (value) {
                        moduleInfoList.push(value);
                    }
                });
            }
            callback(ResultCode.INVALID, moduleInfoList);
        });
    }

    private initBundleInfo(callback: AsyncCallback<BundleManager.BundleInfo>): void {
        let bundleManagerInstance: BundleResourceManager = GlobalContextGetKey('BundleResourceManager') as BundleResourceManager;
        if (bundleManagerInstance != undefined || bundleManagerInstance != null) {
            if (bundleManagerInstance.bundleInfo != null && callback != null) {
                HiAiLog.info(TAG, "callback with cache");
                callback(ResultCode.INVALID, bundleManagerInstance.bundleInfo);
                return;
            }
        }
        try {
            let error: BusinessError = {
                code: RunningErrorCode.SUCCESS
            };
            let bundleFlags: number = BundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION
                | BundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_HAP_MODULE
                | BundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_EXTENSION_ABILITY
                | BundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_METADATA;
            BundleManager.getBundleInfoForSelf(bundleFlags).then((data: BundleManager.BundleInfo): void => {
                GlobalContextUpdateKey('uid', data.appInfo.uid);
                HiAiLog.info(TAG, "Get bundle info data " + JSON.stringify(data));
                if (ObjectUtil.isNullObject(data)) {
                    HiAiLog.info(TAG, "Get bundle info failed!");
                    this.isInitSuccess = false;
                    error.code = RunningErrorCode.QUERY_FAILED_ID;
                    callback(error, null);
                    return;
                }
                this.isInitSuccess = true;
                this.bundleInfo = data;
                this.moduleInfoMap = this.convertBundleInfo(this.bundleInfo);
                callback(error, this.bundleInfo);
            })
        } catch (error) {
            HiAiLog.info(TAG, "get bundle info err=" + JSON.stringify(error));
        }
    }

    private convertBundleInfo(bundleInfo: BundleManager.BundleInfo): HashMap<string, ModuleInfo> {
        let moduleInfoMap: HashMap<string, ModuleInfo> = new HashMap<string, ModuleInfo>();
        bundleInfo.hapModulesInfo.forEach((hapModuleInfo: bundleManager.HapModuleInfo): void => {
            let moduleInfo: ModuleInfo = new ModuleInfo();
            if (this.bundleInfo) {
                moduleInfo.versionName = this.bundleInfo.versionName;
                moduleInfo.versionCode = this.bundleInfo.versionCode;
                moduleInfo.bundleName = this.bundleInfo.name;
            }
            moduleInfo.name = hapModuleInfo.name;
            moduleInfo.metadata = new Array<MetaData>();
            hapModuleInfo.metadata.forEach(meta => {
                moduleInfo.metadata.push(this.convertMetaData(meta));
            });
            moduleInfo.extensionAbilityInfo = new Array<PluginAbilityInfo>();
            hapModuleInfo.extensionAbilitiesInfo.forEach(abilityInfo => {
                moduleInfo.extensionAbilityInfo.push(this.convertAbilityData(abilityInfo));
            });
            moduleInfoMap.set(moduleInfo.name, moduleInfo);
        });
        return moduleInfoMap;
    }

    private convertMetaData(meta: BundleManager.Metadata): MetaData {
        let moduleMeta: MetaData = new MetaData();
        moduleMeta.name = meta.name;
        moduleMeta.value = meta.value;
        moduleMeta.resource = meta.resource;
        return moduleMeta;
    }

    private convertAbilityData(abilityInfo: BundleManager.ExtensionAbilityInfo): PluginAbilityInfo {
        let pluginAbilityInfo: PluginAbilityInfo = new PluginAbilityInfo();
        pluginAbilityInfo.name = abilityInfo.name;
        pluginAbilityInfo.bundleName = abilityInfo.bundleName;
        pluginAbilityInfo.moduleName = abilityInfo.moduleName;
        pluginAbilityInfo.pluginRemoteKey = abilityInfo.name;
        pluginAbilityInfo.abilityType = abilityInfo.extensionAbilityType;
        pluginAbilityInfo.metadata = new Array() as Array<Metadata>;
        abilityInfo.metadata.forEach(meta => {
            pluginAbilityInfo.metadata.push(this.convertMetaData(meta));
        });
        pluginAbilityInfo.isVisible = abilityInfo.exported;
        return pluginAbilityInfo;
    }
}