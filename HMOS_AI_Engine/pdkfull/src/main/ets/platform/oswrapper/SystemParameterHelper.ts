/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import SystemParameter from '@ohos.systemparameter';
import DeviceInfo from '@ohos.deviceInfo';
import HiAILog from '../../utils/log/HiAiLog';
import ObjectUtil from '../../utils/ObjectUtil';
import Constants from '../../utils/Constants';
import power from '@ohos.power';
import i18n from '@ohos.i18n';
import intl from '@ohos.intl';
import HiAiLog from '../../utils/log/HiAiLog';

const TAG: string = "SystemParameter";
const CHIP_PARA = "ohos.boot.hardware";

/**
 * Tool class for obtaining system properties or parameters.
 * <AUTHOR>
 */
export default class SystemParameterHelper {
    static chipLabel: string | undefined = undefined;
    private static readonly PRODUCT_PARAM: string = "const.product.name";
    private static readonly HARMONYOS_TAG: string = "HarmonyOS_";

    /**
     * Get the chip type of the device.
     * @return { string } the chip type
     */
    static getChipType(): string {
        if (!ObjectUtil.isNullObject(SystemParameterHelper.chipLabel)) {
            HiAILog.info(TAG, "return chip label from cache");
            return SystemParameterHelper.chipLabel;
        }
        try {
            let chipPara: string = SystemParameter.getSync(CHIP_PARA);
            SystemParameterHelper.chipLabel = chipPara;
            return chipPara;
        } catch (e) {
            HiAILog.error(TAG, "get chip label unexpected error");
            return '';
        }
    }

    /**
     * Get device model.
     *
     * @return the device model
     */
    public static getDeviceModel(): string {
        return SystemParameter.getSync(SystemParameterHelper.PRODUCT_PARAM, Constants.EMPTY_STRING);
    }

    /**
     * Get device type.
     *
     * @return the device type
     */
    public static getDeviceType(): string {
        return DeviceInfo.deviceType;
    }

    /**
     * Get ohos version.
     *
     * @return the ohos version
     */
    public static getOhosVersion(): string {
        return DeviceInfo.osFullName;
    }

    /**
     * Get brand.
     *
     * @return the brand
     */
    public static getBrand(): string {
        return DeviceInfo.brand;
    }

    /**
     * Get manufacturer.
     *
     * @return the manufacturer
     */
    public static getManufacturer(): string {
        return DeviceInfo.manufacture;
    }

    /**
     * Checks whether the screen of a device is on or off.
     *
     * @return Returns true if the screen is on; returns false otherwise.
     */
    public static isScreenOn(): boolean {
        let isScreenOn: boolean = power.isActive();
        HiAiLog.info(TAG, 'isScreenOn is:' + isScreenOn);
        return isScreenOn;
    }

    /**
     * Get device language.
     *
     * @return the device language, default is zh-CN
     */
    public static getLanguage(): string {
        let locale: intl.Locale = new intl.Locale(i18n.System.getSystemLanguage());
        return locale.language + Constants.UNDERLINE + i18n.System.getSystemRegion();
    }
}