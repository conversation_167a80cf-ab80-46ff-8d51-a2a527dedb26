/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { ModuleConfig, MessageConfig, AppBaseConfigs } from '@hms-ai/aiappbase'

/**
 * 定义模块间tell消息名称, 可以自行定义
 */
const tellMessage = {}

/**
 * 定义模块间ask消息名称, 可以自行定义
 */
const askMessage = {}

/**
 * 定义消息转发规则
 */
const moduleConfigInfo: ModuleConfig[] = []

/**
 * 模块配置文件，配置各个模块的实现类以及运行线程
 */
const messageConfigInfo: MessageConfig[] = []


/**
 * 基础框架配置
 */
const appBaseConfigs: AppBaseConfigs = {
    moduleConfigInfo: moduleConfigInfo,
    messageConfigInfo: messageConfigInfo,
}

export { tellMessage, askMessage, appBaseConfigs, moduleConfigInfo }