/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { ResourceManager } from "./ResourceManager"
import { ValuesBucket } from "@ohos.data.ValuesBucket";
import { AiLog } from "../logmanager/AiLog";
import common from '@ohos.app.ability.common';
import { BusinessError } from "@ohos.base";
import * as UpgradeConstants from "./UpgradeConstants";
import fs from '@ohos.file.fs';
import MessageUtil from './MessageUtil';
import UpgradeSdkManager from './UpgradeSdkManager';
import UpgradeUtils from './UpgradeUtils';

export type commonContext = common.UIAbilityContext | common.ServiceExtensionContext;


/**
 * 升级管理类UpgradeManager
 *
 * 负责升级的核心处理逻辑，管理资源的订阅和更新，需要应用自己继承并实现
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
export abstract class UpgradeManagerBase {
  //应用TAG，用于打印日志
  private TAG: string;
  //应用领域范围，用于打印日志
  private domain: number;
  //应用上下文
  private context: commonContext;
  //订阅信息的列表
  private subscribeInfoList: Array<UpgradeConstants.SubscribeInfo>;
  //存放资源的目录
  private resourceDir: string;
  //数据库管理类
  private dataBase: ResourceManager;
  // 是否是真机测试
  private realMachineTestRes: boolean;
  //有新资源的返回码
  private static readonly UPDATE_CODE: number = 1;
  //成功的返回码
  private static readonly SUCCESS_CODE: number = 0;
  //失败的返回码
  private static readonly FAILED_CODE: number = -1;
  //数据库存放版本信息数量的上限
  private static readonly RES_VERSION_LIMIT: number = 3;
  //数据库存放测试版本信息数量的上限
  private static readonly TEST_RES_VERSION_LIMIT: number = 1;
  //统一sdk分发消息接受回调成功的消息码
  private static readonly CALLBACK_SUCCESS_CODE: number = 1;
  //查询全量版本，列表最后一项为更新时间或'no version'，计算版本数量时应该-1
  private static readonly SUB_NUM: number = 1;
  //默认不支持流量下载
  private static readonly DEFAULT_NET: number = 0;
  //默认的初始版本
  private static readonly DEFAULT_VERSION: string = "0x00000000";
  //真机测试标识
  private static readonly REAL_MACHINE_TEST_RES: string = 'realMachineTestRes';
  //下载资源的最大时延
  private static readonly TIMEOUT: number = 330000;

  /**
   * UpgradeManager类构造器
   */
  constructor(context: commonContext) {
    this.TAG = this.getTag();
    this.domain = this.getDomain();
    this.context = context;
  }

  /**
   * 获取子类的标签用于日志打印
   *
   * @returns { string } 子类的标签
   */
  protected abstract getTag(): string;

  /**
   * 获取子类的domain用于日志打印
   *
   * @returns { number } 子类的domain
   */
  protected abstract getDomain(): number;

  /**
   * 获取上下文
   *
   * @returns { commonContext } 子类上下文
   */
  protected abstract getContext(): commonContext;


  /**
   * 升级通知，当升级管理检测到有新版本时，传入版本信息并调用此方法，需业务app实现升级逻辑
   *
   * @params { UpgradeConstants.updateMessage } message 资源信息
   */
  protected abstract onUpdate(message: UpgradeConstants.updateMessage): void;

  /**
   * 初始化升级管理，对参数进行校验并创建资源管理数据库
   *
   * @params { Array<UpgradeConstants.SubscribeInfo> } subscribeInfoList 订阅信息列表
   * @return { Promise<boolean> } 初始化成功或失败
   */
  public async init(subscribeInfoList: Array<UpgradeConstants.SubscribeInfo>): Promise<boolean> {
    const methodName: string = "init";
    if (!subscribeInfoList || !subscribeInfoList.length){
      AiLog.error(this.domain, methodName, this.TAG, `the subscribeInfoList is null, please check the param.`);
      return false;
    }
    for (let subscribeInfo of subscribeInfoList) {
      if (!subscribeInfo.bundleName || !subscribeInfo.serviceName || !subscribeInfo.resId || !subscribeInfo.domain ) {
        AiLog.error(this.domain, methodName, this.TAG, `No.${subscribeInfoList.indexOf(subscribeInfo)} subscribeInfo is
           invalid, please check the param.`);
        return false;
      }
      subscribeInfo.isSupportMobileNet = subscribeInfo.isSupportMobileNet ?? UpgradeManagerBase.DEFAULT_NET;
      subscribeInfo.resVersion = subscribeInfo.resVersion ?? UpgradeManagerBase.DEFAULT_VERSION;
      subscribeInfo.realMachineTestRes = subscribeInfo.realMachineTestRes ?? false;
      let pattern = new RegExp("^0x\\d+");
      if (!pattern.test(subscribeInfo.resVersion)) {
        AiLog.error(this.domain, methodName, this.TAG, `the resVersion of ${subscribeInfo.resId} is invalid, please
          check the param.`);
        return false;
      }
    }
    //创建存放资源文件的目录
    let filePath = this.context.filesDir + '/Upgrade_Resource_File';
    try {
      if(!fs.accessSync(filePath)){
        fs.mkdirSync(filePath);
        AiLog.info(this.domain, methodName, this.TAG, `create directory success`);
      }
    } catch (error) {
      let err: BusinessError =error as BusinessError;
      AiLog.error(this.domain, methodName, this.TAG, `create directory failed with err code: ${err.code}, err message:
        ${err.message}`);
      return false;
    }
    this.resourceDir = filePath;
    this.subscribeInfoList = subscribeInfoList;
    this.dataBase = ResourceManager.getInstance(this.context);
    // 初始化真机测试标识realMachineTestRes
    this.realMachineTestRes = await this.dataBase.getKV(UpgradeManagerBase.REAL_MACHINE_TEST_RES) as boolean;
    if(this.realMachineTestRes === undefined) {
      this.realMachineTestRes = subscribeInfoList[0]?.realMachineTestRes;
    }
    return await this.dataBase.initDatabase();
  }

  /**
   * 注册资源与注册监听，通过检查本地数据库判断是否已经注册，若已经注册则不再重复注册。
   *
   * @return {Promise<boolean>} 注册成功或失败
   */
  public async subscribe(): Promise<boolean> {
    const methodName: string = "subscribe";
    AiLog.info(this.domain, methodName, this.TAG, "Initialize UpgradeManager, subscribe upgrade information");
    if (!this.dataBase){
      AiLog.error(this.domain, methodName, this.TAG, "the database is null, please init first!");
      return false;
    }
    let successList: Array<string> = new Array();
    let failedList: Array<string> = new Array();
    for (let subscribeInfo of this.subscribeInfoList) {
      let subInfo = await this.dataBase.checkSubscribeInfo(subscribeInfo.resId, subscribeInfo.originId);
      //检查数据库中是否存在当前资源id的订阅信息
      if (!subInfo) {
        let isSubscribeSuccess: boolean = false;
        let isSubscribeDataItemSuccess: boolean = false;
        await UpgradeSdkManager.SubscribeThroughSdk(this.context, JSON.stringify(MessageUtil.getSubscribeMessage(subscribeInfo))).then((res:number) => {
          isSubscribeSuccess = (res === UpgradeManagerBase.CALLBACK_SUCCESS_CODE) ? true : false;
        })
        await UpgradeSdkManager.SubscribeThroughSdk(this.context, JSON.stringify(MessageUtil.getSubscribeDataItemMessage(subscribeInfo, true))).then((res:number) => {
          isSubscribeDataItemSuccess = (res === UpgradeManagerBase.CALLBACK_SUCCESS_CODE) ? true : false;
        })
        AiLog.info(this.domain, methodName, this.TAG, `subscribe result: subscribeResource: ${isSubscribeSuccess},
          subscribeItem: ${isSubscribeDataItemSuccess}`);
        //如果订阅成功，或无需订阅，则向本地数据库添加订阅信息。
        if (isSubscribeSuccess && isSubscribeDataItemSuccess) {
          await this.dataBase.addInfo(UpgradeConstants.subscribeInfoTable.TABLE, UpgradeUtils.getSubValue(subscribeInfo));
          successList.push(subscribeInfo.resId);
        } else {
          failedList.push(subscribeInfo.resId);
        }
      } else {
        AiLog.info(this.domain, methodName, this.TAG, "subInfo existed, already subscribed before");
        successList.push(subscribeInfo.resId);
      }
    }
    if (successList.length === this.subscribeInfoList.length) {
      AiLog.info(this.domain, methodName, this.TAG, "all resId subscribe success");
      return true;
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `subscribe failed, failed resId: ${failedList}`);
      return false;
    }
  }

  /**
   * 取消注册资源与取消注册监听, 必须之前已经注册过资源。
   * 取消注册资源后需要重新初始化，传入新的资源列表。
   *
   * @params { Array<UpgradeConstants.SubscribeInfo> } subscribeList 需要取消注册的资源列表
   * @params { boolean } deleteFile 是否需要删除取消注册的资源文件
   * @return { Promise<boolean> } 取消注册的结果
   */
  public async unSubscribe(subscribeInfoList: Array<UpgradeConstants.SubscribeInfo>, deleteFile: boolean): Promise<boolean> {
    const methodName: string = "subscribe";
    AiLog.info(this.domain, methodName, this.TAG, "begin to unSubscribe");
    if (!this.dataBase){
      AiLog.error(this.domain, methodName, this.TAG, "the database is null, please init first!");
      return false;
    }
    for (let subInfo of subscribeInfoList) {
      let sub = await this.dataBase.checkSubscribeInfo(subInfo.resId, subInfo.originId);
      if (!sub) {
        AiLog.warn(this.domain, methodName, this.TAG, `the resId: ${subInfo.resId}, originId: ${subInfo.originId} has not been subscribed or has been unsubscribed
          before, no need to unsubscribe`);
        continue;
      }
      let unSub: boolean = false;
      let unSubItem: boolean = false;
      await UpgradeSdkManager.SubscribeThroughSdk(this.context, JSON.stringify(MessageUtil.getUnSubscribeMessage(subInfo))).then((res: number) => {
        unSub = (res === UpgradeManagerBase.CALLBACK_SUCCESS_CODE) ? true : false;
      })
      await UpgradeSdkManager.SubscribeThroughSdk(this.context, JSON.stringify(MessageUtil.getSubscribeDataItemMessage(subInfo, false))).then((res: number) => {
        unSubItem = (res === UpgradeManagerBase.CALLBACK_SUCCESS_CODE) ? true : false;
      })
      if (!unSub || !unSubItem) {
        AiLog.error(this.domain, methodName, this.TAG, "unSubscribe failed, please check the param");
        return false;
      }
      await this.dataBase.deleteSubInfo(subInfo.resId, subInfo.originId);
      if (deleteFile) {
        let flag = true;
        while (flag) {
          let filePath = await this.dataBase.deleteInfo(subInfo.resId, this.realMachineTestRes + "", true, subInfo.originId);
          if (filePath) {
            try {fs.unlinkSync(filePath)} catch (e) {
              let err = e as BusinessError
              AiLog.error(this.domain, methodName, this.TAG, `delete file failed with code: ${err.code}, ${err.message}`);
            }
          } else {
            flag = false;
          }
        }
        await this.dataBase.deleteKV(UpgradeManagerBase.REAL_MACHINE_TEST_RES);
        AiLog.info(this.domain, methodName, this.TAG, "delete all file success");
      }
    }
    return true;
  }

  /**
   * 查询资源版本信息
   *
   * @params { string } resId 资源id
   * @return { Promise<UpgradeConstants.QueryResult> } 查询返回信息
   */
  public async query(resId: string, originId?: string): Promise<UpgradeConstants.QueryResult> {
    const methodName: string = "query";
    let result: UpgradeConstants.QueryResult;
    AiLog.info(this.domain, methodName, this.TAG, `begin to query resource veriosn`);
    if (!this.dataBase){
      AiLog.error(this.domain, methodName, this.TAG, "the database is null, please init first!");
      result = {code: UpgradeManagerBase.FAILED_CODE, message: "the database is null, please init first!"};
      return result;
    }
    let curVersion = await this.dataBase.getCurrentVersion(resId, this.realMachineTestRes + "", originId);
    let curVer: string;
    if (curVersion) {
      curVer = originId ? curVersion.version : curVersion.resVersion
    } else {
      curVer = "no current version"
    }
    let versionList = await this.dataBase.getAllVersion(resId, this.realMachineTestRes + "", originId) as Array<string>;
    if (versionList.length - UpgradeManagerBase.SUB_NUM) {
      result = {
        code: UpgradeManagerBase.SUCCESS_CODE,
        message: "query success",
        data: {
          resId: resId,
          originId: originId ?? "",
          currentVersion: curVer,
          updateTime: versionList.pop(),
          versionList: versionList,
          realMachineTestRes: this.realMachineTestRes
        }
      }
    } else {
      result = {code: UpgradeManagerBase.FAILED_CODE, message: "no target res in the database"}
    }
    return result;
  }

  /**
   * 获取资源文件
   *
   * @params { string } resId 资源id
   * @params { string } version 资源版本
   * @params { string } path 资源保存的路径
   * @params { string } fileName 资源保存的文件名
   * @return { Promise<UpgradeConstants.getFileResult> } 获取文件的返回信息
   */
  public async getFile(resId: string, version: string, path: string, fileName: string, originId?: string): Promise<UpgradeConstants.getFileResult> {
    const methodName: string = "getFile";
    AiLog.info(this.domain, methodName, this.TAG, "begin to getFile");
    let result: UpgradeConstants.getFileResult;
    if (!this.dataBase){
      AiLog.error(this.domain, methodName, this.TAG, "the database is null, please init first!");
      result = {code: UpgradeManagerBase.FAILED_CODE, message: "the database is null, please init first!"};
      return result;
    }
    let cur = await this.dataBase.getCurrentVersion(resId, this.realMachineTestRes + "", originId) as UpgradeConstants.resVersionInfo;
    let tar = await this.dataBase.getTargetVersion(resId, version, this.realMachineTestRes + "", originId) as UpgradeConstants.resVersionInfo;

    if (!tar) {
      AiLog.error(this.domain, methodName, this.TAG, `target version do not exist in the database`);
      result = {code: UpgradeManagerBase.FAILED_CODE, message: "target version do not exist in the database"};
      return result;
    }
    try{
      //如果目标路径不存在，则创建目录，支持创建多级目录
      if (!fs.accessSync(path)) {
        fs.mkdirSync(path,true)
      }
      let desPath: string = path + `/${fileName}.zip`;
      //如果目标路径下该文件已存在，则报错
      if (fs.accessSync(desPath)) {
        AiLog.error(this.domain, methodName, this.TAG, `the path and fileName already existed`)
        result = {
          code: UpgradeManagerBase.FAILED_CODE,
          message: "the path and fileName already existed"
        }
      } else {
        fs.copyFileSync(tar.filePath, desPath);
        if (!this.realMachineTestRes) {
          if (cur !== undefined) {
            let curValue = UpgradeUtils.getResValue(cur, "false", "");
            let curVersion = originId ? cur.version : cur.resVersion;
            await this.dataBase.updateInfo(curValue, resId, curVersion, this.realMachineTestRes + "", originId);
          }
          let tarValue = UpgradeUtils.getResValue(tar, "true", "");
          await this.dataBase.updateInfo(tarValue, resId, version, this.realMachineTestRes + "", originId);
        }
        result = {
          code: UpgradeManagerBase.SUCCESS_CODE,
          message: "get file success",
          data: {resId: tar.resId, originId: originId ?? "", version: version, realMachineTestRes: tar.realMachineTestRes}
        }
      }
    } catch(e){
      let err: BusinessError = e as BusinessError
      result = {code: UpgradeManagerBase.FAILED_CODE, message: err.message}
      AiLog.error(this.domain, methodName, this.TAG, `copyfile failed with err code${err.code}, err message: ${err.message}`)
    }
    return result;
  }

  /**
   * 版本更新，检查本地最新版本和最新版本，若两者不一致则执行更新，否则不执行更新；入参为upgradeType.LOCAL则检查数据服务端侧版本，为upgrade.CLOUD则检查IDS云侧版本
   *
   * @params { UpgradeConstants.upgradeType } type - the upgrade type that decides whether download from cloud.
   * @params { string } notifyMessage? - the message that sent by aiDataService when resources is upgraded.
   * @returns { Promise<UpgradeConstants.UpgradeResult> } 返回更新结果
   */
  public async upgrade(type: UpgradeConstants.upgradeType, notifyMessage?: string): Promise<void> {
    const methodName: string = "upgrade";
    AiLog.info(this.domain, methodName, this.TAG, `begin to check upgrade, the type is ${type}`);
    if(!this.dataBase) {
      AiLog.error(this.domain, methodName, this.TAG, "the database is null, please init first!");
      this.onUpdate(MessageUtil.getUpdateMessage(UpgradeManagerBase.FAILED_CODE, "get rdbStore failed, please init fist.", type))
      return;
    }
    let notifyRes: UpgradeConstants.resVersionInfo | void = undefined;
    if (notifyMessage) {
      notifyRes = this.getResInfo(notifyMessage);
      if (!notifyRes) {
        AiLog.error(this.domain, methodName, this.TAG, "parse notifyMessage failed");
        this.onUpdate(MessageUtil.getUpdateMessage(UpgradeManagerBase.FAILED_CODE, "parse notifyMessage failed.", type))
        return;
      }
    }
    for (let subscribeInfo of this.subscribeInfoList) {
      AiLog.info(this.domain, methodName, this.TAG, `begin to check upgrade, resId: ${subscribeInfo.resId}, originId: ${subscribeInfo.originId}`);
      let filePath: string = this.resourceDir;
      let idsLatestInfo: UpgradeConstants.resVersionInfo;
      //先检查是否与notifyMessage的资源一致，如果一致则直接copy,否则根据type检查端侧或云侧的更新
      if (!notifyRes || !UpgradeUtils.checkSubInfo(subscribeInfo, notifyRes)) {
        let idsLocalVersion = await this.QueryLocal(subscribeInfo);
        if (!idsLocalVersion) {
          AiLog.error(this.domain, methodName, this.TAG, `query local resource info failed, resId: ${subscribeInfo.resId}`);
          this.onUpdate(MessageUtil.getUpdateMessage(UpgradeManagerBase.FAILED_CODE, "query resVersion from aiDataService failed.", type));
          return;
        }
        if (type === UpgradeConstants.upgradeType.CLOUD) {
          let downloadResult: boolean = await this.Download(subscribeInfo, idsLocalVersion.realMachineTestRes + "");
          if (downloadResult) {
            idsLocalVersion = await this.QueryLocal(subscribeInfo);
          }
        }
        filePath += UpgradeUtils.getPath(idsLocalVersion as UpgradeConstants.resVersionInfo);
        idsLatestInfo = idsLocalVersion as UpgradeConstants.resVersionInfo;
      } else {
        filePath += UpgradeUtils.getPath(notifyRes);
        idsLatestInfo = notifyRes;
      }
      // 刷新realMachineTestRes
      let typeSwitch = false;
      let isTestRes = idsLatestInfo?.realMachineTestRes;
      if(isTestRes !== undefined && isTestRes !== this.realMachineTestRes) {
        // 如果由测试态转为正式态, 删除测试数据
        if(!isTestRes) {
          let deletePath: string = await this.dataBase.deleteInfo(idsLatestInfo.resId, this.realMachineTestRes + "", false, idsLatestInfo.originId) as string;
          try {fs.unlinkSync(deletePath)} catch (e) {
            let err = e as BusinessError
            AiLog.error(this.domain, methodName, this.TAG, `delete file fialed with message: ${err.message}`);
          }
        }
        // 如果IDS主动通知报文中的realMachineTestRes或查询报文中的状态改变, 变更本地的realMachineTestRes
        AiLog.error(this.domain, methodName, this.TAG, `realMachineTestRes change, realMachineTestRes: ${this.realMachineTestRes} -> ${isTestRes}`);
        this.realMachineTestRes = isTestRes;
        this.dataBase.setKV(UpgradeManagerBase.REAL_MACHINE_TEST_RES, isTestRes);
        typeSwitch = true;
      }
      let idsLatestVersion = (subscribeInfo.originId) ? idsLatestInfo.version : idsLatestInfo.resVersion;
      //获取本地最新版本信息
      let localLatestInfo = await this.dataBase.getLatestVersion(subscribeInfo.resId, this.realMachineTestRes + "", subscribeInfo.originId);
      let localLatestVersion: string = subscribeInfo.resVersion
      if (localLatestInfo) {
        localLatestVersion = (subscribeInfo.originId) ? localLatestInfo.version : localLatestInfo.resVersion;
      }
      if (typeSwitch || (type === UpgradeConstants.upgradeType.NOTIFY) || isTestRes || (idsLatestVersion && (localLatestVersion !== idsLatestVersion))) {
        AiLog.info(this.domain, methodName, this.TAG, `upgrade with local verison: ${localLatestVersion}, IDS version: ${idsLatestVersion}`);
        await this.upgradeRes(subscribeInfo, idsLatestInfo, filePath, type, typeSwitch);
      } else {
        AiLog.info(this.domain, methodName, this.TAG, "the local resource version is consistent with the latest version, no need to upgrade, " +
          `the local verison: ${localLatestVersion}, the latest version: ${idsLatestVersion}`);
        this.onUpdate(MessageUtil.getUpdateMessage(UpgradeManagerBase.SUCCESS_CODE, "the local resource is latest, no need to upgrade", type));
      }
    }
  }

  /**
   * 拷贝更新资源并更新资源信息在本地数据库中。
   *
   * @params { UpgradeConstants.SubscribeInfo } subInfo - the subscribeInfo used to get resId and originId.
   * @params { UpgradeConstants.resVersionInfo } resInfo - the resInfo used to get resVersion.
   * @params { string } path - the path that resource file will be copied to.
   * @params { number } flag - the upgrade type.
   * @params { boolean } typeSwitch - indicates whether the resource type has changed.
   */
  private async upgradeRes(subInfo: UpgradeConstants.SubscribeInfo, resInfo: UpgradeConstants.resVersionInfo, path: string,
      flag: number, typeSwitch: boolean): Promise<void> {
    const methodName: string = "upgrade resource";
    let newVersion = resInfo.originId ? resInfo.version : resInfo.resVersion;
    let resList = await this.dataBase.getAllVersion(subInfo.resId, this.realMachineTestRes + "", subInfo.originId)
    let resNum: number = resList.length - UpgradeManagerBase.SUB_NUM;
    if (await this.copyResFile(subInfo, path)) {
      if (!this.realMachineTestRes && await this.dataBase.getTargetVersion(resInfo.resId, newVersion, this.realMachineTestRes + "", resInfo.originId)) {
        AiLog.info(this.domain, methodName, this.TAG, `tagert version already existed, resId: ${resInfo.resId},
         originId: ${resInfo.originId}, version: ${newVersion}`);
      } else {
        let limit = this.realMachineTestRes ? UpgradeManagerBase.TEST_RES_VERSION_LIMIT : UpgradeManagerBase.RES_VERSION_LIMIT;
        if (resNum >= limit) {
          //数据库中存放超过限制数量的版本信息，则需要先删除一个版本信息
          try {
            let deletePath: string = await this.dataBase.deleteInfo(subInfo.resId, this.realMachineTestRes + "", false, subInfo.originId) as string;
            if (deletePath !== path) {
              fs.unlinkSync(deletePath);
            }
          } catch(e){
            let err = e as BusinessError;
            AiLog.error(this.domain, methodName, this.TAG, `delete file failed with code: ${err.code}, message: ${err.message}`);
          }
        }
        let latestResValue: ValuesBucket = UpgradeUtils.getResValue(resInfo, "false", path);
        await this.dataBase.addInfo(UpgradeConstants.resVersionInfoTable.TABLE, latestResValue);
        AiLog.info(this.domain, methodName, this.TAG, `${resNum} in the database, insert success`);
      }
      let curVersion = await this.dataBase.getCurrentVersion(subInfo.resId, this.realMachineTestRes + "", subInfo.originId) as UpgradeConstants.resVersionInfo;
      let message = typeSwitch ? `isRealMachineTest type switch from ${!this.realMachineTestRes} -> ${this.realMachineTestRes}` : `new resource`;
      this.onUpdate(MessageUtil.getUpdateMessage(UpgradeManagerBase.UPDATE_CODE, message, flag, resInfo, curVersion));
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `copy file from aiDataService failed, resId: ${subInfo.resId}, originId: ${subInfo.originId}`);
      this.onUpdate(MessageUtil.getUpdateMessage(UpgradeManagerBase.FAILED_CODE, "copy file from aiDataService failed", flag, resInfo));
    }
  }

  /**
   * 检查IDS端侧版本信息，返回端侧最新版本信息
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo 订阅信息
   * @returns { Promise<UpgradeConstants.resVersionInfo | void> } 返回版本信息
   */
  private async QueryLocal(subscribeInfo: UpgradeConstants.SubscribeInfo): Promise<UpgradeConstants.resVersionInfo | void> {
    const methodName: string = "QueryLocal";
    AiLog.info(this.domain, methodName, this.TAG, "query IDS local resource info");
    let resVersion: UpgradeConstants.resVersionInfo | undefined = undefined;
    await UpgradeSdkManager.QueryLocalThroughSdk(this.context, JSON.stringify(MessageUtil.getQueryLocalMessage(subscribeInfo))).then((res: UpgradeConstants.QueryData[]) => {
      if (res.length) {
        AiLog.info(this.domain, methodName, this.TAG, "query local resource success");
        let resData: UpgradeConstants.QueryData = res[0]
        resVersion = {
          resId: resData.resId,
          resVersion: resData.resVersion,
          domain: resData.domain,
          updateTime: resData.updateTime,
          bundleName: subscribeInfo.bundleName,
          originId: subscribeInfo.originId,
          version: resData.metaDataVersion,
          realMachineTestRes: (resData.realMachineTestRes === "true") ? true : false
        }
      } else {
        AiLog.info(this.domain, methodName, this.TAG, "no resource info in IDS, please subscribe first");
      }
    }).catch((err:BusinessError) => {AiLog.error(this.domain, methodName, this.TAG, `query local failed with err code:${err.code}, err message: ${err.message}`)})
    return resVersion;
  }

  /**
   * 拷贝IDS侧的版本资源
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo 订阅信息
   * @param { string } filePath 拷贝存放的路径
   * @returns { Promise<boolean> } 返回拷贝成功与否
   */
  private async copyResFile(subscribeInfo: UpgradeConstants.SubscribeInfo, filePath: string): Promise<boolean> {
    const methodName: string = "copyResFile";
    AiLog.info(this.domain, methodName, this.TAG, "copy resource file from IDS");
    let result: boolean = false;
    let fd: number;
    await UpgradeSdkManager.CopyThroughSdk(this.context, JSON.stringify(MessageUtil.getCopyMessage(subscribeInfo))).then((res:number) => {
      try {
        fs.copyFileSync(res, filePath);
        fd = res;
        result = true;
        AiLog.info(this.domain, methodName, this.TAG, "copy resource file success");
      } catch(error) {
        let err: BusinessError = error as BusinessError;
        AiLog.error(this.domain, methodName, this.TAG, `copy file failed with err code: ${err.code}, err message:${err.message}`);
      }
    }).catch((err:BusinessError) => {
      AiLog.error(this.domain, methodName, this.TAG, `get file descriptor from IDS failed with err code: ${err.code}. err message: ${err.message}`);
    }).finally(() => {
      if (fd) {
        fs.closeSync(fd);
      }
    })
    return result;
  }

  /**
   * 检查IDS云侧版本信息，返回云侧最新版本信息
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo 订阅信息
   * @returns { Promise<string | void> } 返回版本信息
   */
  private async QueryCloud(subscribeInfo: UpgradeConstants.SubscribeInfo): Promise<string | void> {
    const methodName: string = "QueryCloud";
    AiLog.info(this.domain, methodName, this.TAG, "query IDS cloud resource info");
    let resVersion: string | undefined = undefined;
    await UpgradeSdkManager.QueryCloudThroughSdk(this.context, JSON.stringify(MessageUtil.getQueryCloudMessage(subscribeInfo))).then((res: UpgradeConstants.Description) => {
      let key: string = subscribeInfo.resId;
      resVersion = res[key]?.resVersion;
      AiLog.info(this.domain, methodName, this.TAG, `query IDS cloud success, the version in cloud is ${resVersion}`);
    }).catch((err:BusinessError) => {AiLog.error(this.domain, methodName, this.TAG, `query cloud failed with err code:${err.code}, err message: ${err.message}`)})
    return resVersion;
  }

  /**
   * 下载IDS云侧的版本资源
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo 订阅信息
   * @returns { Promise<boolean> } 是否下载成功
   */
  private async Download(subscribeInfo: UpgradeConstants.SubscribeInfo, realMachineTestRes: string): Promise<boolean> {
    const methodName: string = "Download";
    AiLog.info(this.domain, methodName, this.TAG, "start download from IDS cloud");
    let timeId: number;
    let downloadPromise =  UpgradeSdkManager.DownloadThroughSdk(this.context, JSON.stringify(MessageUtil.getDownloadMessage(subscribeInfo, realMachineTestRes)));
    let timePromise = new Promise<never>((resolve, reject) => {
      timeId = setTimeout(() => {
        AiLog.error(this.domain, methodName, this.TAG, "time is out, download from cloud failed");
        clearTimeout(timeId);
        reject(new Error("download time out"));
      }, UpgradeManagerBase.TIMEOUT)});
    let result: boolean = false;
    await Promise.race([downloadPromise, timePromise]).then((res: boolean) => {
      AiLog.info(this.domain, methodName, this.TAG, "get downloadSdk callback success");
      clearTimeout(timeId);
      result = res
    }).catch((e: BusinessError) => {
      AiLog.error(this.domain, methodName, this.TAG, `download failed: ${e.message}`);
      result = false
    })
    return result;
  }

  /**
   * 解析数据服务传输的notifyMessage，获取版本信息
   *
   * @param { string } notifyMessage - the message that sent by aiDataService when resources is upgraded.
   * @returns { UpgradeConstants.resVersionInfo | void } - the resInfo if parse success.
   */
  private getResInfo(notifyMessage: string): UpgradeConstants.resVersionInfo | void {
    const methodName: string = "getResInfo from notifyMessage";
    AiLog.info(this.domain, methodName, this.TAG,  `the notify message is: ${notifyMessage}`);
    try {
      let message: UpgradeConstants.DispatchParams = JSON.parse(notifyMessage);
      let data: UpgradeConstants.DataItems = message?.content?.contentData[0]?.payload?.dataItems[0];
      let timestamp: string | undefined = data.timestamp;
      let dataItem: Array<UpgradeConstants.NotifyItem> | undefined = data.dataEntityList;
      if (!data || !dataItem) {
        AiLog.error(this.domain, methodName, this.TAG, `the notify Message is invalid`);
        return;
      } else {
        let notifyData: UpgradeConstants.NotifyItem = dataItem[0]
        let resInfo: UpgradeConstants.resVersionInfo = {
          resId: notifyData.resId,
          domain: notifyData.domain,
          originId: notifyData.originId,
          version: notifyData.metaDataVersion,
          resVersion: notifyData.resVersion,
          updateTime: timestamp ?? "",
          bundleName: message.session.senderName,
          realMachineTestRes: (notifyData.realMachineTestRes === "true") ? true : false
        }
        AiLog.info(this.domain, methodName, this.TAG,  `parse notify message success`);
        return resInfo;
      }
    } catch (e) {
      let err = e as BusinessError;
      AiLog.error(this.domain, methodName, this.TAG,  `parse notifyMessage failed with message: ${err.message}`);
      return;
    }
  }
}