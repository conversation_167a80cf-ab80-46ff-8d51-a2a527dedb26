/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { BaseSdkPresenter } from "@hms-ai/aiBaseSdk/src/main/ets/presenter/BaseSdkPresenter";
import { SdkCallbackType } from "@hms-ai/aiBaseSdk/src/main/ets/callback/SdkCallback";
import * as UpgradeConstants from "./UpgradeConstants";
import { commonContext } from './UpgradeManager';
import { AiLog } from "../logmanager/AiLog";

/**
 * 统一sdk管理类，通过统一sdk与IDS绑定并收发消息
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
export default class UpgradeSdkManager {
  //统一sdk管理类的TAG，用于打印日志
  private static TAG: string = "UpgradeSdkManager";
  //统一sdk管理类的领域范围，用于打印日志
  private static domain: number = 0x0000;
  //绑定的服务信息
  private static serviceNameList: string = '["AiDataService"]';
  //发送消息的服务名
  private static serviceName: string = 'AiDataService';

  /**
   * 通过sdk向IDS发送注册资源的报文
   *
   * @params { commonContext } context 应用上下文
   * @params { string } dispatchParams 发送的报文
   * @returns { Promise<number> } 回调接收的消息码
   */
  public static SubscribeThroughSdk(context: commonContext, dispatchParams: string): Promise<number> {
    const methodName: string = "SubscribeThroughSdk";
    AiLog.info(this.domain, methodName, this.TAG, `begin to create service and dispatch messages: ${dispatchParams}`);
    return new Promise(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();
      //绑定IDS
      await upgradePresenter.create(context, this.serviceNameList, {
        onResult: (retCode: number, callbackResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: retCode: ${retCode}, callbackResult: ${callbackResult}`);
        },
        onNotify: (eventResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: eventResult: ${eventResult}`);
        }
      });
      //通过统一sdk向IDS发送订阅升级的报文
      upgradePresenter.dispatchMessage(this.serviceName, dispatchParams, {
        onResult: (retCode: number, callbackResult: string) => {
          try {
            let result: UpgradeConstants.DispatchParams = JSON.parse(callbackResult);
            let resultCode = result?.content?.contentData[0]?.payload?.retCode;
            let description = result?.content?.contentData[0]?.payload?.description
            AiLog.info(this.domain, methodName, this.TAG, `upgradedispatchCallback: resultCode: ${resultCode}, description: ${description}, callbackResult: ${callbackResult}`);
            resolve(resultCode);
          } catch (e) {
            reject(new Error('Subscribe failed'))
          }
        },
        onNotify: (eventResult: string) => {reject(new Error("get callback result failed"))}
      });
    })
  }

  /**
   * 通过sdk向IDS发送查询资源的报文
   *
   * @params { commonContext } context 应用上下文
   * @params { string } dispatchParams 发送的报文
   * @returns { Promise<UpgradeConstants.QueryData> } 回调接收的查询信息
   */
  public static QueryLocalThroughSdk(context: commonContext, dispatchParams: string): Promise<UpgradeConstants.QueryData[]> {
    const methodName: string = "QueryThroughSdk";
    AiLog.info(this.domain, methodName, this.TAG, `begin to create service and dispatch messages: ${dispatchParams}`);
    return new Promise(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();
      //绑定IDS
      await upgradePresenter.create(context, this.serviceNameList, {
        onResult: (retCode: number, callbackResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: retCode: ${retCode}, callbackResult: ${callbackResult}`);
        },
        onNotify: (eventResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: eventResult: ${eventResult}`);
        }
      });
      //通过统一sdk向IDS发送查询端侧版本信息的报文
      upgradePresenter.dispatchMessage(this.serviceName, dispatchParams, {
        onResult: (retCode: number, callbackResult: string) => {
          if (retCode === 1) {
            try {
              let result: UpgradeConstants.DispatchParams = JSON.parse(callbackResult);
              let resultCode = result?.content?.contentData[0]?.payload?.retCode;
              let description = result?.content?.contentData[0]?.payload?.description;
              AiLog.info(this.domain, methodName, this.TAG, `upgradedispatchCallback: resultCode: ${resultCode}, description: ${description}, callbackResult: ${callbackResult}`);
              if (resultCode === 1) {
                let data: UpgradeConstants.QueryData[] = result?.content?.contentData[0]?.payload?.data?.[0]?.data;
                resolve(data);
              } else {
                reject(new Error(`query failed, message: ${description}`))
              }
            } catch (e) {
              reject(new Error(`query failed, message: ${callbackResult}`))
            }
          } else {
            reject(new Error(`query failed, message: ${callbackResult}`))
          }
        },
        onNotify: (eventResult: string) => {reject(new Error("get callback result failed"))}
      });
    })
  }

  /**
   * 通过sdk向IDS发送拷贝资源的报文
   *
   * @params { commonContext } context 应用上下文
   * @params { string } dispatchParams 发送的报文
   * @returns { Promise<number> } 回调接收的文件句柄
   */
  public static CopyThroughSdk(context: commonContext, dispatchParams: string): Promise<number> {
    const methodName: string = "CopyThroughSdk";
    AiLog.info(this.domain, methodName, this.TAG, `begin to create service and dispatch messages: ${dispatchParams}`);
    return new Promise(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();
      //绑定IDS
      await upgradePresenter.create(context, this.serviceNameList, {
        onResult: (retCode: number, callbackResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: retCode: ${retCode}, callbackResult: ${callbackResult}`);
        },
        onNotify: (eventResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: eventResult: ${eventResult}`);
        }
      });
      //通过统一sdk向IDS发送查询版本信息的报文
      upgradePresenter.dispatchMessage(this.serviceName, dispatchParams, {
        onResult: (retCode: number, callbackResult: number, sdkCallbackType: SdkCallbackType) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: retCode: ${retCode}, callbackResult: ${callbackResult}, type: ${sdkCallbackType}`);
          if (retCode === 1) {
            resolve(callbackResult)
          } else {
            reject(new Error("get file descriptor failed"))
          }
        },
        onNotify: (eventResult: string) => {reject(new Error("get callback result failed"))}
      });
    })
  }

  /**
   * 通过sdk向IDS发送查询云侧最新资源的报文
   *
   * @params { commonContext } context 应用上下文
   * @params { string } dispatchParams 发送的报文
   * @returns { Promise<UpgradeConstants.QueryData> } 回调接收的查询信息
   */
  public static QueryCloudThroughSdk(context: commonContext, dispatchParams: string): Promise<UpgradeConstants.Description> {
    const methodName: string = "QueryCloudThroughSdk";
    AiLog.info(this.domain, methodName, this.TAG, "begin to create service and dispatch messages");
    return new Promise(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();
      //绑定IDS
      await upgradePresenter.create(context, this.serviceNameList, {
        onResult: (retCode: number, callbackResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: retCode: ${retCode}, callbackResult: ${callbackResult}`);
        },
        onNotify: (eventResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: eventResult: ${eventResult}`);
        }
      });
      //通过统一sdk向IDS发送查询云侧版本信息的报文
      upgradePresenter.dispatchMessage(this.serviceName, dispatchParams, {
        onResult: (retCode: number, callbackResult: string) => {
          if (retCode === 1) {
            try {
              let result: UpgradeConstants.Reply = JSON.parse(callbackResult);
              let resultCode = result?.resultCode;
              AiLog.info(this.domain, methodName, this.TAG, `upgradedispatchCallback: resultCode: ${resultCode}, callbackResult: ${callbackResult}`);
              if (resultCode === 1) {
                try {
                  let description: UpgradeConstants.Description = JSON.parse(result?.description)
                  resolve(description);
                } catch (e) {
                  reject(new Error(`query cloud failed, message is: ${callbackResult}`))
                }
              } else {
                reject(new Error(`query cloud failed, message is: ${callbackResult}`))
              }
            } catch (e) {
              reject(new Error(`query cloud failed, message is: ${callbackResult}`))
            }
          } else {
            reject(new Error(`query cloud failed, message is: ${callbackResult}`))
          }
        },
        onNotify: (eventResult: string) => {reject(new Error("get callback result failed"))}
      });
    })
  }

  /**
   * 通过sdk向IDS发送下载云侧资源的报文
   *
   * @params { commonContext } context 应用上下文
   * @params { string } dispatchParams 发送的报文
   * @returns { Promise<string> } 回调接收的信息
   */
  public static DownloadThroughSdk(context: commonContext, dispatchParams: string): Promise<boolean> {
    const methodName: string = "DownloadThroughSdk";
    AiLog.info(this.domain, methodName, this.TAG, `begin to create service and dispatch messages: ${dispatchParams}`);
    let downloadPromise =  new Promise<boolean>(async (resolve, reject) => {
      let upgradePresenter = BaseSdkPresenter.getInstance();
      //绑定IDS
      await upgradePresenter.create(context, this.serviceNameList, {
        onResult: (retCode: number, callbackResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: retCode: ${retCode}, callbackResult: ${callbackResult}`);
        },
        onNotify: (eventResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradeCreateCallback: eventResult: ${eventResult}`);
        }
      });
      //通过统一sdk向IDS发送下载资源的报文
      upgradePresenter.dispatchMessage(this.serviceName, dispatchParams, {
        onResult: (retCode: number, callbackResult: string) => {
          AiLog.info(this.domain, methodName, this.TAG, `upgradedispatchCallback: callbackResult: ${callbackResult}`);
          if (retCode === 1) {
            try {
              let result: UpgradeConstants.Reply = JSON.parse(callbackResult);
              let resultCode = result?.resultCode;
              if (resultCode === 1) {
                resolve(true);
              } else {
                resolve(false);
              }
            } catch (e) {
              resolve(false);
            }
          } else {
            resolve(false);
          }
        },
        onNotify: (eventResult: string) => {
          resolve(false)
        }
      });
    })
    return downloadPromise;
  }
}