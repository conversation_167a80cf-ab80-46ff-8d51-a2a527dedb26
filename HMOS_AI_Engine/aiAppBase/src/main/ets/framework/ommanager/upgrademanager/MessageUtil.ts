/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import * as UpgradeConstants from "./UpgradeConstants"

/**
 * 构造统一sdk的发送报文和升级管理方法的返回结构
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
export default class MessageUtil {
  //绑定服务名
  private static serviceName: string = "AiDataService";
  //跟踪ID
  private static traceId: string = "123456789";
  //消息版本
  private static messageVersion: string = "1.0.0";
  //报文的namespace
  private static readonly IDS_NAMESPACE: string = "ResourcePackage";
  //common
  private static readonly COMMON: string = "common";

  /**
   * 获取订阅升级的发送报文
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo 订阅信息
   * @returns { UpgradeConstants.DispatchParams } 统一sdk的发送报文
   */
  public static getSubscribeMessage(subscribeInfo: UpgradeConstants.SubscribeInfo): UpgradeConstants.DispatchParams {
    let subscribeMessage: UpgradeConstants.DispatchParams = {
      session: {
        serviceName: MessageUtil.serviceName,
        messageName: "SubscribeResourcePackage",
        senderName: subscribeInfo.bundleName,
        traceId: MessageUtil.traceId,
        messageVersion: MessageUtil.messageVersion
      },
      content: {
        contentData: [
          {
            header: {
              namespace: MessageUtil.IDS_NAMESPACE,
              name: "Local"
            },
            payload: {
              request: {
                dataEntities: [
                  {
                    resId: subscribeInfo.resId,
                    domain: subscribeInfo.domain,
                    isSupportMobileNet: subscribeInfo.isSupportMobileNet ? true : false,
                    resVersion: subscribeInfo.resVersion,
                    needShareRes: subscribeInfo.originId ? true : false
                  }
                ]
              }
            }
          }
        ]
      }
    };
    return subscribeMessage;
  }

  /**
   * 获取取消订阅的发送报文
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo 订阅信息
   * @returns { UpgradeConstants.DispatchParams } 统一sdk的发送报文
   */
  public static getUnSubscribeMessage(subscribeInfo: UpgradeConstants.SubscribeInfo): UpgradeConstants.DispatchParams {
    let option: UpgradeConstants.Options;
    if (subscribeInfo.originId) {
      option = {
        resId: subscribeInfo.resId,
        domain: subscribeInfo.domain,
        originId: subscribeInfo.originId
      }
    } else {
      option = {
        resId: subscribeInfo.resId,
        domain: subscribeInfo.domain
      }
    }
    let unSubscribeMessage: UpgradeConstants.DispatchParams = {
      session: {
        serviceName: MessageUtil.serviceName,
        messageName: "UnsubscribeResourcePackage",
        senderName: subscribeInfo.bundleName,
        traceId: MessageUtil.traceId,
        messageVersion: MessageUtil.messageVersion
      },
      content: {
        contentData: [
          {
            header: {
              namespace: MessageUtil.IDS_NAMESPACE,
              name: "Local"
            },
            payload: {
              request: {
                options: [option]
              }
            }
          }
        ]
      }
    };
    return unSubscribeMessage;
  }

  /**
   * 获取订阅升级的发送报文
   *
   * @param { UpgradeConstants.SubscribeInfo } subscribeInfo 订阅信息
   * @returns { UpgradeConstants.DispatchParams } 统一sdk的发送报文
   */
  public static getSubscribeDataItemMessage(subscribeInfo: UpgradeConstants.SubscribeInfo, subs: boolean): UpgradeConstants.DispatchParams {
    let params: UpgradeConstants.SubscribeParams;
    let pattern = new RegExp("[^0-9a-zA-Z]", "g");
    let resId = subscribeInfo.resId.replace(pattern, "");
    if (subscribeInfo.originId) {
      params = {
        subscribeId: subscribeInfo.originId.replace(pattern, "") + "Of" + resId,
        args: {
          subscribeType: "incremental",
          resId: subscribeInfo.resId,
          domain: subscribeInfo.domain,
          originId: subscribeInfo.originId
        }
      }
    } else {
      params = {
        subscribeId: resId,
        args: {
          subscribeType: "incremental",
          resId: subscribeInfo.resId,
          domain: subscribeInfo.domain
        }
      }
    }
    let subscribeMessage: UpgradeConstants.DispatchParams = {
      session: {
        serviceName: MessageUtil.serviceName,
        messageName: subs ? "SubscribeDataItem" : "UnsubscribeDataItem",
        senderName: subscribeInfo.bundleName,
        traceId: MessageUtil.traceId,
        messageVersion: MessageUtil.messageVersion
      },
      content: {
        contentData: [
          {
            header: {
              namespace: MessageUtil.IDS_NAMESPACE,
              name: subscribeInfo.originId ? "ResourceMetaData" : "Local"
            },
            payload: {
              notifyParams: {
                mode: "Service",
                action: subscribeInfo.serviceName,
                bundleName: subscribeInfo.bundleName
              },
              subscribeParams: [params]
            }
          }
        ]
      }
    };
    return subscribeMessage;
  }

  /**
   * 获取查询IDS端侧版本的发送报文
   *
   * @param { UpgradeConstants.SubscribeInfo } queryInfo 订阅信息
   * @returns { UpgradeConstants.DispatchParams } 统一sdk的发送报文
   */
  public static getQueryLocalMessage(queryInfo: UpgradeConstants.SubscribeInfo): UpgradeConstants.DispatchParams {
    let queryOption: UpgradeConstants.Options;
    if (queryInfo.originId) {
      queryOption = {
        resId: queryInfo.resId,
        domain: queryInfo.domain,
        originId: queryInfo.originId
      }
    } else {
      queryOption = {
        resId: queryInfo.resId,
        domain: queryInfo.domain
      }
    }
    let queryLocalMessage: UpgradeConstants.DispatchParams = {
      session: {
        serviceName: MessageUtil.serviceName,
        messageName: "QueryResourcePackage",
        senderName: queryInfo.bundleName,
        traceId: MessageUtil.traceId,
        messageVersion: MessageUtil.messageVersion
      },
      content: {
        contentData: [
          {
            header: {
              namespace: MessageUtil.IDS_NAMESPACE,
              name: "Local"
            },
            payload: {
              request: {
                options: [queryOption]
              }
            }
          }
        ]
      }
    };
    return queryLocalMessage;
  }

  /**
   * 获取拷贝资源的发送报文
   *
   * @param { UpgradeConstants.SubscribeInfo } copyInfo 订阅信息
   * @returns { UpgradeConstants.DispatchParams } 统一sdk的发送报文
   */
  public static getCopyMessage(copyInfo: UpgradeConstants.SubscribeInfo): UpgradeConstants.DispatchParams {
    let copyOption: UpgradeConstants.Options;
    if (copyInfo.originId) {
      copyOption = {
        resId: copyInfo.resId,
        domain: copyInfo.domain,
        originId: copyInfo.originId
      }
    } else {
      copyOption = {
        resId: copyInfo.resId,
        domain: copyInfo.domain
      }
    }
    let copyMessage: UpgradeConstants.DispatchParams = {
      session: {
        serviceName: MessageUtil.serviceName,
        messageName: "CopyResourcePackage",
        senderName: copyInfo.bundleName,
        traceId: MessageUtil.traceId,
        messageVersion: MessageUtil.messageVersion
      },
      content: {
        contentData: [
          {
            header: {
              namespace: MessageUtil.IDS_NAMESPACE,
              name: "Local"
            },
            payload: {
              request: {
                options: [copyOption]
              }
            }
          }
        ]
      }
    };
    return copyMessage;
  }

  /**
   * 获取查询IDS端侧版本的发送报文
   *
   * @param { UpgradeConstants.SubscribeInfo } queryInfo 订阅信息
   * @returns { UpgradeConstants.DispatchParams } 统一sdk的发送报文
   */
  public static getQueryCloudMessage(queryInfo: UpgradeConstants.SubscribeInfo): UpgradeConstants.DispatchParams {
    let queryCloudMessage: UpgradeConstants.DispatchParams = {
      session: {
        serviceName: MessageUtil.serviceName,
        messageName: "DownloadResourcePackage",
        senderName: queryInfo.bundleName,
        traceId: MessageUtil.traceId,
        messageVersion: MessageUtil.messageVersion
      },
      content: {
        contentData: [
          {
            header: {
              namespace: MessageUtil.IDS_NAMESPACE,
              name: "QueryCloud"
            },
            payload: {
              resources: [{
                resId: queryInfo.resId,
                domain: queryInfo.domain,
                deviceType: MessageUtil.COMMON,
                resVersion: queryInfo.resVersion,
                productName: MessageUtil.COMMON,
                romVersion: MessageUtil.COMMON,
                osVersion: MessageUtil.COMMON
              }]
            }
          }
        ]
      }
    };
    return queryCloudMessage;
  }

  /**
   * 获取IDS下载资源的发送报文
   *
   * @param { UpgradeConstants.SubscribeInfo } queryInfo 订阅信息
   * @returns { UpgradeConstants.DispatchParams } 统一sdk的发送报文
   */
  public static getDownloadMessage(queryInfo: UpgradeConstants.SubscribeInfo, realMachineTestRes: string): UpgradeConstants.DispatchParams {
    let downloadMessage: UpgradeConstants.DispatchParams = {
      session: {
        serviceName: MessageUtil.serviceName,
        messageName: "DownloadResourcePackage",
        senderName: queryInfo.bundleName,
        traceId: MessageUtil.traceId,
        messageVersion: MessageUtil.messageVersion
      },
      content: {
        contentData: [
          {
            header: {
              namespace: MessageUtil.IDS_NAMESPACE,
              name: "Execute"
            },
            payload: {
              resources: [{
                resId: queryInfo.resId,
                domain: queryInfo.domain,
                deviceType: MessageUtil.COMMON,
                resVersion: queryInfo.resVersion,
                productName: MessageUtil.COMMON,
                romVersion: MessageUtil.COMMON,
                osVersion: MessageUtil.COMMON,
                needShareRes: queryInfo.originId ? true : false,
                realMachineTestRes: realMachineTestRes,
              }]
            }
          }
        ]
      }
    };
    return downloadMessage;
  }

  /**
   * 获取onUpdate入参结构
   *
   * @param { string|undefined } curVersion 当前版本
   * @param { UpgradeConstants.resVersionInfo } latestVersion 最新版本
   * @param { string } type 触发升级的类型
   * @returns { UpgradeConstants.updateMessage } 触发升级提醒的资源版本信息
   */
  public static getUpdateMessage(code: number, message: string, flag: number, resInfo?: UpgradeConstants.resVersionInfo,
                                 cur?: UpgradeConstants.resVersionInfo | undefined): UpgradeConstants.updateMessage {
    let type: string;
    let updateMessage: UpgradeConstants.updateMessage;
    switch(flag) {
      case UpgradeConstants.upgradeType.LOCAL:
        type = "local";
        break;
      case UpgradeConstants.upgradeType.CLOUD:
        type = "cloud";
        break;
      case UpgradeConstants.upgradeType.NOTIFY:
        type = "notify"
        break;
    }
    if (resInfo) {
      let curVersion: string = ""
      if( cur ){
        curVersion = (resInfo.originId) ? cur.version : cur.resVersion
      }
      let data: UpgradeConstants.updateData = {
        resId: resInfo.resId,
        originId: resInfo.originId ?? "",
        currentVersion: curVersion,
        newVersion: (resInfo.originId) ? resInfo.version : resInfo.resVersion,
        realMachineTestRes: resInfo.realMachineTestRes,
      }
      updateMessage = {
        code: code,
        message: message,
        type: type,
        data: data
      }
    } else {
      updateMessage = {
        code: code,
        message: message,
        type: type,
      }
    }
    return updateMessage;
  }

}

