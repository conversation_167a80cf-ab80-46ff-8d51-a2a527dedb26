/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * 升级管理涉及到的结构体
 * 框架保存的订阅信息的结构
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
export interface SubscribeInfo extends Object {
  //业务bundleName
  bundleName: string;
  //业务接收数据服务主动通知的abilityName
  serviceName: string;
  //业务资源id
  resId: string;
  //业务领域
  domain: string;
  //是否支持流量下载, 0为不支持，1为支持，默认为不支持
  isSupportMobileNet?: number;
  //资源初始版本号，如果不填默认为"0x00000000"
  resVersion?: string;
  //资源子文件的id
  originId?: string;
  //真机测试标识
  realMachineTestRes?: boolean;
}

//框架保存的资源版本结构
export interface resVersionInfo extends Object {
  //业务资源id信息
  resId: string;
  //业务领域信息
  domain: string;
  //真机测试标识
  realMachineTestRes: boolean;
  //子文件id
  originId?: string;
  //子文件版本号
  version?: string;
  //大资源包的版本号。
  resVersion?: string;
  //是否为当前版本
  isCurrent?: string;
  //资源文件的沙箱路径
  filePath?: string;
  //资源更新时间
  updateTime?: string;
  //应用名称
  bundleName?: string;
}

//触发升级提醒的资源信息
export interface updateMessage extends Object {
  //返回码: 0代表无新资源，1代表有新资源，-1代表更新失败
  code: number
  //返回信息
  message: string;
  //触发upgrade的方式
  type: string;
  //资源版本信息
  data?: updateData
}

export interface updateData extends Object {
  resId: string;
  originId: string;
  currentVersion: string;
  newVersion: string;
  realMachineTestRes: boolean;
}

//订阅信息的表单，包括表单名和索引名
export enum subscribeInfoTable {
  TABLE = "subscribeInfo",
  ID = "id",
  RES_ID = "resId",
  DOMAIN = "domain",
  NET = "isSupportMobileNet",
  RES_VERSION = "resVersion",
  ORIGIN_ID = "originId",
  BUNDLE_NAME = "bundleName",
  SERVICE_NAME = "serviceName"
}

//版本信息的表单，包含表单名和索引名
export enum resVersionInfoTable {
  TABLE = "resVersionInfo",
  ID = "id",
  RES_ID = "resId",
  VERSION = "version",
  DOMAIN = "domain",
  IS_CURRENT = "isCurrent",
  FILE_PATH = "filePath",
  UPDATE_TIME = "updateTime",
  BUNDLE_NAME = "bundleName",
  ORIGIN_ID = "originId",
  RES_VERSION = "resVersion",
  REAL_MACHINE_TEST_RES = "realMachineTestRes",
}

export enum upgradeType {
  LOCAL = 0,
  CLOUD = 1,
  NOTIFY = 2
}

//query的返回结果
export interface QueryResult extends Object {
  //0成功，-1失败
  code: number;
  message?: string;
  data?: {
    resId: string;
    originId: string;
    realMachineTestRes: boolean;
    currentVersion: string;
    updateTime: string;
    versionList: Array<string>
  }
}

//getFile的返回结果
export interface getFileResult extends Object {
  //0成功，-1失败
  code: number;
  message?: string
  data?: {
    resId: string;
    originId: string;
    realMachineTestRes: boolean;
    version: string
  }
}

//upgrade的返回结果
export interface UpgradeResult extends Object {
  //0有新版本，-1没有新版本，-2查询失败
  code: number;
  data?: Array<Version>
}

export interface Version extends Object {
  resId: string;
  version?: string;
  message: string
}



//统一sdk发送和接收报文的格式，用于构造和解析报文
export interface DispatchParams extends Object {
  session: Session;
  content: Content;
}

export interface Session extends Object {
  serviceName: string;
  messageName: string;
  senderName: string;
  traceId: string;
  messageVersion: string;
  extra?: any
}

export interface Content extends Object {
  contentData: Array<ContentData>
}

export interface ContentData extends Object {
  header: Header;
  payload: Payload
}

export interface Header extends Object {
  namespace: string;
  name: string;
}

export interface Payload extends Object {
  request?: Request;
  retCode?: number;
  description?: string;
  data?: Array<Data>;
  resources?: Array<Resources>;
  notifyParams?: NotifyParams;
  subscribeParams?: Array<SubscribeParams>;
  dataItems?: Array<DataItems>
}

export interface Request extends Object {
  dataEntities?: Array<DataEntities>;
  options?: Array<Options>
}

export interface DataEntities extends Object {
  resId: string;
  domain: string;
  isSupportMobileNet: boolean;
  resVersion: string;
  needShareRes: boolean;
}

export interface Options extends Object {
  resId: string;
  domain: string;
  originId?: string;
}

export interface Data extends Object {
  description: string;
  retCode: number;
  data?: Array<QueryData>
}

export interface QueryData extends Object {
  domain: string;
  isSupportMobileNet: number;
  resId: string;
  realMachineTestRes?: string;
  resVersion?: string;
  updateTime: string;
  resPath?: string;
  shareCounts?: number;
  metaDataVersion?: string;
  metaDataPath?: string;
}

//查询云端和启动下载的资源信息，包含在payload中
export interface Resources extends Object {
  resId: string;
  domain: string;
  deviceType: string;
  resVersion: string;
  productName: string;
  romVersion: string;
  osVersion: string;
  needShareRes?: boolean;
  realMachineTestRes?: string;
}

//查询云端和启动下载的返回报文
export interface Reply extends Object {
  description: string;
  failedList: string;
  resultCode: number;
  succeedList: string
}

export interface Description {
  [key: string]: DescriptionInfo
}

export interface DescriptionInfo extends Object {
  resId: string;
  resVersion: string;
  domain: string;
  resSize: string;
  resUrl: string;
  resDigest: string;
  resType: string;
  supportSubRes: boolean;
  resPriority: string;
  realMachineTestRes?: boolean;
}

export interface NotifyParams extends Object {
  action: string;
  bundleName: string;
  mode: string;
}

export interface SubscribeParams extends Object {
  args: Args;
  subscribeId: string;
}

export interface Args extends Object {
  domain: string;
  originId?: string;
  resId: string;
  subscribeType: string;
}

export interface DataItems extends Object {
  changeType: string;
  notifyDataType: string;
  subscribeId: string;
  timestamp?: string;
  dataEntityList: Array<NotifyItem>
}

export interface NotifyItem extends Object {
  resId: string;
  domain: string;
  originId?: string;
  metaDataPath?: string;
  metaDataVersion?: string;
  resVersion?: string;
  resPath?: string;
  realMachineTestRes?: string;
}