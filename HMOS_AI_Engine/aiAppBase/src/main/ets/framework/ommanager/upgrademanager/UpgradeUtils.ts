/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import * as UpgradeConstants from "./UpgradeConstants";
import { ValuesBucket } from "@ohos.data.ValuesBucket";
import { BusinessError } from "@ohos.base";
import { AiLog } from "../logmanager/AiLog";

export default class UpgradeUtils {

  public static getSubValue(subInfo: UpgradeConstants.SubscribeInfo): ValuesBucket {
    let value: ValuesBucket = {
      "resId": subInfo.resId,
      "domain": subInfo.domain,
      "isSupportMobileNet": subInfo.isSupportMobileNet as number,
      "resVersion": subInfo.resVersion as string,
      "originId": subInfo.originId ?? "",
      "bundleName": subInfo.bundleName,
      "serviceName": subInfo.serviceName
    }
    return value;
  }

  public static getResValue(resVersionInfo: UpgradeConstants.resVersionInfo, isCur: string, path: string): ValuesBucket {
    let filePath: string = resVersionInfo.filePath ?? path;
    let resVersionValue: ValuesBucket = {
      "resId": resVersionInfo.resId,
      "version": resVersionInfo.version ?? "",
      "domain": resVersionInfo.domain,
      "isCurrent": isCur,
      "filePath": filePath,
      "updateTime": resVersionInfo.updateTime,
      "bundleName": resVersionInfo.bundleName,
      "originId": resVersionInfo.originId ?? "",
      "resVersion": resVersionInfo.resVersion ?? "",
      "realMachineTestRes": resVersionInfo.realMachineTestRes ? "true" : "false"
    }
    return resVersionValue;
  }

  public static checkSubInfo(subInfo: UpgradeConstants.SubscribeInfo, resInfo: UpgradeConstants.resVersionInfo): boolean {
    if (subInfo.resId !== resInfo.resId) {
      return false;
    }
    let subOrigin: string | undefined = subInfo.originId;
    let resOrigin: string | undefined = resInfo.originId;
    if (subOrigin === resOrigin) {
      return true;
    }
    return false;
  }

  public static getPath(resInfo: UpgradeConstants.resVersionInfo): string {
    let path = '/Version_';
    if(resInfo.originId){
      path += `${resInfo.resId}_${resInfo.originId}_${resInfo.version}.zip`;
    } else {
      path += `${resInfo.resId}_${resInfo.resVersion}.zip`;
    }
    return path;
  }
}