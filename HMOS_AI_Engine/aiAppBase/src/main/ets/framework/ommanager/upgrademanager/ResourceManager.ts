/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import relationalStore from '@ohos.data.relationalStore';
import { BusinessError } from "@ohos.base";
import { ValuesBucket } from "@ohos.data.ValuesBucket";
import common from '@ohos.app.ability.common';
import { AiLog } from "../logmanager/AiLog";
import * as UpgradeConstants from "./UpgradeConstants"
import distributedKVStore from '@ohos.data.distributedKVStore';
import { subscribeInfoTable } from './UpgradeConstants';

//定义上下文的类型
type commonContext = common.UIAbilityContext | common.ServiceExtensionContext;

/**
 * 资源管理类ResourceManager，用于创建并管理一个数据库，以存储订阅信息和版本信息
 *
 * <AUTHOR>
 * @since 2023-12-26
 */
export class ResourceManager {
  private readonly TAG: string = "ResourceManager";
  //应用上下文
  private context: commonContext;
  //单例对象
  private static instance: ResourceManager;
  //应用领域范围，用于日志打印
  private domain: number = 0x0000;
  // 创建数据库的配置信息，默认路径为应用沙箱路径下的/rdb
  private storeConfig: relationalStore.StoreConfig = {
    //数据库
    name: "aiAppBase_upgrade_database",
    //安全级别
    securityLevel: relationalStore.SecurityLevel.S1
  }
  //数据库中存在数据的最少行数
  private static readonly ROW_EXIST_THRESHOLD: number = 1;
  //
  private static readonly EMPTY_STRING: string = "";
  // 键值数据库
  private static readonly UPGRADE_KV_DATABASE: string = 'Upgrade_KV_Database';

  // 创建键值数据库配置信息
  private kvStoreOptions: distributedKVStore.Options = {
    createIfMissing: true,
    encrypt: false,
    backup: true,
    autoSync: false,
    kvStoreType: distributedKVStore.KVStoreType.SINGLE_VERSION,
    securityLevel: distributedKVStore.SecurityLevel.S2,
  }

  /**
   * ResourceManager类构造器
   *
   * @param { commonContext } context 应用上下文
   */
  private constructor(context: commonContext) {
    this.context = context;
  }

  /**
   * 获取ResourceManager单例对象
   *
   * @param { commonContext } context - 上下文
   * @returns { ResourceManager } ResourceManager实例
   */
  public static getInstance(context: commonContext): ResourceManager {
    this.instance = this.instance ?? new ResourceManager(context);
    return this.instance;
  }

  /**
   * 创建关系型数据库并初始化表结构
   */
  public async initDatabase(): Promise<boolean> {
    const methodName: string = "initDatabase";
    let initResult: boolean = false;
    let store: relationalStore.RdbStore | undefined = undefined;
    //创建subscribeInfo表的SQL语句，用以初始化表格，该表格存储订阅信息
    let createSubscribeInfo: string = `CREATE TABLE IF NOT EXISTS ${UpgradeConstants.subscribeInfoTable.TABLE} (` +
      `${UpgradeConstants.subscribeInfoTable.ID} INTEGER PRIMARY KEY AUTOINCREMENT, ` +
      `${UpgradeConstants.subscribeInfoTable.BUNDLE_NAME} TEXT NOT NULL, ` +
      `${UpgradeConstants.subscribeInfoTable.SERVICE_NAME} TEXT NOT NULL, ` +
      `${UpgradeConstants.subscribeInfoTable.RES_ID} TEXT NOT NULL, ` +
      `${UpgradeConstants.subscribeInfoTable.DOMAIN} TEXT NOT NULL, ` +
      `${UpgradeConstants.subscribeInfoTable.NET} INTEGER NOT NULL, ` +
      `${UpgradeConstants.subscribeInfoTable.RES_VERSION} TEXT NOT NULL, ` +
      `${UpgradeConstants.subscribeInfoTable.ORIGIN_ID} TEXT)`;
    //创建resVersionInfo表的SQL语句，用以初始化表格，该表格存储版本信息
    let createResVersionInfo: string = `CREATE TABLE IF NOT EXISTS ${UpgradeConstants.resVersionInfoTable.TABLE} (` +
      `${UpgradeConstants.resVersionInfoTable.ID} INTEGER PRIMARY KEY AUTOINCREMENT,` +
      `${UpgradeConstants.resVersionInfoTable.RES_ID} TEXT NOT NULL, ` +
      `${UpgradeConstants.resVersionInfoTable.VERSION} TEXT , ` +
      `${UpgradeConstants.resVersionInfoTable.DOMAIN} TEXT NOT NULL, ` +
      `${UpgradeConstants.resVersionInfoTable.IS_CURRENT} TEXT NOT NULL, ` +
      `${UpgradeConstants.resVersionInfoTable.FILE_PATH} TEXT, ` +
      `${UpgradeConstants.resVersionInfoTable.UPDATE_TIME} TEXT, ` +
      `${UpgradeConstants.resVersionInfoTable.BUNDLE_NAME} TEXT, ` +
      `${UpgradeConstants.resVersionInfoTable.ORIGIN_ID} TEXT, ` +
      `${UpgradeConstants.resVersionInfoTable.RES_VERSION} TEXT, ` +
      `${UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES} TEXT)`;
    //获取数据库，如果不存在则创建数据库
    try {
      await relationalStore.getRdbStore(this.context, this.storeConfig).then(async (rdbStore:relationalStore.RdbStore) => {
        store = rdbStore;
        //初始化数据库的表结构
        store.executeSql(createSubscribeInfo);
        store.executeSql(createResVersionInfo);
        AiLog.info(this.domain, methodName, this.TAG, "database create success");
        initResult = true;
      }).catch((err:BusinessError) => {
        AiLog.error(this.domain, methodName, this.TAG, `get RdbStore failed, code is ${err.code}, message is ${err.message}`);
      })
    } catch(error) {
      let err = error as BusinessError;
      AiLog.error(this.domain, methodName, this.TAG, `get RdbStore failed, code is ${err.code}, message is ${err.message}`);
    }
    return initResult;
  }

  /**
   * 检查数据库的订阅信息
   *
   * @param { string } target 查询的目标字段
   * @returns { Promise<boolean> } 是否存在
   */
  public async checkSubscribeInfo(resId: string, originId?: string): Promise<UpgradeConstants.SubscribeInfo | void> {
    const methodName: string = "checkSubscribeInfo";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig);
    let result : UpgradeConstants.SubscribeInfo | undefined = undefined;
    //配置谓词以匹配sheet表中resId列值为target的字段
    let predicate = new relationalStore.RdbPredicates(UpgradeConstants.subscribeInfoTable.TABLE);
    if (originId) {
      predicate.equalTo(UpgradeConstants.subscribeInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.subscribeInfoTable.ORIGIN_ID, originId)
    } else {
      predicate.equalTo(UpgradeConstants.subscribeInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.subscribeInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
    }
    //根据predicate查询数据库中是否存在该数据
    if (store !== undefined){
      await store.query(predicate).then((resultSet: relationalStore.ResultSet) => {
        if (resultSet.rowCount < ResourceManager.ROW_EXIST_THRESHOLD) {
          AiLog.info(this.domain, methodName, this.TAG, `target resId not exists, row count:${resultSet.rowCount}`);
        } else {
          AiLog.info(this.domain, methodName, this.TAG, `target resId exists, row count: ${resultSet.rowCount}`);
          resultSet.goToFirstRow();
          result = this.getSubInfo(resultSet);
        }
        resultSet.close();
      }).catch((err: BusinessError) => {
        AiLog.error(this.domain, methodName, this.TAG, `no subscribeInfo in the database, error code: ${err.code}, error message: ${err.message}`);
      })
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `get rdbStore failed`);
    }
    return result;
  }

  /**
   * 获取指定resId或originId的最新版本
   *
   * @param { string } resId 目标的资源id
   * @param { string } originId 子文件资源id
   * @returns { Promise<UpgradeConstants.reeVersionInfo | void> } 返回对应版本信息
   */
   public async getLatestVersion(resId: string, realMachineTestRes: string, originId?: string): Promise<UpgradeConstants.resVersionInfo | void> {
     const methodName: string = "getLatestVersion";
     let store = await relationalStore.getRdbStore(this.context, this.storeConfig);
     let result: UpgradeConstants.resVersionInfo | undefined = undefined;

     let predicates = new relationalStore.RdbPredicates(UpgradeConstants.resVersionInfoTable.TABLE);
     if (originId) {
       predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
         .and()
         .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, originId)
         .and()
         .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes)
     } else {
       predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
         .and()
         .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
         .and()
         .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes)
     }
     if (store !== undefined) {
       await store.query(predicates).then((resultSet: relationalStore.ResultSet) => {
         if (resultSet.rowCount < ResourceManager.ROW_EXIST_THRESHOLD){
           AiLog.info(this.domain, methodName, this.TAG, `no version in the table, row count: ${resultSet.rowCount}`);
         } else {
           resultSet.goToLastRow();
           result = this.getResInfo(resultSet);
         }
         resultSet.close();
       }).catch((err: BusinessError) => {
         AiLog.error(this.domain, methodName, this.TAG, `no verison in the database, error code: ${err.code}, error message: ${err.message}`);
       })
     } else {
       AiLog.error(this.domain, methodName, this.TAG, `get rdbStore failed`);
     }
     return result;
   }

  /**
   * 获取指定resId或originId的指定版本
   *
   * @param { string } resId 目标的资源id
   * @param { string } version 目标版本
   * @returns { Promise<UpgradeConstants.reeVersionInfo | void> } 返回对应版本信息
   */
  public async getTargetVersion(resId: string, version: string, realMachineTestRes: string, originId?: string): Promise<UpgradeConstants.resVersionInfo | void> {
    const methodName: string = "getTargetVersion";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig)
    let result: UpgradeConstants.resVersionInfo | undefined = undefined;

    let predicates = new relationalStore.RdbPredicates(UpgradeConstants.resVersionInfoTable.TABLE);
    if (originId) {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, originId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.VERSION, version)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes)
    } else {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.RES_VERSION, version)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes)
    }
    if (store !== undefined) {
      await store.query(predicates).then((resultSet: relationalStore.ResultSet) => {
        if (resultSet.rowCount < ResourceManager.ROW_EXIST_THRESHOLD){
          AiLog.info(this.domain, methodName, this.TAG, `no target version in the table, row count: ${resultSet.rowCount}`);
        } else {
          resultSet.goToFirstRow();
          result = this.getResInfo(resultSet);
        }
        resultSet.close();
      }).catch((err: BusinessError) => {
        AiLog.info(this.domain, methodName, this.TAG, `no verison in the database, error code: ${err.code}, error message: ${err.message}`);
      })
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `get rdbStore failed`);
    }
    return result;
  }

  /**
   * 检查数据库中目标resId的当前版本信息
   *
   * @param { string } resId 目标资源id
   * @returns { Promise<UpgradeConstants.reeVersionInfo | void> } 是否存在当前版本信息,如果存在则返回对应信息
   */
  public async getCurrentVersion(resId: string, realMachineTestRes: string, originId?: string): Promise<UpgradeConstants.resVersionInfo | void> {
    const methodName: string = "getCurrentVersion";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig)
    let result: UpgradeConstants.resVersionInfo|undefined =undefined;

    let predicates = new relationalStore.RdbPredicates(UpgradeConstants.resVersionInfoTable.TABLE);
    if (originId) {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, originId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.IS_CURRENT, "true")
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    } else {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.IS_CURRENT, "true")
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    }
    if (store !== undefined) {
      await store.query(predicates).then((resultSet: relationalStore.ResultSet) => {
        AiLog.info(this.domain, methodName, this.TAG, `ResultSet columns name: ${resultSet.columnNames}, rows count: ${resultSet.rowCount}`);
        if (resultSet.rowCount < ResourceManager.ROW_EXIST_THRESHOLD){
          AiLog.info(this.domain, methodName, this.TAG, `no current version in the table, ${resultSet.rowCount}`);
        } else {
          //当前表中存在目标版本信息
          resultSet.goToFirstRow();
          result = this.getResInfo(resultSet);
        }
        resultSet.close();
      }).catch((err: BusinessError) => {
        AiLog.info(this.domain, methodName, this.TAG, `query failed with error code: ${err.code}, error message: ${err.message}`);
      })
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `get rdbStore failed`);
    }
    return result;
  }

  /**
   * 检查数据库中目标resId的全量版本，返回全量版本列表，列表最后一项为最新版本的更新时间
   *
   * @param { string } 目标资源id
   * @return { Promise< Array<string>|void } 返回全量版本列表
   */
  public async getAllVersion(resId: string, realMachineTestRes: string, originId?: string): Promise< Array<string> > {
    const methodName: string = "getAllVersion";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig);
    let resultArray: Array<string> = new Array();

    let predicates = new relationalStore.RdbPredicates(UpgradeConstants.resVersionInfoTable.TABLE);
    if (originId) {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, originId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    } else {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    }
    if (store !== undefined) {
      //创建数组记录表中存放的版本信息，如果至少有一个版本，则数组最后再添加最新版本的更新时间，如果一个版本也没有，则添加“no version”，计算版本数量则取数组长度-1
      await store.query(predicates).then((resultSet: relationalStore.ResultSet) => {
        if (resultSet.rowCount < ResourceManager.ROW_EXIST_THRESHOLD) {
          AiLog.info(this.domain, methodName, this.TAG, `no target version in the table, row count: ${resultSet.rowCount}`);
          resultArray.push("no version")
        } else {
          while (resultSet.goToNextRow()) {
            let version: string
            if (originId) {
              version = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.VERSION))
            } else {
              version = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.RES_VERSION))
            }
            resultArray.push(version);
          }
          resultSet.goToLastRow();
          resultArray.push(resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.UPDATE_TIME)));
        }
        resultSet.close();
      }).catch((err: BusinessError) => {
        AiLog.info(this.domain, methodName, this.TAG, `no resource verison in the database, error code: ${err.code}, error message: ${err.message}`);
        resultArray.push("no version");
      })
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `get rdbStore failed`);
      resultArray.push("no version");
    }
    return resultArray;
  }

  /**
   * 向数据库中添加信息
   *
   * @param { string } table - 要增添信息的表单
   * @param { ValueBucket } valueBucket - 要增添的信息
   */
  public async addInfo(table: string, valueBucket: ValuesBucket): Promise<void> {
    const methodName: string = "addInfo";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig)
    //插入数据
    if (store !== undefined) {
      await (store as relationalStore.RdbStore).insert(table,valueBucket).then((rowId:number) => {
        AiLog.info(this.domain, methodName, this.TAG, `insert successfully, rowId = ${rowId}`);
      }).catch((err: BusinessError) => {
        AiLog.error(this.domain, methodName, this.TAG, `insert failed, code is ${err.code}. message is ${err.message}`);
      })
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `get RdbStore failed, can not insert`);
    }
  }

  /**
   * 更新版本表中资源信息。
   *
   * @param { string } table  待更新的表格
   * @param { ValueBucket } valueBucket  要更新的信息
   * @param { string } resId  被更新信息的id
   * @param { string } version 待更新的版本号
   */
  public async updateInfo(valueBucket: ValuesBucket, resId: string, version: string, realMachineTestRes: string, originId?: string): Promise<void> {
    const methodName: string = "updateInfo";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig);
    let predicates = new relationalStore.RdbPredicates(UpgradeConstants.resVersionInfoTable.TABLE);
    if (originId) {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID,resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, originId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.VERSION, version)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    } else {
      predicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.RES_VERSION, version)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    }
    if (store !== undefined) {
      await store.update(valueBucket, predicates).then(async (rows: number) => {
        AiLog.info(this.domain, methodName, this.TAG, `update successfully, row count = ${rows}`);
      }).catch((err: BusinessError) => {
        AiLog.error(this.domain, methodName, this.TAG, `update failed, err code is ${err.code}, err message is ${err.message}`);
      })
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `update failede, get RdbStore failed`);
    }
  }

  /**
   * 删除数据库中最老的，非当前的版本信息
   *
   * @param { string } resId 目标资源id
   * @returns { Promise<string|void> } 返回删除的版本资源的路径
   */
  public async deleteInfo(resId: string, realMachineTestRes: string, delCur: boolean, originId?: string): Promise<string | void> {
    const methodName: string = "deleteInfo";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig)
    //获取当前数据库中存放的最老的版本的索引，由于数据库中添加信息都是在最新一行添加，因此最老的版本就是索引最小的版本
    let queryPredicates = new relationalStore.RdbPredicates(UpgradeConstants.resVersionInfoTable.TABLE);
    if (originId) {
      queryPredicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, originId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    } else {
      queryPredicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
        .and()
        .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
    }
    let path: string|undefined = undefined;
    if (store !== undefined) {
      await store.query(queryPredicates).then(async (resultSet: relationalStore.ResultSet) => {
        AiLog.info(this.domain, methodName, this.TAG, `query row count: ${resultSet.rowCount}`);
        resultSet.goToFirstRow();
        if (!delCur) {
          let isCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.IS_CURRENT));
          if (isCur === "true") {
            resultSet.goToNextRow();
          }
        }
        path = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.FILE_PATH));
        let deletePredicates = new relationalStore.RdbPredicates(UpgradeConstants.resVersionInfoTable.TABLE);
        deletePredicates.equalTo(UpgradeConstants.resVersionInfoTable.RES_ID, resId)
          .and()
          .equalTo(UpgradeConstants.resVersionInfoTable.FILE_PATH, path)
          .and()
          .equalTo(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES, realMachineTestRes);
        await store.delete(deletePredicates).then((row:number) => {
        }).catch((err:BusinessError) => {
          AiLog.error(this.domain, methodName, this.TAG, `delete failed, err code is ${err.code}, err message is ${err.message}`);
        })
      }).catch((err:BusinessError) => {
        AiLog.error(this.domain, methodName, this.TAG, `query failed, err code is ${err.code}, err message is ${err.message}`);
      })
    } else {
      AiLog.error(this.domain, methodName, this.TAG, `delete failed, can not get databse`);
    }
    return path;
  }

  /**
   * 删除数据库中的订阅资源信息
   *
   * @param { string } resId 目标资源id
   * @param { string } originId 目标originId
   * @returns { Promise<boolean> } 返回成功或失败
   */
  public async deleteSubInfo(resId: string, originId?: string): Promise<void> {
    const methodName: string = "deleteSubInfo";
    let store = await relationalStore.getRdbStore(this.context, this.storeConfig);
    let predicates = new relationalStore.RdbPredicates(UpgradeConstants.subscribeInfoTable.TABLE);
    if (originId) {
      predicates.equalTo(UpgradeConstants.subscribeInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.subscribeInfoTable.ORIGIN_ID, originId)
    } else {
      predicates.equalTo(UpgradeConstants.subscribeInfoTable.RES_ID, resId)
        .and()
        .equalTo(UpgradeConstants.subscribeInfoTable.ORIGIN_ID, ResourceManager.EMPTY_STRING)
    }
    await store.delete(predicates).then((res:number) => {
      AiLog.info(this.domain, methodName, this.TAG, `delete success, ${res} row is deleted, resId: ${resId}, originId: ${originId}`);
    }).catch((err: BusinessError) => {
      AiLog.error(this.domain, methodName, this.TAG, `delete failed, err code is ${err.code}, err message is ${err.message}`);
    })
  }

  /**
   * 根据数据库的查询结果构造版本信息
   *
   * @param { relationalStore.ResultSet } resultSet 数据库查询的结果集
   * @returns { UpgradeConstants.resVersionInfo }  返回升级管理的资源版本信息
   */
  private getResInfo(resultSet: relationalStore.ResultSet): UpgradeConstants.resVersionInfo {
    let resIdCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.RES_ID));
    let resVersionCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.RES_VERSION));
    let domainCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.DOMAIN));
    let isCurrentCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.IS_CURRENT));
    let filePathCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.FILE_PATH));
    let updateTimeCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.UPDATE_TIME));
    let bundleNameCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.BUNDLE_NAME));
    let originIdCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.ORIGIN_ID));
    let versionCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.VERSION));
    let realMachineTestRes = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.resVersionInfoTable.REAL_MACHINE_TEST_RES));
    let result: UpgradeConstants.resVersionInfo = {
      resId: resIdCur,
      resVersion: resVersionCur,
      domain: domainCur,
      isCurrent: isCurrentCur,
      filePath: filePathCur,
      updateTime: updateTimeCur,
      bundleName: bundleNameCur,
      originId: originIdCur,
      version: versionCur,
      realMachineTestRes: JSON.parse(realMachineTestRes) as boolean,
    }
    return result;
  }

  /**
   * 根据数据库的查询结果构造订阅信息
   *
   * @param { relationalStore.ResultSet } resultSet 数据库查询的结果集
   * @returns { UpgradeConstants.SubscribeInfo }  返回升级管理的资源版本信息
   */
  private getSubInfo(resultSet: relationalStore.ResultSet): UpgradeConstants.SubscribeInfo {
    let resIdCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.subscribeInfoTable.RES_ID));
    let versionCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.subscribeInfoTable.RES_VERSION));
    let domainCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.subscribeInfoTable.DOMAIN));
    let bundleNameCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.subscribeInfoTable.BUNDLE_NAME));
    let serviceNameCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.subscribeInfoTable.SERVICE_NAME));
    let netCur = resultSet.getLong(resultSet.getColumnIndex(UpgradeConstants.subscribeInfoTable.NET));
    let originCur = resultSet.getString(resultSet.getColumnIndex(UpgradeConstants.subscribeInfoTable.ORIGIN_ID));
    let result: UpgradeConstants.SubscribeInfo = {
      bundleName: bundleNameCur,
      serviceName: serviceNameCur,
      resId: resIdCur,
      domain: domainCur,
      isSupportMobileNet: netCur,
      resVersion: versionCur,
      originId: originCur
    }
    return result;
  }

  /*
   * 获取键值数据库实例
   *
   * @returns { ResourceManager } distributedKVStore实例
   */
  private async createKVStore(): Promise<distributedKVStore.SingleKVStore> {
    const methodName: string = "createKVStore"
    AiLog.info(this.domain, methodName, this.TAG, `createKVStore start`);
    let kvStore: distributedKVStore.SingleKVStore = undefined;
    const kvManagerConfig: distributedKVStore.KVManagerConfig = {
      context: this.context,
      bundleName: this.context.applicationInfo.name,
    }
    let kvManager = distributedKVStore.createKVManager(kvManagerConfig);
    if(!kvManager) {
      AiLog.error(this.domain, methodName, this.TAG, `get kvManager failed`);
      return kvStore;
    }
    try {
      AiLog.info(this.domain, methodName, this.TAG, `get kvManager success`);
      kvStore =  await kvManager.getKVStore<distributedKVStore.SingleKVStore>(ResourceManager.UPGRADE_KV_DATABASE, this.kvStoreOptions);
    } catch (e) {
      let error = e as BusinessError;
      AiLog.error(this.domain, methodName, this.TAG, `createKVStore failed, code is ${error.code}, message is ${error.message}`);
    }
    return kvStore;
  }

  /*
   * 根据key从键值数据库取对应的值
   *
   * @param { string } key - 上下文
   * @returns { Promise<boolean | string | number> } key对应的值
   */
  public async getKV(key: string): Promise<boolean | string | number> {
    const methodName: string = 'getKV';
    AiLog.info(this.domain, methodName, this.TAG, `getKV start`);
    return new Promise<boolean> (async resolve=>{
      let store = await this.createKVStore();
      if(!store) {
        AiLog.info(this.domain, methodName, this.TAG, `createKVStore failed`);
        resolve(undefined);
      }
      try {
        store.get(key).then((r) => {
          AiLog.info(this.domain, methodName, this.TAG, `get success, key=${key} value=${r}`);
          resolve(r as boolean);
        }).catch((err: BusinessError)=>{
          AiLog.info(this.domain, methodName, this.TAG, `get failed, code=${err.code} message=${err.message}`);
          resolve(undefined);
        })
      } catch (e) {
        let error = e as BusinessError;
        AiLog.info(this.domain, methodName, this.TAG, `try get failed, code=${error.code} message=${error.message}`);
        resolve(undefined);
      }
    })
  }

  /*
   * 键值数据库插入键值
   *
   * @param { string } key - 上下文
   * @param { boolean | string | number } value - 上下文
   * @returns { Promise<number> } 操作状态: 1成功, 0失败
   */
  public async setKV(key: string, value: boolean | string | number): Promise<number> {
    const methodName: string = 'setKV';
    AiLog.info(this.domain, methodName, this.TAG, `setKV start`);
    return new Promise<number>(async resolve=> {
      let store = await this.createKVStore();
      let res = 0;
      if(!store) {
        AiLog.info(this.domain, methodName, this.TAG, `createKVStore failed`);
        resolve(res);
      }
      try {
        store.put(key, value).then(() => {
          res = 1;
          AiLog.info(this.domain, methodName, this.TAG, `set success, key=${key} value=${value}`);
          resolve(res);
        }).catch((err: BusinessError) => {
          AiLog.info(this.domain, methodName, this.TAG, `set failed, code=${err.code} message=${err.message}`);
          resolve(res);
        })
      } catch (e) {
        let error = e as BusinessError;
        AiLog.info(this.domain, methodName, this.TAG, `try set failed, code=${error.code} message=${error.message}`);
        resolve(res);
      }
    })
  }

  /*
   * 键值数据库删除指定键值
   *
   * @param { string } key - 上下文
   * @returns { Promise<number> } 操作状态: 1成功, 0失败
   */
  public async deleteKV(key: string): Promise<number> {
    const methodName: string = 'deleteKV';
    AiLog.info(this.domain, methodName, this.TAG, `deleteKV start`);
    return new Promise<number>(async resolve => {
      let store = await this.createKVStore();
      let res = 0;
      if(!store) {
        AiLog.info(this.domain, methodName, this.TAG, `createKVStore failed`);
        resolve(res);
      }
      try {
        store.delete(key).then(() => {
          res = 1;
          AiLog.info(this.domain, methodName, this.TAG, `delete success, key=${key}`);
          resolve(res);
        }).catch((err: BusinessError) => {
          AiLog.info(this.domain, methodName, this.TAG, `set failed, code=${err.code} message=${err.message}`);
          resolve(res);
        })
      } catch (e) {
        let error = e as BusinessError;
        AiLog.info(this.domain, methodName, this.TAG, `try set failed, code=${error.code} message=${error.message}`);
        resolve(res);
      }
    })
  }
}