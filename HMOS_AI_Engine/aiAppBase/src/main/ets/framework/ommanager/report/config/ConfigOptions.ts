/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import hiAnalytics from '@hms.core.hiAnalytics'
/**
 * the typed define
 */
export enum ReportPlatformType {
    HA = "ha"
}

export enum NetworkType {
    BUSINESS_NETWORK = "business_net",
    TEST_NETWORK = "test_net"
}

export enum EventType {
    OPERATION = 0,
    MAINTENANCE = 1,
}

export interface MaintenanceConfig extends hiAnalytics.EventTypeConfig {
    collectUrl: string;
    presetProperties?: Array<hiAnalytics.PresetProperty>;
    commonProperty?: Record<string, string>;
  }

export interface OperationConfig extends hiAnalytics.EventTypeConfig {
    collectUrl: string;
    presetProperties?: Array<hiAnalytics.PresetProperty>;
    commonProperty?: Record<string, string>;
  }

export interface Config extends hiAnalytics.InstanceParams {
    appId?: string,
    instanceTag: string;
    maintenanceConfig: MaintenanceConfig;
    operationConfig: OperationConfig;
}

export interface ConfigParam extends Object {
    reportPlatformType: ReportPlatformType;
    networkType: NetworkType;
    instanceTag: string;
    appId?:string;
    presetProperties?: Array<hiAnalytics.PresetProperty>;
}

export interface EventData extends Record<string, string> {

}

export interface ReportInfo extends Object{
    reportType: EventType;
    eventId: string;
    eventData: EventData;
}


