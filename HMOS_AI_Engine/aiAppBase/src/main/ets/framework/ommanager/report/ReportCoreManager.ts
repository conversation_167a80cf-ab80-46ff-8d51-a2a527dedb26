/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import common from '@ohos.app.ability.common';
import hiAnalytics from '@hms.core.hiAnalytics';
import * as Config from "./config/ConfigOptions"
import CONSTANT from '../report/Constant';
import { AiLog } from '../logmanager/AiLog';
import { DOMAIN } from '../loggermanager/LoggerConstant';
import Constant from '../report/Constant';
import buffer from '@ohos.buffer';

const TAG: string = "ReportCoreManager";

type commonContext = common.UIAbilityContext | common.ServiceExtensionContext | common.Context;

/**
 * This class is a report management class.
 * The information of reporting can be found { MaintenanceReportInfo, OperatorReportInfo }.
 * @see { MaintenanceReportInfo, OperatorReportInfo }
 * <AUTHOR>
 */
export class ReportCoreManager {
    context: commonContext;

    private static reportManager: ReportCoreManager;
    static instanceTag: string = CONSTANT.EMPTY_STRING;

    /**
     * The constructor of this class.
     * @class
     */
    private constructor(context: commonContext) {
        this.context = context;
    }

    /**
     * Get an instance object of this class.
     *
     * @param { Context.AbilityContext } the ability context
     * @returns { ReportCoreManager } the instance of ReportCoreManager
     */
    static getInstance(context: commonContext): ReportCoreManager {
        if (!this.reportManager) {
            this.reportManager = new ReportCoreManager(context);
            return this.reportManager;
        }
        return this.reportManager;
    }

    public static async setConfigOptions(context: commonContext, configOptionsData: Config.ConfigParam): Promise<void> {
        AiLog.info(DOMAIN, Constant.MODULE_REPORT, TAG, `setConfigOptions`);
        let myResourceManager = context.resourceManager;
        let urlBuffer = await myResourceManager.getRawFileContent("aiAppBaseReportUrl.json");
        let url: string
        if (configOptionsData.networkType === Config.NetworkType.BUSINESS_NETWORK) {
            url = JSON.parse(buffer.from(urlBuffer.buffer).toString()).business_net;
        } else {
            AiLog.error(DOMAIN, Constant.MODULE_REPORT, TAG, `current version do not support test network`);
            return;
        }
        let configOptions: Config.Config = {
            instanceTag: configOptionsData.instanceTag,
            maintenanceConfig: {
                collectUrl: url,
            },
            operationConfig: {
                collectUrl: url,
            },
        };
        if (Object.prototype.hasOwnProperty.call(configOptionsData, "appId")) {
            configOptions.appId = configOptionsData.appId;
        }
        if (Object.prototype.hasOwnProperty.call(configOptionsData, "presetProperties")) {
            configOptions.maintenanceConfig.presetProperties = configOptionsData.presetProperties;
            configOptions.operationConfig.presetProperties = configOptionsData.presetProperties;
        }
        ReportCoreManager.instanceTag = configOptionsData.instanceTag;
        AiLog.info(DOMAIN, Constant.MODULE_REPORT, TAG, `setConfigOptions instanceTag:${ReportCoreManager.instanceTag}`);
        await hiAnalytics.setConfigOptions(configOptions).then(() => {
            AiLog.info(DOMAIN, Constant.MODULE_REPORT, TAG, "SetConfigOption   Success");
        }).catch(err => {
            AiLog.info(DOMAIN, Constant.MODULE_REPORT, TAG, "SetConfigOption   Failed: " + err.message);
        });
    }

    onReport(reportData: Config.ReportInfo): void {
        AiLog.info(DOMAIN, Constant.MODULE_REPORT, TAG, `onReport instanceTag: ${ReportCoreManager.instanceTag},
            reportType: ${reportData.reportType}, eventId: ${reportData.eventId}`);
        if (ReportCoreManager.instanceTag === CONSTANT.EMPTY_STRING) {
            AiLog.error(DOMAIN, Constant.MODULE_REPORT, TAG, `lack of instanceTAG, please set config of report`);
            return ;
        }
        if (reportData.eventId === CONSTANT.EMPTY_STRING) {
            AiLog.error(DOMAIN, Constant.MODULE_REPORT, TAG, `lack of eventId, please set eventId`);
            return ;
        }
        hiAnalytics.onEvent(ReportCoreManager.instanceTag, reportData.reportType,
            reportData.eventId, reportData.eventData).then(() => {
            AiLog.info(DOMAIN, Constant.MODULE_REPORT, TAG, "onReport  onEvent  Success");
        }).catch(err => {
            AiLog.info(DOMAIN, Constant.MODULE_REPORT, TAG, "onReport  onEvent   Failed: " + err.message);
        });
    }
}