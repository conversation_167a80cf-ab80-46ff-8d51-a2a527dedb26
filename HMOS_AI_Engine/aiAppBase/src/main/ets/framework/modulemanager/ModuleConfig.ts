/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { BaseLogicModule } from './BaseLogicModule';

/*
 * 每个worker对应唯一的容器标识，全局唯一
 * */
enum ContainerId {
    THREAD_MAIN = 'mainThread',
    WORKER1 = 'worker1',
    WORKER2 = 'worker2',
    WORKER3 = 'worker3',
    WORKER4 = 'worker4',
    WORKER5 = 'worker5',
    WORKER6 = 'worker6',
    WORKER7 = 'worker7'
}

/*
 * worker配置的类型定义
 * */
interface WorkerConfig {
    // worker的id
    containerId: ContainerId,

    // worker入口文件, worker初始化的时候需要传入这个参数
    workerEntry: string,
}

/**
 * worker配置，配置各个worker对应的容器Id以及入口文件
 * 容器即模块运行的环境，当前使用worker实现容器，后续系统支持actor后，可以将容器切换至actor
 */
const allWorkers: WorkerConfig[] = [
    {
        containerId: ContainerId.WORKER1,
        workerEntry: "/ets/framework/workers/worker1.ts"
    },
    {
        containerId: ContainerId.WORKER2,
        workerEntry: "/ets/framework/workers/worker2.ts"
    },
    {
        containerId: ContainerId.WORKER3,
        workerEntry: "/ets/framework/workers/worker3.ts"
    },
    {
        containerId: ContainerId.WORKER4,
        workerEntry: "/ets/framework/workers/worker4.ts"
    },
    {
        containerId: ContainerId.WORKER5,
        workerEntry: "/ets/framework/workers/worker5.ts"
    },
    {
        containerId: ContainerId.WORKER6,
        workerEntry: "/ets/framework/workers/worker6.ts"
    },
    {
        containerId: ContainerId.WORKER7,
        workerEntry: "/ets/framework/workers/worker7.ts"
    }
]

// 模块Id的类型定义
type ModuleId = string;

// 模块启动的优先级的类型定义
enum InitLevel {
    LEVEL_0 = '0',
    LEVEL_1 = '1',
    LEVEL_2 = '2',
    LEVEL_3 = '3',
};

/*
 * 模块配置的类型定义
 * */
interface ModuleConfig {
    // 模块Id，用于唯一标识一个模块，全局唯一
    moduleId: ModuleId,

    // 模块实现类，是模块初始化、收发消息的入口
    moduleClass: { new(): BaseLogicModule },

    // 模块运行的线程标识
    containerId: ContainerId,

    // 启动优先级, 0 1 2 3  0优先级最高,依次递减
    initLevel: InitLevel,
}

// 发送消息的源模块名称：模块管理
const MODULE_MANAGE: ModuleId = "ModuleManager";

// 模块启动阶差，每提升一个等级，增加100ms时延
const MODULE_INIT_STEP: number = 100;

// 导出线程配置以及模块配置
export { ContainerId, WorkerConfig, allWorkers, ModuleConfig, ModuleId, InitLevel }

// 模块管理相关配置
export { MODULE_MANAGE, MODULE_INIT_STEP }
