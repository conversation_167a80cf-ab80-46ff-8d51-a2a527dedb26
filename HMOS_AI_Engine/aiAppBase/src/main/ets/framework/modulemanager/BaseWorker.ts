/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { ThreadWorkerGlobalScope } from '@ohos.worker';
import common from '@ohos.app.ability.common';
import { Message, MessageConfig, MessageName } from '../messagebus/Message';
import { ContainerId, ModuleConfig, ModuleId, MODULE_MANAGE } from './ModuleConfig';
import { BaseLogicModule } from './BaseLogicModule';
import { msgBusManager } from '../messagebus/MsgBusManager';
import { MSG_BOOTSTRAP_WORKER, MSG_REBOOT } from '../messagebus/MsgBusConfig';
import { AiLog } from '../../../../../Index';

let DOMAIN: number;

/**
 * 提供worker线程能力，负责线程的初始化
 * 各worker入口文件需要加载并初始化BaseWorker，BaseWorker会自动接管worker线程并根据配置初始化模块
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
export class BaseWorker {
    private readonly TAG: string = 'BaseWorker';
    // 当前线程id
    private workerId: ContainerId | null = null;
    private moduleId: string;
    // 模块配置
    private moduleConfigs: ModuleConfig[];
    // 记录本线程运行模块id和句柄的映射关系
    private moduleInstance: Map<ModuleId, BaseLogicModule>;
    // 记录模块id与所在线程id的映射关系
    private moduleThreadMap: Map<ModuleId, ContainerId>;

    /**
     * 构造启动器时需要传入线程配置和模块配置，启动器根据线程以及模块配置进行启动初始化。
     * @param moduleConfigs 模块配置
     */
    public constructor(moduleConfigs: ModuleConfig[], workerPort?: ThreadWorkerGlobalScope) {
        if(workerPort){
            AiLog.info(DOMAIN ?? 0x0000, this.workerId ?? '', this.TAG, `constructor onmessage start.`);
            this.init(workerPort);
        }
        this.moduleInstance = new Map();
        this.moduleConfigs = moduleConfigs;
        this.moduleThreadMap = new Map();
    }

    /**
     * 初始化worker线程启动器，配置worker线程消息处理
     * @param workerPort worker端口，和主线程通信的通道
     */
    public init(workerPort: ThreadWorkerGlobalScope): void {
        AiLog.info(DOMAIN ?? 0x0000, this.workerId ?? '', this.TAG, `init onmessage start.`);
        // worker线程的消息处理：1）worker初始化消息由初始化函数处理；2）模块消息都交由对应模块
        workerPort.onmessage = (message): Promise<void> | undefined => {
            let messageData = message.data as Message;
            let messageName = messageData.header.messageName;
            AiLog.info(DOMAIN ?? 0x0000, this.workerId ?? '', this.TAG, `onmessage name: ${messageName}`);
            // 1）如果消息名称是模块初始化消息，则触发模块初始化
            if (messageName === MSG_BOOTSTRAP_WORKER) {
                this.workerThreadInit(messageData, workerPort);
                return;
            }
            if (messageName === MSG_REBOOT) {
                this.updateRebootData(messageData.contentData);
                return;
            }
            // 2）模块消息交由消息总线转交对应模块处理
            msgBusManager.handleMessage(messageData, false);
            return;
        };
    }

    // 同步重启后的全局变量
    private updateRebootData(content: Object): void {
        this.moduleThreadMap = content['moduleThreadMap'] as Map<ModuleId, ContainerId>;
        AiLog.info(DOMAIN, this.workerId ?? '', this.TAG, "updateRebootData moduleThreadMap: %{public}s", this.moduleThreadMap);
        msgBusManager.setModuleThreadMap(this.moduleThreadMap);
    }

    private workerThreadInit(message: Message, workerPort: ThreadWorkerGlobalScope): void {
        // 解析传入的消息：获取线程Id和名称
        let content = message.contentData;
        DOMAIN = content['domain'] as number;
        this.workerId = content['workerId'] as ContainerId;
        AiLog.info(DOMAIN, this.workerId, this.TAG, "start thread: %{public}s in worker", this.workerId);
        let appContext = content['appContext'] as common.Context;
        let messageConfigMap = content['messageConfigMap'] as Map<MessageName, MessageConfig>;
        this.moduleThreadMap.set(MODULE_MANAGE, ContainerId.THREAD_MAIN);
        // 1. 依次遍历模块，找到当前线程运行的模块，进行模块创建
        this.moduleConfigs.forEach(module => {
            // 如果module运行线程为当前线程，则创建模块
            if (module.containerId === this.workerId) {
                AiLog.info(DOMAIN, this.workerId, this.TAG, "module %{public}s run in %{public}s thread",
                    module.moduleId, this.workerId);
                let moduleInstance: BaseLogicModule = (new module.moduleClass()) as BaseLogicModule;
                moduleInstance.setLogicModuleId(module.moduleId);
                moduleInstance.appContext = appContext;
                this.moduleInstance.set(module.moduleId, moduleInstance);
                this.moduleId = module.moduleId;
            }
            this.moduleThreadMap.set(module.moduleId, module.containerId);
        });
        // 2. 初始化子线程模块消息总线
        msgBusManager.setCurrentThread(this.workerId);
        msgBusManager.setWorkerPort(workerPort);
        msgBusManager.setAppContext(appContext);
        msgBusManager.setDomain(DOMAIN)
        msgBusManager.setModuleThreadMap(this.moduleThreadMap);
        msgBusManager.setModuleHandlerMap(this.moduleInstance);
        msgBusManager.setMessageConfigMap(messageConfigMap);
        message.header.targetModuleId = this.moduleId;
        msgBusManager.handleMessage(message, false);
    }
}