/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { msgBus } from '../messagebus/MsgBus';
import { AskId, Header, Message, MessageName, Tag } from '../messagebus/Message';
import { RequestType } from '../messagebus/Message';
import { ModuleId } from './ModuleConfig';
import { INIT_MODULE, MSG_BOOTSTRAP_WORKER } from '../messagebus/MsgBusConfig';
import common from '@ohos.app.ability.common';

/**
 * 逻辑模块虚拟基类，各模块继承该基类才能使用消息总线进行消息收发
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
export abstract class BaseLogicModule {
    // 应用上下文
    public appContext: common.Context | null = null;

    // 模块Id
    protected moduleId: ModuleId = '';

    /**
     * 各module自己的初始化逻辑，会在模块初始化时调用
     */
    public abstract init(): void;

    /**
     * 内部默认消息处理
     *
     * @param message
     */
    public async handleMsg(message: Message): Promise<void> {
        let targetModule = message.header.sourceModuleId;
        let messageName = message.header.messageName;
        let askId = message.header.askId;
        let tag = message.header.tag;
        if (message.header.messageName === INIT_MODULE) {
            this.init();
            this.answer(targetModule, messageName, askId, null, tag);
            return;
        }
        if (message.header.messageName === MSG_BOOTSTRAP_WORKER) {
            this.answer(targetModule, messageName, askId, null ,tag);
            return;
        }
        let answerData = await this.onAnswer(message);
        if (askId != null) {
            this.answer(targetModule, messageName, askId, answerData, tag);
        }
    }

    /**
     * 处理消息，由消息总线调用，各模块实现该接口，处理消息并返回处理结果
     *
     * @param message 发送的消息
     */
    public abstract onAnswer(message: Message): Promise<Object>;

    /**
     * 获取逻辑模块ID
     */
    public setLogicModuleId(id: ModuleId): void {
        this.moduleId = id;
    }

    /**
     * 发送消息到指定模块，消息总线根据路由信息找到目标模块，将详细发送过去
     *
     * @param targetModule 目标模块
     * @param messageName 模块名称
     * @param messageData 消息内容
     * @param tag 消息tag，用于代码定位使用，使用方可以根据日志中的tag查找发消息的代码，区分同一个消息不同发送函数的情况
     */
    public tell(targetModule: ModuleId, messageName: MessageName, messageData: Object, tag?: Tag): void {
        let header: Header = {
            targetModuleId: targetModule,
            sourceModuleId: this.moduleId,
            messageName: messageName,
            requestType: RequestType.TELL,
            tag: tag
        }
        msgBus.tell(header, messageData);
    }

    /**
     * 发送消息到指定模块，同时异步返回响应消息
     *
     * @param targetModule 目标模块
     * @param messageName 模块名称
     * @param messageData 消息内容
     * @param timeout 消息响应超时时间, 单位毫秒(ms)
     * @param tag 消息tag，用于代码定位使用，使用方可以根据日志中的tag查找发消息的代码，区分同一个消息不同发送函数的情况
     * @returns 目标模块响应消息异步结果
     */
    public ask(targetModule: ModuleId, messageName: MessageName, messageData: Object, tag?: Tag, timeout?: number): Promise<Message> {
        let header: Header = {
            targetModuleId: targetModule,
            sourceModuleId: this.moduleId,
            messageName: messageName,
            requestType: RequestType.ASK,
            timeout: timeout,
            tag: tag
        }
        return msgBus.ask(header, messageData);
    }

    /**
     * 返回ask的响应消息
     *
     * @param targetModule 目标模块
     * @param messageName 模块名称
     * @param askId ask请求的唯一标识
     * @param messageData 消息内容
     * @param tag 消息tag，用于代码定位使用，使用方可以根据日志中的tag查找发消息的代码，区分同一个消息不同发送函数的情况
     */
    private answer(targetModule: ModuleId, messageName: MessageName, askId: AskId, messageData: Object, tag?: Tag): void {
        let header: Header = {
            targetModuleId: targetModule,
            sourceModuleId: this.moduleId,
            messageName: messageName,
            requestType: RequestType.ANSWER,
            askId: askId,
            tag: tag
        }
        msgBus.answer(header, messageData);
    }
}