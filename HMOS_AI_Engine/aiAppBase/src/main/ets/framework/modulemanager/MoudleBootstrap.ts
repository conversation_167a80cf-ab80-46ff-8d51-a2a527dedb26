/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import worker from '@ohos.worker';
import common from '@ohos.app.ability.common';
import { Header, Message, MessageConfig, MessageName } from '../messagebus/Message';
import { RequestType } from '../messagebus/Message';
import { BaseLogicModule } from './BaseLogicModule';
import { ModuleConfig, ModuleId, WorkerConfig } from './ModuleConfig';
import { allWorkers, ContainerId, MODULE_INIT_STEP, MODULE_MANAGE } from './ModuleConfig';
import { msgBusManager } from '../messagebus/MsgBusManager';
import { INIT_MODULE, MSG_BOOTSTRAP_WORKER, MSG_REBOOT, TIMEOUT_MSG, WORKER_PROACTIVE_EXIT, WORKER_MANUAL_EXIT } from '../messagebus/MsgBusConfig';
import { msgBus } from '../messagebus/MsgBus';
import { AiLog } from '../../../../../Index';


let DOMAIN: number = 0;

export function getDomain(): number {
    return DOMAIN;
}

export interface AppBaseConfigs {
    moduleConfigInfo: ModuleConfig[],
    messageConfigInfo: MessageConfig[]
}

/**
 * 主线程启动器，运行在主线程，需要在应用启动时执行，先于具体业务执行。只有主线程启动完成，才能开始具体业务。
 * 主线程启动器负责拉起子线程，初始化主线程以及子线程消息总线、模块
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
export class ModuleBootstrap {
    // 单实例对象
    private static instance: ModuleBootstrap;
    private readonly TAG: string = "ModuleBootstrap";
    // 应用上下文
    private appContext: common.UIAbilityContext | common.ExtensionContext | null = null;
    // 启动源模块
    private bootHapModuleName: string = '';
    // 线程配置
    private workerConfigs: WorkerConfig[] = allWorkers;
    // 当前已经创建的模块的配置, 用于worker销毁之后的重启
    private allModuleConfigs: ModuleConfig[] = [];
    // 模块配置
    private moduleConfigs: ModuleConfig[] = [];
    // 模块消息管控配置
    private messageConfigs: MessageConfig[] = [];
    // 记录本线程运行模块id和句柄的映射关系
    private moduleInstance: Map<ModuleId, BaseLogicModule> = new Map();
    // 记录线程id和worker线程的映射关系
    private workerMap: Map<ContainerId, worker.ThreadWorker> = new Map();
    // 记录线程messageName和messageConfig消息转发规则的映射关系
    private messageConfigMap: Map<MessageName, MessageConfig> = new Map();
    // 标记是否重启
    private isReboot: boolean = false;
    // 记录模块id与所在线程id的映射关系
    private moduleThreadMap: Map<ModuleId, ContainerId> = new Map();
    // 记录启动或重启时的增量module
    private newlyModules: ModuleConfig[] = [];
    // 记录启动或重启时的增量module的相关线程,发送初始化消息到这些线程
    private newlyThreads: ContainerId[] = [];
    // 标记参数是否通过校验
    private isCheckSucceeded: boolean = false;
    // 记录由于异常导致销毁的worker的Id
    private crashWorkers: ContainerId[] = [];
    //记录重试次数，防止死循环
    private initTryCount: number = 0;

    public constructor() {
    }

    // 获取单实例
    public static getInstance(): ModuleBootstrap {
        if (!ModuleBootstrap.instance) {
            ModuleBootstrap.instance = new ModuleBootstrap();
        }
        return ModuleBootstrap.instance
    }

    /**
     * 初始化启动器时需要传入线程配置和模块配置，启动器根据线程以及模块配置进行启动初始化。
     * @param appContext 应用上下文
     * @param domain 日志对应的各个智慧业务的领域标识，范围0x0~0xFFFF
     * @param appBaseConfigs 基础框架配置参数,包括模块配置、消息配置、网络服务配置
     */
    public init(appContext: common.UIAbilityContext | common.ExtensionContext, domain: number, appBaseConfigs: AppBaseConfigs): void {
        DOMAIN = domain
        this.isCheckSucceeded = this.checkConfigs(appBaseConfigs.moduleConfigInfo, appBaseConfigs.messageConfigInfo);
        if(!this.isCheckSucceeded) {
            return;
        }
        if (!this.isReboot) {
            this.moduleInstance = new Map();
            this.workerMap = new Map();
            this.messageConfigMap = new Map();
            this.moduleThreadMap = new Map();
        }
        this.appContext = appContext;
        this.bootHapModuleName = appContext.currentHapModuleInfo.name
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "bootHapModuleName %{public}s", JSON.stringify(this.bootHapModuleName));
        this.moduleConfigs = appBaseConfigs.moduleConfigInfo;
        this.messageConfigs = appBaseConfigs.messageConfigInfo;
        this.initNewlyModuleThread()
    }

    /**
     * 启动器入口，开始启动
     */
    public async start(): Promise<boolean> {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start bootstrap");

        // 参数校验不通过, 不能启动框架
        if(!this.isCheckSucceeded) {
            return false;
        }

        // 如果重新启动框架, 没有新的需要初始化的worker和module, 直接返回true
        if(this.isReboot && this.newlyThreads.length === 0 && this.newlyModules.length === 0) {
            AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "is reboot, all the workers and modules are initialized");
            return true;
        }

        // 1. 启动线程：按照配置文件启动线程
        this.startWorkers(this.newlyThreads);

        // 2. 主线程初始化：创建主线程的模块对象，初始化主线程模块消息通道
        this.mainThreadInit();

        // 3. worker初始化：创建worker的模块对象，初始化worker模块消息通道
        let initRes = await this.workerInit(this.newlyThreads, false);
        if(!initRes) {
            AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "init worker failed!");
            let workers = this.newlyThreads.slice();
            for(let i = 0; i < workers.length; i++) {
                this.terminateWorker(workers[i]);
            }
            return false;
        }

        if(this.newlyModules.length > 0) {
            this.sendThreadMap(false);
        }
        // 4. 标记重启
        this.markerReboot();

        // 5. 启动各模块的业务初始化
        return this.initAllModule(this.newlyModules);
    }


    // 增量模块以及相关线程的数据初始化
    private initNewlyModuleThread(): void {
        this.newlyModules = new Array<ModuleConfig>();
        this.newlyThreads = new Array<ContainerId>();
        // 如果是重启
        if (this.isReboot) {
            AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "is reboot");
            this.moduleConfigs.forEach((module: ModuleConfig) => {
                // 如果线程已经初始化过,不能再初始化 and 如果这个模块之前没有初始化过, 说明这是个新的模块
                if (!this.workerMap.has(module.containerId) && !this.moduleThreadMap.has(module.moduleId)) {
                    // 如果这个模块不是在主线程初始化,记录它对应的线程id 并去重
                    if (module.containerId !== ContainerId.THREAD_MAIN && !this.newlyThreads.includes(module.containerId)) {
                        this.newlyThreads.push(module.containerId);
                    }
                    this.newlyModules.push(module);
                    this.addAllModuleConfig(module);
                } else {
                    // 这个模块在之前的启动中已经初始化过
                    AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "has been initialized this worker: %{public}s or this module: %{public}s",
                        JSON.stringify(module.containerId), JSON.stringify(module.moduleId));
                }
            })
        } else {
            AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "is first boot");
            // 如果是初次启动,所有模块都要初始化
            this.newlyModules = this.moduleConfigs;
            // 如果是初次启动,配置了模块的线程才发送初始化消息
            this.moduleConfigs.forEach(module => {
                if (module.containerId !== ContainerId.THREAD_MAIN && !this.newlyThreads.includes(module.containerId)) {
                    this.newlyThreads.push(module.containerId)
                }
                this.allModuleConfigs.push(module);
            })
        }
    }

    // 校验配置是否合规
    private checkConfigs(moduleConfigs: ModuleConfig[], messageConfigs: MessageConfig[]): boolean {
        if(!moduleConfigs) {
            AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "check configs failed --> moduleConfigs is null or undefined.");
            return false;
        }
        if(!messageConfigs) {
            AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "check configs failed --> messageConfigs is null or undefined.");
            return false;
        }
        if(moduleConfigs.length === 0) {
            AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "check configs failed --> moduleConfigs is empty.");
            return false;
        }
        return true;
    }

    private markerReboot(): void {
        if (!this.isReboot) {
            this.isReboot = true;
        }
    }

    private startWorkers(newlyThreads: ContainerId[]): void {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start workers: " + JSON.stringify(newlyThreads));
        // 启动worker线程
        this.workerConfigs.forEach(workerItem => {
            AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start workers, workerConfigs workerItem: " + JSON.stringify(workerItem));
            if (newlyThreads.includes(workerItem.containerId)) {
                AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start workers, newlyThreads includes this containerId: " + JSON.stringify(workerItem.containerId));
                AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start workers, new worker path: " + JSON.stringify(this.bootHapModuleName + workerItem.workerEntry));
                // 创建worker实例，启动worker
                let wk = new worker.ThreadWorker(this.bootHapModuleName + workerItem.workerEntry);
                AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start workers, new worker instance wk: " + JSON.stringify(wk));
                if (wk) {
                    // worker来的消息交由消息总线处理，发送到对应模块或转发到对应线程
                    wk.onmessage = (message): void => {
                        let messageData = message.data as Message;
                        msgBusManager.handleMessage(messageData, false);
                    };
                    wk.onexit = (code): void => {
                        this.deleteWorkerModuleRecord(workerItem.containerId);
                        this.notifyWorkerTerminated(workerItem.containerId, WORKER_PROACTIVE_EXIT + ', code: ' + code);
                    };
                    // 保存worker
                    AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, `new worker success: ${workerItem.containerId}` );
                    this.workerMap.set(workerItem.containerId, wk);
                } else {
                    AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, `the worker is undefined: ${workerItem.containerId}`);
                    let wk = new worker.ThreadWorker(this.bootHapModuleName + workerItem.workerEntry);
                    if (wk) {
                        // worker来的消息交由消息总线处理，发送到对应模块或转发到对应线程
                        wk.onmessage = (message): void => {
                            let messageData = message.data as Message;
                            msgBusManager.handleMessage(messageData, false);
                        };
                        wk.onexit = (code): void => {
                            this.deleteWorkerModuleRecord(workerItem.containerId);
                            this.notifyWorkerTerminated(workerItem.containerId, WORKER_PROACTIVE_EXIT + ', code: ' + code);
                        };
                        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, `renew worker success: ${workerItem.containerId}` );
                        this.workerMap.set(workerItem.containerId, wk);
                    } else {
                        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, `renew worker failed: ${workerItem.containerId}` );
                    }
                }
            } else {
                AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start workers, newlyThreads not this containerId: " + JSON.stringify(workerItem.containerId));
            }
        })
    }

    private addAllModuleConfig(module: ModuleConfig): void {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "addAllModuleConfig, module: %{public}s", JSON.stringify(module));
        let isIncludes = false;
        for(let i = 0; i < this.allModuleConfigs.length; i++) {
            let md = this.allModuleConfigs[i];
            if(md.containerId === module.containerId && md.moduleId === module.moduleId) {
                isIncludes = true;
                break;
            }
        }
        if(!isIncludes) {
            this.allModuleConfigs.push(module);
        }
    }

    private deleteWorkerModuleRecord(id: ContainerId): void {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "delete worker module record, for this worker: %{public}s", JSON.stringify(id));
        if(this.workerMap.has(id)) {
            this.workerMap.delete(id);
        }
        let moduleIdList: ModuleId[] = [];
        this.moduleThreadMap.forEach((workerId,moduleId) => {
            if(workerId === id) {
                moduleIdList.push(moduleId);
            }
        })
        moduleIdList.forEach(moduleId => {
            this.moduleThreadMap.delete(moduleId);
        })
        msgBusManager.setThreadMap(this.workerMap);
        msgBusManager.setModuleThreadMap(this.moduleThreadMap);
        this.sendThreadMap(true);
    }

    private notifyWorkerTerminated(id: ContainerId, message: string): void {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "has terminate worker: %{public}s", id);
        if(!this.crashWorkers.includes(id)) {
            this.crashWorkers.push(id);
        }
        this.onWorkerTerminated(id, message);
    }

    public onWorkerTerminated: (workerId: ContainerId, message: string) => void;

    /*
     * 销毁某个worker
     *
     * @params { ContainerId } id 待销毁的worker对应的Id
     * @return { Promise<boolean> } 销毁成功或失败
     * */
    public terminateWorker(id: ContainerId): Promise<boolean> {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "terminateWorker");
        return new Promise<boolean>(async resolve => {
            if(!this.workerMap.has(id)) {
                AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "not created this worker: %{public}s", id);
                resolve(false);
            }
            // 如果这个worker没有销毁, 主动调用销毁逻辑再重新启动
            let wk: worker.ThreadWorker = this.workerMap.get(id);
            wk.onexit = async (e): Promise<void> => {
                this.deleteWorkerModuleRecord(id);
                this.notifyWorkerTerminated(id, WORKER_MANUAL_EXIT);
                resolve(true);
            };
            AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start terminate this worker: %{public}s", id);
            wk.terminate();
        });
    }

    /*
     * 重新启动worker
     * 按照传入的workerId重启worker
     *
     * @params { ContainerId } id 待重启的worker对应的Id
     * @return { Promise<boolean> } 重启worker成功或失败
     * */
    public restartWorker(id: ContainerId): Promise<boolean> {
        return new Promise<boolean>(async resolve => {
            if(this.workerMap.has(id)) {
                AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "now, worker is running, can not restart this worker: %{public}s", id);
                if(this.crashWorkers.includes(id)) {
                    this.crashWorkers.splice(this.crashWorkers.indexOf(id),1);
                }
                resolve(false);
            }
            // 如果这个worker已经销毁, 重启worker
            if(this.crashWorkers.includes(id)) {
                // 重启worker
                let result = await this.restart(id);
                this.crashWorkers.splice(this.crashWorkers.indexOf(id),1);
                if(result) {
                    AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "restart worker success: %{public}s", id);
                } else {
                    AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "restart worker failed: %{public}s", id);
                }
                resolve(result);
            } else {
                AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "not terminated worker record: %{public}s", id);
                resolve(false);
            }
        });
    }

    /*
     * 重新启动某个worker
     * */
    private async restart(id: ContainerId): Promise<boolean> {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start restart worker: %{public}s", id);
        let newlyModules = new Array<ModuleConfig>();
        this.allModuleConfigs.forEach(module => {
            if(module.containerId === id) {
                newlyModules.push(module);
                this.moduleThreadMap.set(module.moduleId, id);
            }
        })
        this.startWorkers([id]);
        msgBusManager.setThreadMap(this.workerMap);
        msgBusManager.setModuleThreadMap(this.moduleThreadMap);
        let result = await this.workerInit([id], true);
        if (result) {
            this.sendThreadMap(true);
            return this.initAllModule(newlyModules);
        } else {
            AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "restart worker failed: %{public}s", id);
            return false;
        }
    }

    private mainThreadInit(): void {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start mainThreadInit, preModules: %{public}s", JSON.stringify(this.newlyModules));
        // 1. 依次遍历模块，找到当前线程运行的模块，进行模块创建
        this.newlyModules.forEach(module => {
            // 如果module运行线程为当前线程，则创建模块
            if (module.containerId === ContainerId.THREAD_MAIN) {
                AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "module %{public}s run in %{public}s thread",
                    module.moduleId, ContainerId.THREAD_MAIN);
                let moduleInstance: BaseLogicModule = (new module.moduleClass()) as BaseLogicModule;
                moduleInstance.setLogicModuleId(module.moduleId);
                moduleInstance.appContext = this.appContext;
                this.moduleInstance.set(module.moduleId, moduleInstance);
            }
            this.moduleThreadMap.set(module.moduleId, module.containerId);
        });
        // 遍历模块消息转发管控配置
        this.messageConfigs.forEach((item: MessageConfig) => {
            this.messageConfigMap.set(item.messageName, item)
        })
        // 2. 初始化主线程模块消息总线
        if (!this.isReboot) {
            msgBusManager.setCurrentThread(ContainerId.THREAD_MAIN);
            this.moduleThreadMap.set(MODULE_MANAGE, ContainerId.THREAD_MAIN);
        }
        msgBusManager.setThreadMap(this.workerMap);
        if (this.appContext) {
            msgBusManager.setAppContext(this.appContext);
        }
        msgBusManager.setDomain(DOMAIN);
        msgBusManager.setModuleThreadMap(this.moduleThreadMap);
        msgBusManager.setModuleHandlerMap(this.moduleInstance);
        msgBusManager.setMessageConfigMap(this.messageConfigMap);
    }

    private async workerInit(newlyThreads: ContainerId[], isRetry: boolean): Promise<boolean> {
        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "start workerInit, preThreads: " + JSON.stringify(newlyThreads));
        if (!newlyThreads.length) {
            return Promise.resolve(true);
        }
        let message: Message = {
            contentData: {},
            header: {
                // 初始化worker模块消息，不需要指定模块
                targetModuleId: '',
                sourceModuleId: MODULE_MANAGE,
                // 初始化worker模块消息名称
                messageName: MSG_BOOTSTRAP_WORKER,
                askId: undefined,
                requestType: RequestType.ASK,
                tag: '',
            },
        };
        // 初始化所有的worker线程
        return new Promise((resolve) => {
            let count = 0;
            let catchCount = 0;
            let size = newlyThreads.length;
            newlyThreads.forEach(containerId => {
                // 构造初始化module的消息，指示各线程初始化各自的模块
                message.contentData = {
                    // 将配置的线程id发送到目标线程
                    domain: DOMAIN,
                    workerId: containerId,
                    appContext: this.appContext,
                    messageConfigMap: this.messageConfigMap
                }
                // 发送到指定worker线程进行初始化
                // msgBusManager.postBusMsgToWorker(message, containerId);
                msgBus.initAsk(message, containerId).then((message: Message) => {
                    count++;
                    AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, `init worker ${JSON.stringify(containerId)} success`);
                    if (count === size) {
                        resolve(true)
                    }
                }).catch(async (err: string) => {
                    AiLog.error(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, `init worker ${JSON.stringify(containerId)} failed. ` +
                        `The error message is: ${JSON.stringify(err)}`);
                    resolve(false);
                })
            })
        })
    }

    private sendThreadMap(isDeleteRecord: boolean): void {
        let message: Message = {
            contentData: {},
            header: {
                // 初始化worker模块消息，不需要指定模块
                targetModuleId: '',
                sourceModuleId: MODULE_MANAGE,
                // 初始化worker模块消息名称
                messageName: MSG_REBOOT,
                askId: undefined,
                requestType: RequestType.TELL,
                tag: '',
            },
        };
        if (this.isReboot || isDeleteRecord) {
            let workerMapKeys = Array.from(this.workerMap.keys());
            for (let id of workerMapKeys) {
                let containerId = id as ContainerId;
                message.contentData = {
                    // 将moduleThreadMap发送到目标线程
                    moduleThreadMap: this.moduleThreadMap
                }
                // 发送到指定worker线程更新moduleThreadMap
                msgBusManager.postBusMsgToWorker(message, containerId);
            }
        }
    }

    private initAllModule(newlyModules: ModuleConfig[]): Promise<boolean> {
        return new Promise<boolean>(resolve => {
            let maxLevel: number = 0;
            let levelArray: Array<ModuleConfig[]> = new Array<ModuleConfig[]>();
            let size: number = newlyModules.length;
            let count: number = 0;
            // 梳理不同启动优选级的模块信息
            newlyModules.forEach(module => {
                let level: number = Number(module.initLevel);
                if (maxLevel < level) {
                    maxLevel = level
                }
                if (levelArray[level]) {
                    levelArray[level].push(module)
                } else {
                    levelArray[level] = [module]
                }
            })
            // 依次遍历所有的模块配置，发送模块初始化消息s
            let header: Header = {
                targetModuleId: '',
                sourceModuleId: MODULE_MANAGE,
                messageName: INIT_MODULE,
                requestType: RequestType.ASK
            }
            // 从0 -> 优选级最大值,依次初始化模块,间隔100ms
            for (let i = 0;i <= maxLevel; i++) {
                let moduleArray: ModuleConfig[] = levelArray[i]
                if (!moduleArray) {
                    continue
                }
                setTimeout(() => {
                    moduleArray.forEach(module => {
                        AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "init module %{public}s level %{public}s", module.moduleId, module.initLevel);
                        header.targetModuleId = module.moduleId;
                        msgBus.ask(header, null).then(message => {
                            count++;
                            AiLog.info(DOMAIN, ContainerId.THREAD_MAIN, this.TAG, "init module answer message from module: %{public}s , progress = (%{public}d/%{public}d)", message.header.sourceModuleId, count, size);
                            if(count === size) {
                                resolve(true);
                            }
                        }).catch(async (err: string) => {
                            let workerIdList: ContainerId[] = [];
                            newlyModules.forEach(module => {
                                if(!workerIdList.includes(module.containerId)) {
                                    workerIdList.push(module.containerId);
                                }
                            })
                            for(let i = 0;i < workerIdList.length; i++) {
                                await this.terminateWorker(workerIdList[i]);
                            }
                            resolve(false);
                        });
                    })
                }, i * MODULE_INIT_STEP);
            }
        });
    }
}
