/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { MessageName } from './Message';

// 初始化worker消息
export const MSG_BOOTSTRAP_WORKER: MessageName = "__base__BootstrapWorker";

// 重新启动消息
export const MSG_REBOOT: MessageName = "__base__Reboot";

// 初始化模块消息
export const INIT_MODULE: MessageName = "__base__InitModule";

// 超时配置，默认10秒钟；暂不支持自定义，如果后续确有需求，再进行优化，需要按照超时做排序
export const ASK_MSG_RESPONSE_TIMEOUT: number = 10000;

// 初始化超时，默认10s
export const INIT_ASK_MSG_RESPONSE_TIMEOUT: number = 10000;

//超时返回的错误信息
export const TIMEOUT_MSG: string = 'ask response message timeout.';

//wk获取失败的错误信息
export const GET_WK_FAILED_MSG: string = 'get wk failed';

// worker 主动退出的消息
export const WORKER_PROACTIVE_EXIT: string = 'worker proactive exit';

// worker 手动退出的消息
export const WORKER_MANUAL_EXIT: string = 'worker manual exit';
