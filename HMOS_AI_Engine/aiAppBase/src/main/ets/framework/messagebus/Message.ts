/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { ModuleId } from '../modulemanager/ModuleConfig';

/**
 * 消息定义，模块收发的消息格式。包含session以及contentData部分。
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
interface Message {
    // 定义消息的路由信息，消息名称、目标模块、源模块
    header: Header,

    // 消息体信息，发送的具体消息内容
    contentData: Object
}

// 消息请求类型
enum RequestType {
    // tell消息
    TELL = 'tell',

    // ask消息
    ASK = 'ask',

    // ask消息的响应消息
    ANSWER = 'answer'
}

// 消息名称的类型定义
type MessageName = string;

// ask消息唯一标识的类型定义
type AskId = string;

// 消息日志定位tag的类型定义
type Tag = string;

// 消息头结构体,包括消息模块,源模块,消息名称,askId,请求类型,消息定位标识tag
interface Header {
    // 目标模块id，根据模块id寻找模块，转发消息
    targetModuleId: ModuleId;

    // 源模块id
    sourceModuleId: ModuleId;

    // 消息名称
    messageName: MessageName;

    // 请求类型
    requestType: RequestType;

    // askId
    askId?: AskId;

    // ask响应超时时间
    timeout?: number,

    // 消息日志定位tag
    tag?: Tag;
}

/**
 * 模块消息管控配置
 *
 * <AUTHOR>
 * @since 2023-8-18
 */
interface MessageConfig {
    // 消息名称
    messageName: MessageName,

    // 消息发送白名单
    srcModules: ModuleId[],

    // 消息结束白名单
    dstModules: ModuleId[]
}

export { Message, Header, MessageName, AskId, RequestType, Tag, MessageConfig }