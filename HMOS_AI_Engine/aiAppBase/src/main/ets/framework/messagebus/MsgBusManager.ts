/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { ThreadWorkerGlobalScope } from '@ohos.worker';
import worker from '@ohos.worker';
import common from '@ohos.app.ability.common';
import { AskId, Message, MessageConfig, MessageName } from './Message';
import { RequestType } from './Message';
import { ModuleId } from '../modulemanager/ModuleConfig';
import { ContainerId } from '../modulemanager/ModuleConfig';
import { BaseLogicModule } from '../modulemanager/BaseLogicModule';
import { ASK_MSG_RESPONSE_TIMEOUT, INIT_ASK_MSG_RESPONSE_TIMEOUT, TIMEOUT_MSG, GET_WK_FAILED_MSG } from './MsgBusConfig';
import { AiLog } from '../../../../../Index';

let DOMAIN: number;

/**
 * 消息总线管理
 * 提供消息总线管理能力，各模块通过消息总线收发消息，不需要关心各模块执行所在线程
 * 支持范围：主线程、worker线程，不支持TaskPool线程
 * 消息总线能在主线程、worker线程中被调用
 * 各模块执行线程在初始化时确定，通过注册接口传递进来，运行期间不支持修改
 * 仅支持消息异步发送，不支持结果返回或回调
 * 通信原理：主线程和worker线程通过消息方式进行通信，如果两个worker线程通信，则考虑使用emitter通信
 * 通信规格：
 *    worker线程间通信时传递的数据量最大限制为16M，大数据量使用transfer
 *    emitter发送事件时传递的数据，数据类型支持字符串、整型和布尔型。 其中字符串长度最大为10240字节。
 */

/**
 * Ask消息返回的Promise信息，当接收到回复信息时，触发resolve完成承诺，否则reject拒绝承诺
 */
interface AskPromiseInfo {
    // 消息名称
    msgName: MessageName;

    // ask消息的id
    askId: AskId;

    // 消息发送的时间戳，用于超时判断
    msgTime: number;

    // Ask消息返回Promise的resolve
    resolve: (value: Message | PromiseLike<Message>) => void;

    // Ask消息返回Promise的reject，当前为string，后续增加异常类
    reject: (reason?: string) => void;
    // 自定义超时未来时间点
    futureTime?: number;
}

enum MessageStatus {
    SEND = 'Send',
    SEND_FAILED = 'SendFailed',
    TRANSMIT = 'Transmit',
    TRANSMIT_FAILED = 'TransmitFailed',
    RECEIVED = 'Received',
    RECEIVE_FAILED = 'ReceiveFailed'
}

/**
 * 消息总线管理
 * 提供消息总线管理能力，各模块通过消息总线收发消息，不需要关心各模块执行所在线程
 *
 * <AUTHOR>
 * @since 2023-07-18
 */
class MsgBusManager {
    // 类的名称，用于日志打印TAG
    private readonly TAG: string = "MsgBusManager";

    // 消息打印要专门标识，方便日志过滤，专门查找消息日志
    private readonly MESSAGE_TRACE: string = 'MessageTrace';

    // 当前线程id
    private currentThreadId: ContainerId | null = null;

    // 用于子线程向主线程收发消息
    private workerPort: ThreadWorkerGlobalScope | null = null;

    // 应用上下文
    private appContext: common.Context | null = null;

    // 记录线程id与worker线程对象的映射关系。worker线程对象用于主线程向子线程收发消息
    private threadMap: Map<ContainerId, worker.ThreadWorker>;

    // 记录模块id与运行线程id的映射关系
    private moduleThreadMap: Map<ModuleId, ContainerId>;

    // 记录模块id与模块句柄的映射关系。通过模块句柄进行消息发送
    private moduleHandlerMap: Map<ModuleId, BaseLogicModule>;

    // 记录消息名称与消息管控配置的关系
    private messageConfigMap: Map<MessageName, MessageConfig> = new Map();

    // 记录askId与Promise异步函数resolve的映射关系
    private askIdResolveMap: Map<AskId, AskPromiseInfo>;

    // 记录初始化askId与Promise异步函数resolve的映射关系
    private initAskIdResolveMap: Map<AskId, AskPromiseInfo>;

    // 保存未来超时时间点的集合. 按从小到大排序
    private customFutureTimeArray: Array<number>;

    // 记录自定义超时时间的ask消息未来超时时间点与askId的映射关系
    private customFutureTimeAskIdMap: Map<number, AskId[]>;

    // 记录自定义超时时间的AskId与Promise异步函数resolve的映射关系
    private customAskIdResolveMap: Map<AskId, AskPromiseInfo>;

    // ask消息响应超时器id，当该超时器触发时，说明没有得到响应，需要reject消息
    private askAnswerTimeoutId: number = -1;

    // 初始化ask消息响应超时器id，当该超时器触发时，说明没有得到响应，需要reject消息
    private initAskAnswerTimeoutId: number = -1;

    // 自定义超时时间的ask消息响应超时器id，当该超时器触发时，说明没有得到响应，需要reject消息
    private customTimeoutId: number = -1;

    /**
     * 构造消息总线，需要提供线程id
     */
    public constructor() {
        this.threadMap = new Map();
        this.moduleThreadMap = new Map();
        this.moduleHandlerMap = new Map();
        this.askIdResolveMap = new Map();
        this.initAskIdResolveMap = new Map();
        this.customFutureTimeArray = new Array<number>();
        this.customFutureTimeAskIdMap = new Map();
        this.customAskIdResolveMap = new Map();
    }

    /**
     * 设置当前线程id
     * @param currentThreadId 当前线程id
     */
    public setCurrentThread(currentThreadId: ContainerId): void {
        this.currentThreadId = currentThreadId;
    }

    /**
     * 设置应用上下文context
     * @param appContext 应用上下文
     */
    public setAppContext(appContext: common.Context): void {
        this.appContext = appContext
    }

    /*
     * 设置DOMAIN
     * */
    public setDomain(domain: number): void {
        DOMAIN = domain
    }

    /**
     * 获取应用上下文context
     * @return appContext 应用上下文
     */
    public getAppContext(): common.Context | null {
        if (this.appContext) {
            return this.appContext
        }
        return null;
    }

    /**
     * 在主线程中设置各子worker线程信息
     * @param threadMap worker线程信息
     */
    public setThreadMap(threadMap: Map<ContainerId, worker.ThreadWorker>): void {
        this.threadMap = threadMap;
    }

    /**
     * 在子线程中设置workerPort
     * @param workerPort worker端口，用于和主线程通信
     */
    public setWorkerPort(workerPort: ThreadWorkerGlobalScope): void {
        this.workerPort = workerPort;
    }

    /**
     * 记录模块名称与worker的映射关系
     * 发送消息时，根据模块名称找到目标worker
     * 转发消息时，主线程根据模块名称找到目标worker
     * @param moduleThreadMap 模块线程映射
     */
    public setModuleThreadMap(moduleThreadMap: Map<ModuleId, ContainerId>): void {
        this.moduleThreadMap = moduleThreadMap;
    }

    /**
     * 记录模块名称与模块实例的映射关系
     * 接收到消息时，根据模块名称找到模块实例
     * @param moduleHandlerMap 模块句柄映射
     */
    public setModuleHandlerMap(moduleHandlerMap: Map<ModuleId, BaseLogicModule>): void {
        this.moduleHandlerMap = moduleHandlerMap;
    }

    /**
     * 记录消息名称与消息管控配置的关系
     * 接收到消息时，判断消息是否在管控范围, 进行截断或放行
     * @param messageConfigMap 消息名称与消息管控配置映射
     * @since 2023.8.17 API10
     */
    public setMessageConfigMap(messageConfigMap: Map<MessageName, MessageConfig>): void {
        this.messageConfigMap = messageConfigMap;
    }

    /**
     * 获取当前线程id
     * @returns 当前线程id
     */
    public getCurrentThreadId(): ContainerId {
        if (this.currentThreadId) {
            return this.currentThreadId;
        }
        return ContainerId.THREAD_MAIN
    }

    /**
     * 总线根据路由信息，自动转发到对应模块
     * @param message 需要发送的消息
     * @param isSend 模块为发送消息，线程间转发为传输消息
     */
    public handleMessage(message: Message, isSend: boolean): void {
        // 发送消息时打印日志
        if (isSend) {
            this.logMessage(message, MessageStatus.SEND);
        } else {
            this.logMessage(message, MessageStatus.TRANSMIT);
        }
        // 检查消息：如果配置了转发规则，则进行规则校验
        if (!this.checkMessage(message)) {
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, "check message failed.");
            this.logMessage(message, MessageStatus.SEND_FAILED);
            return;
        }

        // 分发消息
        // 1. 目标模块在本线程，则找到对应模块，送达消息
        // 2. 目标模块不在本线程，则找到对应线程，转发消息
        //  2.1 本线程为worker线程，则先发送到主线程，由主线程处理或转发
        //  2.2 本线程为主线程，找到对应的线程，转发消息
        // 获取消息的目标模块所在线程
        let targetThreadId = this.moduleThreadMap.get(message.header.targetModuleId);
        if (targetThreadId === this.currentThreadId) {
            // 目标模块在本线程，则找到对应模块，送达消息
            this.postMsgToModule(message);
        } else if (this.currentThreadId !== ContainerId.THREAD_MAIN) {
            // 消息是其他线程的模块处理，则找到对应线程，转发消息
            // 本线程为worker线程，则先发送到主线程，由主线程处理或转发
            this.postMsgToMain(message);
        } else {
            // 本线程为主线程，找到对应的线程，转发消息
            this.postMsgToWorker(message);
        }
        // 进行打点上报
    }

    /**
     * 处理Ask消息，保存Promise的resolve以及reject，当匹配到返回消息时，resolve承诺，超时时触发reject
     * @param message 发送的消息
     * @param resolve ask消息Promise的解决回调
     * @param reject ask消息Promise的拒绝回调
     */
    public handlerAskMessage(message: Message, resolve: (value: Message | PromiseLike<Message>) => void,
                             reject: (reason?: string) => void): void {
        let currentTime = new Date().getTime();
        // 添加一条ask记录，接收到answer消息时，会查找该记录，完成承诺
        if (message.header.askId) {
            let askPromise: AskPromiseInfo = {
                msgName: message.header.messageName,
                askId: message.header.askId,
                msgTime: currentTime,
                resolve: resolve,
                reject: reject
            }
            if(message.header.timeout) {
                let futureTime = currentTime + message.header.timeout;
                AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, "futureTime: " + futureTime);
                askPromise.futureTime = futureTime;
                this.customAskIdResolveMap.set(message.header.askId, askPromise);
                if(this.customFutureTimeAskIdMap.has(futureTime)) {
                    let askIds = this.customFutureTimeAskIdMap.get(futureTime);
                    askIds?.push(message.header.askId);
                    this.customFutureTimeAskIdMap.set(futureTime, askIds);
                } else {
                    this.customFutureTimeAskIdMap.set(futureTime, [message.header.askId]);
                    // 如果有更靠前的超时时间点, 需要更新计时器
                    let isForemostFutureTime = futureTime < this.customFutureTimeArray[0];
                    this.customFutureTimeArray.push(futureTime);
                    this.customFutureTimeArray.sort((s , e)=> s - e);
                    if(this.customFutureTimeAskIdMap.size === 1) {
                        this.customTimeoutId = setTimeout(() => {
                            this.processCustomTimeout();
                        }, message.header.timeout);
                    }
                    if(this.customFutureTimeAskIdMap.size > 1 && isForemostFutureTime) {
                        clearTimeout(this.customTimeoutId);
                        this.customTimeoutId = setTimeout(() => {
                            this.processCustomTimeout();
                        }, message.header.timeout);
                    }
                }
            } else {
                this.askIdResolveMap.set(message.header.askId, askPromise);
            }
        }
        // 如果只有一条数据，添加定时器；如果有多条数据，说明之前已经添加定时器，不需要再添加
        if (this.askIdResolveMap.size === 1) {
            this.askAnswerTimeoutId = setTimeout(() => {
                this.processAskTimeout(false);
            }, ASK_MSG_RESPONSE_TIMEOUT);
        }
        // 继续发送消息
        this.handleMessage(message, true);
    }

    /**
     * 处理Ask消息，保存Promise的resolve以及reject，当匹配到返回消息时，resolve承诺，超时时触发reject
     * @param message 发送的消息
     * @param resolve ask消息Promise的解决回调
     * @param reject ask消息Promise的拒绝回调
     */
    public handlerInitAskMessage(message: Message, targetThreadId: ContainerId,
        resolve: (value: Message | PromiseLike<Message>) => void, reject: (reason?: string) => void): void {
        // 添加一条ask记录，接收到answer消息时，会查找该记录，完成承诺
        if (message.header.askId) {
            let askPromise: AskPromiseInfo = {
                msgName: message.header.messageName,
                askId: message.header.askId,
                msgTime: new Date().getTime(),
                resolve: resolve,
                reject: reject
            }
            this.initAskIdResolveMap.set(message.header.askId, askPromise);
        }
        // 如果只有一条数据，添加定时器；如果有多条数据，说明之前已经添加定时器，不需要再添加
        if (this.initAskIdResolveMap.size === 1) {
            this.initAskAnswerTimeoutId = setTimeout(() => {
                this.processAskTimeout(true);
            }, INIT_ASK_MSG_RESPONSE_TIMEOUT);
        }
        // 继续发送消息
        this.postBusMsgToWorker(message, targetThreadId);
    }

    /**
     * 发送总线消息到指定线程，用于worker线程初始化。模块业务消息不应该调用该接口
     * @param message 发送的消息
     * @param targetThreadId 目标线程
     */
    public postBusMsgToWorker(message: Message, targetThreadId: ContainerId): void {
        AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, "post msg %{public}s to thread %{public}s",
            message.header.messageName, targetThreadId);
        // 如果目标线程不存在，则发送失败
        if (!this.threadMap.has(targetThreadId)) {
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, "post msg failed. message name: %{public}s. unknown thread id: %{public}d",
                message.header.messageName, targetThreadId);
            return;
        }

        // 如果当前为主线程，则直接发送到目标线程
        try {
            if (this.currentThreadId === ContainerId.THREAD_MAIN) {
                let getTargetThreadId = this.threadMap.get(targetThreadId);
                if (getTargetThreadId) {
                    AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, `get wk success ,the wk: ${JSON.stringify(getTargetThreadId)}`);
                    getTargetThreadId.postMessage(message);
                } else {
                    if (message.header.requestType === RequestType.ASK) {
                        AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, `get wk failed ,the wk: ${JSON.stringify(getTargetThreadId)}`);
                        let askId = message.header.askId;
                        this.initAskIdResolveMap.get(askId)?.reject(GET_WK_FAILED_MSG);
                        this.initAskIdResolveMap.delete(askId)
                    }
                }
                return;
            }
        } catch(err) {
            // 消息发生异常，一般为序列化失败，异常采用private
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'post msg to worker error: %{private}s', JSON.stringify(err));
            this.logMessage(message, MessageStatus.TRANSMIT_FAILED);
        }


        // 如果是worker线程，直接发送到主线程。 理论上不同worker线程不应该指定线程发送消息
        if (targetThreadId !== ContainerId.THREAD_MAIN) {
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, "post msg failed.  message name: %{public}s. worker can only send to main." +
                " target thread id: %{public}d", message.header.messageName, targetThreadId);
            return;
        }
        if (this.workerPort) {
            this.workerPort.postMessage(message);
        }
    }

    private postMsgToWorker(message: Message): void {
        try {
            let targetThreadId = this.moduleThreadMap.get(message.header.targetModuleId);
            if (targetThreadId) {
                let getTargetThreadId = this.threadMap.get(targetThreadId);
                if (getTargetThreadId) {
                    getTargetThreadId.postMessage(message);
                }
            }
        } catch (err) {
            // 消息发生异常，一般为序列化失败，异常采用private
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'post msg to worker error: %{private}s', JSON.stringify(err));
            this.logMessage(message, MessageStatus.TRANSMIT_FAILED);
        }
    }

    private postMsgToMain(message: Message): void {
        try {
            if (this.workerPort) {
                this.workerPort.postMessage(message);
            }
        } catch (err) {
            // 消息发生异常，一般为序列化失败，异常采用private
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'post msg to main error: %{private}s', JSON.stringify(err));
            this.logMessage(message, MessageStatus.TRANSMIT_FAILED);
        }
    }

    /**
     * 发送消息到目标
     * @param message 发送的消息
     */
    private postMsgToModule(message: Message): void {
        // 如果是ask的响应消息，则找到对应的resolve，完成Promise
        let askId = message.header.askId;
        if (message.header.requestType === RequestType.ANSWER) {
            if (askId && this.askIdResolveMap.has(askId)) {
                let promiseInfo = this.askIdResolveMap.get(askId);
                // 当前ask响应是否是第一个，用于定时器更新
                let isFirstRecord = this.askIdResolveMap.keys().next().value === askId;
                // 删除askIdResolveMap中的记录
                this.askIdResolveMap.delete(askId);
                // 异步返回message，完成承诺
                if (promiseInfo) {
                    promiseInfo.resolve(message);
                }
                // ASK响应消息送达日志
                this.logMessage(message, MessageStatus.RECEIVED);

                // 如果该ask响应是第一个，则更新计时器；如果不是第一个，说明已有的定时器还没有触发
                if (isFirstRecord) {
                    clearTimeout(this.askAnswerTimeoutId);
                    AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'ask answer time out');
                    this.processAskTimeout(false);
                }
            } else if (askId && this.initAskIdResolveMap.has(askId)) {
                let promiseInfo = this.initAskIdResolveMap.get(askId);
                let isFirstRecord = this.initAskIdResolveMap.keys().next().value === askId;
                this.initAskIdResolveMap.delete(askId);
                if (promiseInfo) {
                    promiseInfo.resolve(message);
                }
                this.logMessage(message, MessageStatus.RECEIVED);
                // 如果该ask响应是第一个，则更新计时器；如果不是第一个，说明已有的定时器还没有触发
                if (isFirstRecord) {
                    clearTimeout(this.initAskAnswerTimeoutId);
                    AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'init ask answer time out');
                    this.processAskTimeout(true);
                }
            } else if(askId && this.customAskIdResolveMap.has(askId)) { // 自定义超时时间的消息resolve处理逻辑
                let promiseInfo = this.customAskIdResolveMap.get(askId);
                let futureTime = promiseInfo?.futureTime;
                // 当前ask响应对应的futureTime是否是第一个
                let index = this.customFutureTimeArray.indexOf(futureTime);
                let isFirstRecord = index === 0;
                // 当前futureTime(当前ask消息对应的超时响应时间点的时间戳)已经失效, 没有需要超时响应的ask消息, 用于定时器更新
                let isFutureTimeInvalided = false;
                // 删除askIdResolveMap中的记录
                this.customAskIdResolveMap.delete(askId);
                // 删除customFutureTimeAskIdMap中的记录
                if(futureTime) {
                    let customAskIds = this.customFutureTimeAskIdMap.get(futureTime);
                    if(customAskIds?.length <= 1) {
                        this.customFutureTimeAskIdMap.delete(futureTime);
                        this.customFutureTimeArray.splice(index,1);
                        isFutureTimeInvalided = true;
                    } else {
                        let tempAskIds: AskId[] = new Array<AskId>();
                        customAskIds?.forEach(id => {
                            if(id !== askId) {
                                tempAskIds.push(id);
                            }
                        })
                        this.customFutureTimeAskIdMap.set(futureTime, tempAskIds);
                    }
                }
                // 异步返回message，完成承诺
                if (promiseInfo) {
                    promiseInfo.resolve(message);
                }
                // ASK响应消息送达日志
                this.logMessage(message, MessageStatus.RECEIVED);

                // 如果该ask响应对应的futureTime是第一个 并且 该ask消息对应的的未来响应时间点的时间戳没有其他ask消息的超时响应，则更新计时器；如果不是，说明已有的定时器还没有触发
                if (isFirstRecord && isFutureTimeInvalided) {
                    clearTimeout(this.customTimeoutId);
                    this.customTimeoutId = setTimeout(() => {
                        this.processCustomTimeout();
                    }, this.customFutureTimeArray[0] - new Date().getTime());
                }
            } else {
                AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'receive ask response but not found ask send record. askId: %{public}s', askId);
                this.logMessage(message, MessageStatus.RECEIVE_FAILED);
            }
            return;
        }
        // tell、ask的发送消息送达模块
        try {
            if (this.moduleHandlerMap.has(message.header.targetModuleId)) {
                this.logMessage(message, MessageStatus.RECEIVED);
                // 送达消息，接收成功
                this.moduleHandlerMap.get(message.header.targetModuleId)?.handleMsg(message);
            } else {
                // 目标模块没有创建
                AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'receive message but not found target module instance. targetModule: %{public}s',
                    message.header.targetModuleId);
                this.logMessage(message, MessageStatus.RECEIVE_FAILED);
            }
        } catch (err) {
            // 处理消息发生异常，比如序列化失败，处理函数内部异常等，异常采用private
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'module handle message error. error: %{private}s', JSON.stringify(err));
            this.logMessage(message, MessageStatus.RECEIVE_FAILED);
        }
    }

    /**
     * 处理ask返回消息超时
     */
    private processAskTimeout(isInit: boolean): void {
        // 遍历ask记录，取出已经超时的ask消息记录，不需要继续等待，触发reject
        // 注：由于map是按照插入进行排序的，前面处理的时间戳肯定更靠前
        AiLog.info(DOMAIN, `this.currentThreadId === ${this.currentThreadId ?? ''}`, this.TAG, 'start process ask time out');
        let askIdResolveMapKeys: string[];
        let askIdResolveMapValues: AskPromiseInfo[];
        try {
            if (isInit) {
                AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'array from, isInit is true.');
                askIdResolveMapKeys = Array.from(this.initAskIdResolveMap.keys());
                askIdResolveMapValues = Array.from(this.initAskIdResolveMap.values());
            } else {
                AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'array from, isInit is false.');
                askIdResolveMapKeys = Array.from(this.askIdResolveMap.keys());
                askIdResolveMapValues = Array.from(this.askIdResolveMap.values());
            }
        } catch (e) {
            AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'array from catch error is: ' + JSON.stringify(e));
        }

        let timeout = isInit ? INIT_ASK_MSG_RESPONSE_TIMEOUT : ASK_MSG_RESPONSE_TIMEOUT;
        for (let i = 0; i < askIdResolveMapKeys.length; i++) {
            // 截止到当前耗时
            let elapsedTime = new Date().valueOf() - askIdResolveMapValues[i].msgTime;
            // Ask响应消息超时，触发reject，打印日志
            if (elapsedTime > timeout) {
                askIdResolveMapValues[i].reject(TIMEOUT_MSG);
                try {
                    if (isInit) {
                        AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'map delete, isInit is true.');
                        this.initAskIdResolveMap.delete(askIdResolveMapKeys[i])
                    } else {
                        AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'map delete, isInit is false.');
                        this.askIdResolveMap.delete(askIdResolveMapKeys[i]);
                    }
                } catch (e) {
                    AiLog.error(DOMAIN, this.currentThreadId ?? '', this.TAG, 'map delete catch error is: ' + JSON.stringify(e));
                }
                AiLog.error(DOMAIN, `this.currentThreadId === ${this.currentThreadId ?? ''}`, this.TAG, 'ask response message timeout. askId: %{public}s', askIdResolveMapKeys[i]);
                this.loggerAskTimeout(askIdResolveMapValues[i]);
            } else {
                // 按照下一个ask响应消息剩余的时间创建新的定时器
                if(isInit) {
                    AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'init ask answer set time out, isInit is true.');
                    this.initAskAnswerTimeoutId = setTimeout(() => {
                        this.processAskTimeout(isInit);
                    }, timeout - elapsedTime);
                } else {
                    AiLog.info(DOMAIN, this.currentThreadId ?? '', this.TAG, 'ask answer set time out, isInit is true.');
                    this.askAnswerTimeoutId = setTimeout(() => {
                        this.processAskTimeout(isInit);
                    }, timeout - elapsedTime);
                }
                break;
            }
        }
    }

    /*
     * 处理自定义超时时间ask返回消息超时
     * */
    private processCustomTimeout():void {
        // 遍历ask记录，取出已经超时的ask消息记录，不需要继续等待，触发reject
        // 注：由于customFutureTimeArray对未来时间点进行了从小到大的排序，前面处理的未来时间点的时间戳肯定更靠前
        let currentTime: number = 0;
        let futureTime: number = 0;
        let index: number = -1;
        for(let i = 0; i < this.customFutureTimeArray.length; i++) {
            currentTime = new Date().valueOf();
            futureTime = this.customFutureTimeArray[i];
            index = i;
            if(currentTime > futureTime) {
                let customAskIds = this.customFutureTimeAskIdMap.get(futureTime);
                customAskIds?.forEach(id => {
                    if(this.customAskIdResolveMap.has(id)) {
                        this.rejectCustomTimeout(id);
                    }
                })
                this.customFutureTimeAskIdMap.delete(futureTime);
            } else {
                break;
            }
        }
        if(index !== -1) {
            this.customFutureTimeArray = this.customFutureTimeArray.slice(index);
            // 按照下一个ask响应消息剩余的时间创建新的定时器
            this.customTimeoutId = setTimeout(() => {
                this.processCustomTimeout();
            }, futureTime - currentTime);
        }
    }

    // 超时执行reject
    private rejectCustomTimeout(askId: AskId): void {
        let promiseInfo = this.customAskIdResolveMap.get(askId);
        promiseInfo?.reject(TIMEOUT_MSG);
        this.customAskIdResolveMap.delete(askId);
        AiLog.error(DOMAIN, `this.currentThreadId === ${this.currentThreadId ?? ''}`, this.TAG, 'custom ask response message timeout. askId: %{public}s', askId);
        this.loggerAskTimeout(promiseInfo);
    }

    private loggerAskTimeout(promiseInfo: AskPromiseInfo): void {
        let fakeAnswerMsg: Message = {
            header: {
                targetModuleId: '',
                sourceModuleId: '',
                messageName: promiseInfo.msgName,
                askId: promiseInfo.askId,
                requestType: RequestType.ANSWER,
                tag: '',
            },
            contentData: {}
        }
        this.logMessage(fakeAnswerMsg, MessageStatus.RECEIVE_FAILED);
    }

    private checkMessage(message: Message): boolean {
        let messageName = message.header.messageName;
        let sourceModuleId = message.header.sourceModuleId
        let targetModuleId = message.header.targetModuleId;
        let requestType = message.header.requestType;

        // 消息转发规则校验
        if (this.messageConfigMap.has(messageName) && (requestType === RequestType.TELL || requestType === RequestType.ASK)) {
            let messageConfig: MessageConfig | undefined = this.messageConfigMap.get(messageName)
            let srcModules: string[] = [];
            let dstModules: string[] = [];
            if (messageConfig) {
                srcModules = messageConfig.srcModules;
                dstModules = messageConfig.dstModules;
            }
            if (srcModules.length > 0 && !srcModules.includes(sourceModuleId)) {
                AiLog.debug(DOMAIN, this.currentThreadId ?? '', this.TAG, "messageConfig not allowed sourceModuleId: " + sourceModuleId);
                return false;
            }
            if (dstModules.length > 0 && !dstModules.includes(targetModuleId)) {
                AiLog.debug(DOMAIN, this.currentThreadId ?? '', this.TAG, "messageConfig not allowed targetModuleId: " + targetModuleId);
                return false;
            }
        }
        return true;
    }

    /**
     * 进行消息日志记录。
     * 1. 日志格式：DOMAIN（0xFFEE）,TAG(固定为MessageTrace，可以过滤该关键字查找所有消息日志)，
     *    消息名称(消息类型, tag: 消息标签, askId: Ask消息id) 消息状态, 源模块->目标模块, thread: 当前线程, data(消息内容，private)
     * 2. debug日志全部进行打印，如果专门配置消息打印，则使用info级别进行打印；失败统一打印error日志
     * 3. 消息体属于private，要查看需要手动打开设备hilog开关
     *
     * @param message 转发的消息
     * @param status 消息状态
     */
    private logMessage(message: Message, status: MessageStatus): void {
        // 失败统一打印error日志，如果专门配置消息打印，则使用info级别进行打印，否则打印debug日志；
        let header = message.header;
        let logFunc = AiLog.debug;
        if (status === MessageStatus.SEND_FAILED || status === MessageStatus.RECEIVE_FAILED) {
            logFunc = AiLog.error;
        } else if (this.messageConfigMap.has(header.messageName)) {
            logFunc = AiLog.info;
        }

        logFunc(DOMAIN, this.currentThreadId ?? '', this.MESSAGE_TRACE,
            "%{public}s(%{public}s) %{public}s, %{public}s->%{public}s, tag: %{public}s, " +
                "askId: %{public}s, thread: %{public}s",
            header.messageName, header.requestType, status.valueOf(), header.sourceModuleId, header.targetModuleId,
            header.tag ?? '', header.askId ?? '', this.currentThreadId);
    }
}

const msgBusManager = new MsgBusManager();

// 导出消息总线
export { msgBusManager }