/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { AskId, Header, Message, MessageName } from './Message';
import { RequestType } from './Message';
import { msgBusManager } from './MsgBusManager';
import { ModuleId } from '../modulemanager/ModuleConfig';
import { ContainerId } from '../modulemanager/ModuleConfig';

/**
 * 消息总线，各模块调用消息总线进行消息发送
 *
 * <AUTHOR>
 * @since 2023-7-18 API10
 */
class MsgBus {
    // 消息编号，用于生成AskId，避免AskId重复；每个线程都有自己的实例，从1开始计数，最大1.79E+308，不会出现溢出
    private msgNumber: number = 0;

    /**
     * 发送消息到指定模块，消息总线根据路由信息找到目标模块，将消息发送过去。
     *
     * @param header 请求头
     * @param messageData 消息内容
     */
    public tell(header: Header, messageData: Object): void {
        // 构造消息体
        header.requestType = RequestType.TELL;
        let message: Message = {
            header: header,
            contentData: messageData
        }
        // 消息总线处理消息
        msgBusManager.handleMessage(message, true);
    }

    /**
     * 发送消息到指定模块，同时异步返回响应消息
     *
     * @param header 请求头
     * @param messageData 消息内容
     * @returns 目标模块响应消息异步结果
     */
    public ask(header: Header, messageData: Object): Promise<Message> {
        return new Promise<Message>((resolve, reject) => {
            // 生成askId
            let askId = this.generateAskId(header.targetModuleId, header.sourceModuleId, header.messageName);
            // 构造消息体
            header.requestType = RequestType.ASK;
            header.askId = askId;
            let message: Message = {
                header: header,
                contentData: messageData
            }
            msgBusManager.handlerAskMessage(message, resolve, reject);
        });
    }

    /**
     * 发送初始化消息到指定模块，同时异步返回响应消息
     *
     * @param header 请求头
     * @param messageData 消息内容
     * @returns 目标模块响应消息异步结果
     */
    public initAsk(message: Message, targetThreadId: ContainerId): Promise<Message> {
        return new Promise<Message>((resolve, reject) => {
            // 生成askId
            let header = message.header;
            let askId = this.generateAskId(header.targetModuleId, header.sourceModuleId, header.messageName);
            // 构造消息体
            message.header.askId = askId
            msgBusManager.handlerInitAskMessage(message, targetThreadId, resolve, reject);
        });
    }

    /**
     * 返回ask的响应消息
     *
     * @param header 请求头
     * @param messageData 消息内容
     */
    public answer(header: Header, messageData: Object): void {
        // 构造消息体
        header.requestType = RequestType.ANSWER;
        let message: Message = {
            header: header,
            contentData: messageData
        }
        // 消息总线处理消息
        msgBusManager.handleMessage(message, true);
    }

    /**
     * 生成AskId
     * 形如：消息名称-目标模块-源模块-自增变量
     * @param targetModule 目标模块
     * @param sourceModule 源模块
     * @param messageName 消息名称
     * @returns ask id
     */
    private generateAskId(targetModule: ModuleId, sourceModule: ModuleId, messageName: MessageName): AskId {
        this.msgNumber++;
        return messageName + '-' + targetModule + '-' + sourceModule + '-' + this.msgNumber;
    }
}

const msgBus = new MsgBus();

// 导出消息总线msgBus
export { msgBus }