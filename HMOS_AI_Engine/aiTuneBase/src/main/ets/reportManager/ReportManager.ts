/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import buffer from '@ohos.buffer';
import { BusinessError } from '@ohos.base';
import { ReportConfig, ReportContent, DEFAULT_DURATION, MAX_DURATION, MIN_DURATION, ReportDeviceInfo, Content,
  MAX_COUNT, MAX_SIZE, NUM_ZERO, MAX_LENGTH, EMPTY_STRING, ContentInfo, ReportUrlType } from './utils/Constants';
import { Utils } from './utils/Utils';
import StoreManager from './StoreManager';
import NetworkManager from './NetworkManager';
import Log from './utils/Log';
import { ErrMaker } from './utils/errorMaker';
import { Context } from '@kit.AbilityKit';


/**
 * TuneBase ReportManager.
 * Provide methods to cache the application's data and submit to HuaShan datahub.
 *
 * <AUTHOR>
 * @since 2024-5-28
 */
export class ReportManager {
  // TAG used to print the log.
  private TAG: string;
  // context used to create database.
  private context: Context;
  // the singleton instance of TuneBaseReport.
  private static instance: ReportManager;
  // the config which would be set while init provides default property if the data's property is missing.
  private config: ReportConfig | undefined = undefined;
  // the deviceInfo which would be submit along with data.
  private deviceInfo: ReportDeviceInfo | undefined = undefined;
  // the singleton instance of StoreManager.
  private database: StoreManager | undefined = undefined;
  // the report url
  private url: string = "";
  // the max size of a single piece of content.
  private static readonly MAX_SIZE: number = 10240;
  // the retry interval, 30s.
  private static readonly INTERVAL: number = 30000;


  private constructor(context: Context) {
    this.TAG = "aiTuneBase_report";
    this.context = context;
  }

  /**
   * get TuneBaseReport singleton instance.
   *
   * @params { Context } context - the context of application.
   * @return { TuneBaseReport } the singleton instance of TuneBaseReport.
   */
  public static getInstance(context: Context): ReportManager {
    ReportManager.instance = ReportManager.instance ?? new ReportManager(context);
    return ReportManager.instance;
  }

  /**
   * initialize the reportManger, save the config and create a database if not exist to cache the data.
   *
   * @params { ReportConfig } config - the report config.
   */
  public async init(config: ReportConfig): Promise<void> {
    if (!this.checkConfig(config)) {
      return Promise.reject(new ErrMaker(ErrMaker.INPUT_CODE, ErrMaker.INPUT_MSG));
    }
    if (!config.dataDuration) {
      config.dataDuration = DEFAULT_DURATION;
    } else if (config.dataDuration > MAX_DURATION || config.dataDuration < MIN_DURATION) {
      Log.warn(this.TAG, `dataDuration of config is invalid, set ${DEFAULT_DURATION} days instead.`);
      config.dataDuration = DEFAULT_DURATION;
    }
    // set the public properties and create a database if not exists.
    this.config = config;
    this.deviceInfo = Utils.getDeviceInfo();
    this.database = StoreManager.getInstance(this.context, config.encrypt);

    try {
      this.url = await this.getReportUrl(config.urlType);
      if (this.url === EMPTY_STRING) {
        return Promise.reject(new ErrMaker(ErrMaker.INPUT_CODE, ErrMaker.INPUT_MSG));
      }
    } catch (e) {
      return Promise.reject(new ErrMaker(ErrMaker.INNER_CODE, ErrMaker.INNER_MSG));
    }

    if (this.database && await this.database.createDataBase()) {
      Log.info(this.TAG, `create database success, init success.`);
      return Promise.resolve();
    } else {
      this.database = undefined;
      Log.error(this.TAG, `create database failed, init failed.`);
      return Promise.reject(new ErrMaker(ErrMaker.INNER_CODE, ErrMaker.INNER_MSG));
    }
  }

  /**
   * cache the data content to the database and report if condition is satisfied.
   *
   * @params { ReportContent } content - the report content.
   */
  public async onCache(content: ReportContent): Promise<void> {
    // check whether the service has initialized before.
    if (!this.database || !this.config) {
      Log.error(this.TAG, `can not find database or config, please init first.`);
      return Promise.reject(new ErrMaker(ErrMaker.CONFIG_CODE, ErrMaker.CONFIG_MSG));
    }
    // check whether the input content is larger than limit.
    let dataSize = buffer.byteLength(JSON.stringify(content));
    if (!content || !content.content || dataSize > ReportManager.MAX_SIZE ) {
      Log.error(this.TAG, `the content size is ${dataSize}, larger than limit: 10 KB`);
      return Promise.reject(new ErrMaker(ErrMaker.INPUT_CODE, ErrMaker.INPUT_MSG));
    }

    try {
      let isNeedReport = await this.database.addData(content, this.config, dataSize);
      Log.info(this.TAG, `cache data success, instanceTag: ${content.instanceTag ?? this.config.instanceTag}.`);
      // check the network and decide to report immediately or later if condition is satisfied.
      if (isNeedReport && NetworkManager.checkNetwork()) {
        Log.info(this.TAG, `report condition is satisfied, begin to report.`);
        return this.startReport();
      } else if (isNeedReport && !NetworkManager.checkNetwork()) {
        Log.warn(this.TAG, `report condition is satisfied, but network is unavailable.`);
        return Promise.resolve();
      }
      return Promise.resolve();
    } catch (e) {
      if (e === ErrMaker.CACHE_CODE) {
        return Promise.reject(new ErrMaker(ErrMaker.CACHE_CODE, ErrMaker.CACHE_MSG));
      } else {
        return Promise.reject(new ErrMaker(ErrMaker.REPORT_CODE, ErrMaker.REPORT_MSG));
      }
    }
  }

  /**
   * report the data that cached in database instantly.
   */
  public async onReport(): Promise<void> {
    // check whether the service has initialized before.
    if (!this.database || !this.config) {
      Log.error(this.TAG, `can not find database or config, please init first.`);
      return Promise.reject(new ErrMaker(ErrMaker.CONFIG_CODE, ErrMaker.CONFIG_MSG));
    }
    if (!NetworkManager.checkNetwork()) {
      Log.error(this.TAG, `the network is unavailable. report failed.`)
      return Promise.reject(new ErrMaker(ErrMaker.REPORT_CODE, ErrMaker.REPORT_MSG));
    }

    try {
      Log.info(this.TAG, `begin to report.`)
      await this.startReport();
      Log.info(this.TAG, `onReport success.`);
      return Promise.resolve();
    } catch (e) {
      Log.error(this.TAG, `onReport failed.`);
      return Promise.reject(new ErrMaker(ErrMaker.REPORT_CODE, ErrMaker.REPORT_MSG));
    }
  }

  /**
   * report the data and do not cache in database unless report is failed.
   *
   * @params { ReportContent[] } contentList - the report content list.
   * @returns { boolean } whether report success or not. if report is failed, the data will be cached in database,
   * otherwise the promise is rejected.
   */
  public async onReportRealTime(contentList: ReportContent[]): Promise<boolean> {
    // check whether the service has initialized before.
    if (!this.database || !this.config) {
      Log.error(this.TAG, `can not find database or config, please init first.`);
      return Promise.reject(new ErrMaker(ErrMaker.CONFIG_CODE, ErrMaker.CONFIG_MSG))
    }
    let contentInfo: ContentInfo = this.checkContent(contentList);
    let list: Content[] = contentInfo.contentList;
    let sizeList: number[] = contentInfo.sizeList
    if (!list.length) {
      return Promise.reject(new ErrMaker(ErrMaker.INPUT_CODE, ErrMaker.INPUT_MSG))
    }

    let result: boolean | undefined = undefined;
    try {
      Log.info(this.TAG, `all data checked. begin to report.`)
      await this.report(list);
      Log.info(this.TAG, `onReportRealTime success.`);
      result = true;
    } catch (e) {
      Log.error(this.TAG, `onReportRealTime failed. cache the data in database.`);
      await this.database.addDataBatch(contentList, this.config, sizeList).then(()=>{
        Log.info(this.TAG, `cache data success. ready to report next time.`);
        result = false;
      }).catch((error: number) => {
        Log.error(this.TAG, `cacha data failed.`)
      })
    }
    if (result !== undefined) {
      return result;
    } else {
      return Promise.reject(new ErrMaker(ErrMaker.CACHE_CODE, ErrMaker.CACHE_MSG));
    }
  }

  /**
   * start report. if the remaining data still satisfy submit condition, continue to report after 30s.
   */
  private async startReport(): Promise<void> {
    let flag = true;
    let reportCount = await (this.database as StoreManager).getReportTimes();
    for (let i = 0; i < reportCount; i++) {
      try {
        Log.info(this.TAG, `${i} of ${reportCount} to be reported.`)
        flag = await this.report()
      } catch (e) {
        flag = false;
        return Promise.reject(ErrMaker.REPORT_CODE)
      }
      if (flag) {
        Log.warn(this.TAG, `continue to report after 30 s.`);
        await Utils.sleep(ReportManager.INTERVAL);
      } else {
        break;
      }
    }
    return Promise.resolve();
  }

  /**
   * begin to report.
   *
   * @returns { boolean } whether the remaining data satisfy the report condition.
   */
  private async report(data?: Content[]): Promise<boolean> {
    if (!NetworkManager.checkNetwork()) {
      Log.error(this.TAG, `the network is unavailable. report failed.`)
      return Promise.reject(ErrMaker.REPORT_CODE)
    }
    try {
      let content = data ?? await (this.database as StoreManager).getContent(this.deviceInfo as ReportDeviceInfo);
      if (content.length === NUM_ZERO) {
        Log.warn(this.TAG, `no data in cache, no need to report.`);
        return false;
      }
      let header = NetworkManager.buildHeader(this.config as ReportConfig, this.deviceInfo as ReportDeviceInfo);
      let reportResult: boolean = await NetworkManager.sendHttpRequest(this.url, header, content);
      let result = false;
      if (reportResult) {
        //delete the report data in the database if report is success.
        result = await (this.database as StoreManager).deleteReportData();
        Log.info(this.TAG, `report success. the requestId: ${header.requestId}. remaining data: ${result}.`);
      } else {
        Log.warn(this.TAG, `report failed. the requestId: ${header.requestId}.`)
        return Promise.reject(ErrMaker.REPORT_CODE)
      }
      return result;
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `report fialed, errMsg: ${err.message}, errCode: ${err.code}.`);
      return Promise.reject(ErrMaker.INNER_CODE);
    }
  }

  /**
   * check whether the config properties are valid or not.
   *
   * @params { ReportConfig } config - the config to be checked.
   * @return { boolean } whether the input config is valid or not.
   */
  private checkConfig(config: ReportConfig): boolean {
    if (!config.deviceId || config.deviceId.length > MAX_LENGTH) {
      Log.error(this.TAG, "the deviceId of config is invalid.");
      return false;
    }
    if (!config.appName || config.appName.length > MAX_LENGTH) {
      Log.error(this.TAG, "the appName of config is invalid.");
      return false;
    }
    if (!config.appVersion || config.appVersion.length > MAX_LENGTH) {
      Log.error(this.TAG, "the appVersion of config is invalid.");
      return false;
    }
    if (!config.trigger || config.trigger.length > MAX_LENGTH) {
      Log.error(this.TAG, "the trigger of config is invalid.");
      return false;
    }
    if (!new RegExp('^[a-zA-Z_]{1,32}$').test(config.dataType)) {
      Log.error(this.TAG, "the dataType of config is invalid.");
      return false;
    }
    if (!config.instanceTag || config.instanceTag.length > MAX_LENGTH) {
      Log.error(this.TAG, "the instanceTag of config is invalid.");
      return false;
    }
    return true;
  }

  /**
   * check the input list is invalid.
   *
   * @params { ReportContent[] } contentList - the contentList
   * @return { Content[] } the content list
   */
  private checkContent(contentList: ReportContent[]): ContentInfo {
    let size: number = 0;
    let sizeList: number[] = [];
    let list: Content[] = []
    if (contentList.length > MAX_COUNT || !contentList.length) {
      Log.error(this.TAG, `the content list length is larger than limit: 30 or the content list is null.`);
      let contentInfo: ContentInfo = {
        contentList: list,
        sizeList: sizeList
      };
      return contentInfo;
    }
    for (let i = 0; i < contentList.length; i++) {
      if (!contentList[i] || !contentList[i].content) {
        Log.error(this.TAG, `the ${i} content is null, please check input.`);
        list.length = NUM_ZERO;
        break;
      }
      // check whether the input content is larger than limit.
      let dataSize = buffer.byteLength(JSON.stringify(contentList[i]));
      if (dataSize > ReportManager.MAX_SIZE) {
        Log.error(this.TAG, `the content size is ${dataSize}, larger than limit: 10 KB.`);
        list.length = NUM_ZERO;
        break;
      }
      size += dataSize;
      if (size > MAX_SIZE) {
        Log.error(this.TAG, `the content total size is ${size}, larger than limit: 50 KB.`);
        list.length = NUM_ZERO;
        break
      }
      sizeList.push(dataSize);
      let content = this.getContentFromInput(contentList[i], this.config, this.deviceInfo as ReportDeviceInfo);
      list.push(content);
    }
    let contentInfo: ContentInfo = {
      contentList: list,
      sizeList: sizeList
    };
    return contentInfo;
  }


  /**
   * get report content from the onReportRealTime input.
   *
   * @params { ReportContent } data - the reportContent.
   * @params { ReportConfig } config - the report config.
   * @params { ReportDeviceInfo } deviceInfo -the deviceInfo
   * @return { Content } the report content.
   */
  private getContentFromInput(data: ReportContent, config: ReportConfig, deviceInfo: ReportDeviceInfo): Content {
    let content: Content = {
      appName: data.appName ?? config.appName,
      trigger: data.trigger ?? config.trigger,
      dataType: data.dataType ?? config.dataType,
      clientTime: new Date().getTime(),
      content: data.content,
      appVersion: data.appVersion ?? config.appVersion,
      sn: data.sn ?? config.sn ?? EMPTY_STRING,
      udid: data.odid ?? config.odid ?? EMPTY_STRING,
      uid: data.uid ?? config.uid ?? EMPTY_STRING,
      uuid: data.uuid ?? config.uuid ?? EMPTY_STRING,
      deviceType: deviceInfo.deviceCategory,
      model: deviceInfo.model,
      osVersion: deviceInfo.osVersion,
      brand: deviceInfo.brand,
      manufacturer: deviceInfo.manufacturer
    };
    return content;
  }

  /**
   * get report url from profile.
   *
   * @return { string } the report url.
   */
  private async getReportUrl(urlType: ReportUrlType): Promise<string> {
    let myResourceManager = this.context.resourceManager;
    try {
      let url: string;
      let urlBuffer = await myResourceManager.getRawFileContent("aiTuneBase_reportUrl.json");
      if (urlType === ReportUrlType.LIVE) {
        url = JSON.parse(buffer.from(urlBuffer.buffer).toString()).live_url;
      } else {
        return EMPTY_STRING;
      }
      Log.info(this.TAG, `get url success.`)
      return url;
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `get url from profile failed, errMsg: ${err.message}, errCode: ${err.code}.`);
      return Promise.reject(err);
    }
  }
}