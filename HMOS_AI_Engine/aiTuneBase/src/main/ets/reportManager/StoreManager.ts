/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import relationalStore from '@ohos.data.relationalStore';
import distributedKVStore from '@ohos.data.distributedKVStore';
import { ValuesBucket } from '@ohos.data.ValuesBucket';
import { BusinessError } from "@ohos.base";
import Log from './utils/Log';
import { Field, ReportContent, ReportConfig, COUNT_REQUEST, SIZE_REQUEST, ReportDeviceInfo,
  Content, NUM_ZERO, NUM_ONE, MAX_COUNT, MAX_SIZE, EMPTY_STRING }  from './utils/Constants';
import { Utils } from './utils/Utils';
import { ErrMaker } from './utils/errorMaker'
import { Context } from '@kit.AbilityKit';


/**
 * Record and store the report data contents.
 *
 * <AUTHOR>
 * @since 2024-4-15
 */
export default class StoreManager {
  // TAG used to print the log.
  private TAG: string;
  // context used to create the database.
  private context: Context;
  // the store config used to create database.
  private storeConfig: relationalStore.StoreConfig;
  // the database.
  private store: relationalStore.RdbStore | undefined = undefined;
  // the KVStore.
  private kvStore:  distributedKVStore.SingleKVStore | undefined = undefined;
  // the reportStorage singleton instance.
  private static instance: StoreManager;
  // the multiplier that convert day to millisecond.
  private static readonly MULTI: number = 1000*60*60*24;
  // the max data pieces that database could contain.
  private static readonly MAX_DATA: number = 10000;
  // the idList of content
  private idList: number[] = [];
  // the size list of content
  private sizeList: number[] = [];
  // the key of time
  private readonly TIME: string = "time";
  // the key of count
  private readonly COUNT: string = "count";
  // the key of size
  private readonly SIZE: string = "size";


  private constructor(context: Context, needEncrypt: boolean) {
    this.context = context;
    this.TAG = "aiTuneBase_store";
    this.storeConfig = {
      name: "aiTuneBaseStore.db",
      securityLevel: relationalStore.SecurityLevel.S2,
      customDir: "aiTuneBase",
      encrypt: needEncrypt
    }
  }

  /**
   * get reportStorage singleton instance.
   *
   * @params { Context } context - the context of application.
   * @return { ReportStorage } the singleton instance of reportStorage.
   */
  public static getInstance(context: Context, encrypt: boolean): StoreManager {
    StoreManager.instance = StoreManager.instance ?? new StoreManager(context, encrypt);
    return StoreManager.instance;
  }

  /**
   * create a database and establish the table.
   *
   * @return { Promise<boolean> } whether the database is created successfully.
   */
  public async createDataBase(): Promise<boolean> {
    let createResult: boolean = false;
    let tableConfig: string = `CREATE TABLE IF NOT EXISTS ${Field.TABLE} (id INTEGER PRIMARY KEY ` +
      `AUTOINCREMENT, ${Field.TAG} TEXT NOT NULL, ${Field.CLIENT_TIME} INTEGER NOT NULL, ` +
      `${Field.REQUEST_ID} TEXT NOT NULL, ${Field.SIZE} INTEGER NOT NULL, ${Field.CONTENT} TEXT NOT NULL, ` +
      `${Field.APP_NAME} TEXT NOT NULL, ${Field.APP_VER} TEXT NOT NULL, ${Field.TRIGGER} TEXT NOT NULL, ` +
      `${Field.DATA_TYPE} TEXT NOT NULL, ${Field.SN} TEXT, ${Field.ODID} TEXT, ${Field.UID} TEXT, ${Field.UUID} TEXT)`;
    let options: distributedKVStore.Options = {
      kvStoreType: distributedKVStore.KVStoreType.SINGLE_VERSION,
      securityLevel: distributedKVStore.SecurityLevel.S1
    }
    let config: distributedKVStore.KVManagerConfig = {
      context: this.context,
      bundleName: this.context.applicationInfo.name
    }

    // create a database and establish the table if not exists.
    try {
      this.store = await relationalStore.getRdbStore(this.context, this.storeConfig);
      await this.store.executeSql(tableConfig);
      // create a kvStore
      Log.info(this.TAG, `begin to getKVstore.`);
      let kvManager = distributedKVStore.createKVManager(config);
      this.kvStore = await kvManager.getKVStore("aiTuneBase_data", options);

      createResult = true;
      Log.info(this.TAG, `create database and table success.`);
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `create database and table failed with errCode: ${err.code}, errMessage: ${err.message}.`);
    }
    return createResult;
  }

  /**
   * add a piece of report data to the database and deleted expired data.
   *
   * @params { ReportContent } content - the main part of content.
   * @params { ReportConfig } config - the additional part of content.
   * @params { number }  - the additional part of content.
   * @return { Promise<boolean> } whether the data in database satisfies the submit condition.
   */
  public async addData(content: ReportContent, config: ReportConfig, size: number): Promise<boolean> {
    if (!this.store) {
      Log.error(this.TAG, `can not find store, please createDatabase first.`)
      return Promise.reject(ErrMaker.CACHE_CODE);
    }
    let time = new Date().getTime();
    let contentValue: ValuesBucket = this.getValueBucket(content, config, size, time);
    let lastTime = await this.getKV(this.TIME);
    let dataCount = await this.getKV(this.COUNT);
    let datasize = await this.getKV(this.SIZE);
    let interval: number = 0;
    if (lastTime === NUM_ZERO) {
      await this.setKV(this.TIME, time);
    } else {
      interval = time - lastTime;
    }
    //insert and delete expired data.
    try {
      let rowId = await this.store.insert(Field.TABLE, contentValue);
      dataCount++;
      datasize += size;
      await this.setKV(this.COUNT, dataCount);
      await this.setKV(this.SIZE, datasize);
      Log.info(this.TAG, `insert success, rowId: ${rowId}.`);
      await this.deleteExpiredData(time, config.dataDuration as number);
      dataCount = await this.getKV(this.COUNT);
      datasize = await this.getKV(this.SIZE)

      return (dataCount >= COUNT_REQUEST || datasize >= SIZE_REQUEST || interval > StoreManager.MULTI);
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `insert failed with code: ${err.code}, message: ${err.message}`);
      return Promise.reject(ErrMaker.CACHE_CODE);
    }
  }

  /**
   * add a piece of report data to the database and deleted expired data.
   *
   * @params { ReportContent } content - the main part of content.
   * @params { ReportConfig } config - the additional part of content.
   * @params { number }  - the additional part of content.
   * @return { Promise<boolean> } whether the data in database satisfies the submit condition.
   */
  public async addDataBatch(content: ReportContent[], config: ReportConfig, size: number[]): Promise<void> {
    if (!this.store) {
      Log.error(this.TAG, `can not find store, please createDatabase first.`)
      return Promise.reject(ErrMaker.INNER_CODE);
    }
    let time = new Date().getTime();
    let list: Array<ValuesBucket> = []
    for (let i = NUM_ZERO; i < content.length; i++) {
      list.push(this.getValueBucket(content[i], config, size[i], time))
    }

    let dataCount = await this.getKV(this.COUNT);
    let datasize = await this.getKV(this.SIZE);
    //insert and delete expired data.
    try {
      let rowNum = await this.store.batchInsert(Field.TABLE, list);
      dataCount += content.length;
      size.forEach((value) => {
        datasize += value
      })
      await this.setKV(this.COUNT, dataCount);
      await this.setKV(this.SIZE, datasize);
      Log.info(this.TAG, `batch insert success, rowNum: ${rowNum}.`);
      await this.deleteExpiredData(time, config.dataDuration as number);

      return Promise.resolve();
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `insert failed with code: ${err.code}, message: ${err.message}`);
      return Promise.reject(ErrMaker.INNER_CODE);
    }
  }


  /**
   * delete the expired data content in the database.
   *
   * @params { number } currentTime - the current time stamp(ms).
   * @params { number } duration - the max duration that the data could be cached.
   */
  private async deleteExpiredData(currentTime: number, duration: number): Promise<void> {
    if (!this.store) {
      Log.error(this.TAG, `can not find store, please createDatabase first.`)
      return Promise.reject(new Error("no database found, please createDatabase first."));
    }
    let dataCount = await this.getKV(this.COUNT);
    let datasize = await this.getKV(this.SIZE);
    let queryData = new relationalStore.RdbPredicates(Field.TABLE);
    queryData.lessThan(Field.CLIENT_TIME, currentTime - duration * StoreManager.MULTI);

    try {
      let resultSet = await this.store.query(queryData);
      while(resultSet.goToNextRow()) {
        dataCount--;
        datasize-= resultSet.getLong(resultSet.getColumnIndex(Field.SIZE));
      }
      resultSet.close();
      let deleteRow = await this.store.delete(queryData);
      Log.info(this.TAG, `delete expire data success: ${deleteRow} rows deleted. remains dataCount: ${dataCount}, ` +
        `dataSize: ${datasize}.`);
      await this.setKV(this.COUNT, dataCount);
      await this.setKV(this.SIZE, datasize);
      if (dataCount > StoreManager.MAX_DATA) {
        await this.deleteExtraData(dataCount, datasize);
      }
      return Promise.resolve();
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `delete expired data failed with code: ${err.code}, ${err.message}`);
      return Promise.reject(err);
    }
  }

  /**
   * delete the extra data content that exceeds max limit in the database.
   *
   * @params { number } count - the data count.
   * @params { number } size - the data size.
   */
  private async deleteExtraData(count: number, size: number): Promise<void>{
    let deleteIdList: number[] = [];
    let predicates = new relationalStore.RdbPredicates(Field.TABLE);
    predicates.isNotNull("id");
    try {
      let resultSet = await this.store?.query(predicates);
      while (resultSet?.goToNextRow() && count > StoreManager.MAX_DATA) {
        count--;
        size-=resultSet.getLong(resultSet.getColumnIndex(Field.SIZE));
        deleteIdList.push(resultSet.getLong(resultSet.getColumnIndex("id")))
      }
      resultSet?.close();
      let deletePredicates = new relationalStore.RdbPredicates(Field.TABLE);
      deletePredicates.between("id", deleteIdList[NUM_ZERO], deleteIdList[deleteIdList.length - NUM_ONE]);
      let deleteRows = await this.store?.delete(deletePredicates);
      await this.setKV(this.COUNT, count);
      await this.setKV(this.SIZE, size)
      Log.info(this.TAG, `delete extra data success: ${deleteRows} rows deleted. remains dataCount: ${count}, ` +
        `dataSize: ${size}.`);
      return Promise.resolve();
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `delete extra data failed with code: ${err.code}, ${err.message}`);
      return Promise.reject(err);
    }
  }


  /**
   * get at most 30 pieces or 50KB of data content in the database.
   *
   * @params { ReportDeviceInfo } deviceInfo - the deviceInfo.
   * @return { Promise<Content[]> } the content list.
   */
  public async getContent(deviceInfo: ReportDeviceInfo): Promise<Content[]> {
    if (!this.store) {
      Log.error(this.TAG, `can not find store, please createDatabase first.`)
      return Promise.reject(ErrMaker.INNER_CODE);
    }
    let dataCount: number = NUM_ZERO;
    let dataSize: number = NUM_ZERO;
    let contentList: Content[] = new Array();
    // clear the idList.
    this.idList.length = NUM_ZERO;
    this.sizeList.length = NUM_ZERO;
    let predicates = new relationalStore.RdbPredicates(Field.TABLE);
    predicates.isNotNull("id");
    try {
      let resultSet = await this.store.query(predicates);
      while (resultSet.goToNextRow() && dataCount < MAX_COUNT && dataSize < MAX_SIZE) {
        dataCount++;
        let size = resultSet.getLong(resultSet.getColumnIndex(Field.SIZE));
        dataSize += size
        contentList.push(this.getReportContent(resultSet, deviceInfo));
        this.idList.push(resultSet.getLong(resultSet.getColumnIndex("id")));
        this.sizeList.push(size);
      }
      resultSet.close();
      if (dataSize > MAX_SIZE) {
        contentList.pop();
        this.idList.pop();
        this.sizeList.pop();
        dataCount--;
      }
      Log.info(this.TAG, `get data content success, ${dataCount} rows, the id list: ${JSON.stringify(this.idList)}`);
      return contentList;
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `get data content failed with code: ${err.code}, message: ${err.message}.`)
      return Promise.reject(ErrMaker.INNER_CODE);
    }
  }

  /**
   * delete the data that has been reported successfully.
   *
   * returns { boolean } whether need report again.
   */
  public async deleteReportData(): Promise<boolean> {
    if (!this.store) {
      Log.error(this.TAG, `can not find store, please createDatabase first.`);
      return Promise.reject(ErrMaker.INNER_CODE);
    }
    if (!this.idList.length) {
      Log.warn(this.TAG, `the idList is empty, no need to delete.`);
      return false;
    }
    let low = this.idList[NUM_ZERO];
    let high = this.idList[this.idList.length - NUM_ONE];
    Log.info(this.TAG, `begin to delete report data, the id list: ${this.idList}. low: ${low}, high: ${high}.`)

    let predicates = new relationalStore.RdbPredicates(Field.TABLE);
    predicates.between("id", low, high);
    let count = await this.getKV(this.COUNT);
    let size = await this.getKV(this.SIZE);
    try {
      let deleteRows = await this.store.delete(predicates);
      count -= deleteRows;
      this.sizeList.forEach((value) => {
        size -= value;
      })
      await this.setKV(this.COUNT, count);
      await this.setKV(this.SIZE, size);
      await this.setKV(this.TIME, new Date().getTime());
      Log.info(this.TAG, `delete report data success, rowCounts: ${deleteRows}, ${count} and ${size} Bytes remian.`);
      // clear the id list after deletion.
      this.idList.length = NUM_ZERO;
      this.sizeList.length = NUM_ZERO;
      return (count > COUNT_REQUEST || size > SIZE_REQUEST);
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `delete report data failed with code: ${err.code}, message: ${err.message}.`)
      return Promise.reject(ErrMaker.INNER_CODE);
    }
  }

  /**
   * calculate how many times to report according to dataSize and dataCount.
   *
   * @return { number } the times expected to report.
   */
  public async getReportTimes(): Promise<number> {
    let dataCount = await this.getKV(this.COUNT);
    let dataSize = await this.getKV(this.SIZE);
    // at least cCount times according to dataCount, considering that one report contains at most 30 pieces.
    let cCount = Math.floor(dataCount / MAX_COUNT);
    // at least sCount times according to dataSize, considering that one report contains at most 50KB.
    let sCount = Math.floor(dataSize / MAX_SIZE);
    let reportCount = Math.max(cCount, sCount);
    Log.warn(this.TAG, `getReportTimes success: ${reportCount}.`)
    //if result is zero, then at least one report is needed
    return reportCount ? reportCount : NUM_ONE;
  }

  /**
   * get valueBucket from the content.
   *
   * @params { ReportContent } content - the report content.
   * @params { ReportConfig } config - the report config.
   * @params { number } size - the report content size.
   * @params { number } time - the client time.
   * @return { ValuesBucket } the valueBucket.
   */
  private getValueBucket(content: ReportContent, config: ReportConfig, size: number, time: number): ValuesBucket {
    // fill up the missing properties in the content.
    let tag: string = content.instanceTag ?? config.instanceTag;
    let contentValue: ValuesBucket = {
      "instanceTag": tag,
      "clientTime": time,
      "requestId": tag + "_" + time,
      "size": size,
      "content": content.content,
      "appName": content.appName ?? config.appName,
      "appVersion": content.appVersion ?? config.appVersion,
      "trigger": content.trigger ?? config.trigger,
      "dataType": content.dataType ?? config.dataType,
      "sn": content.sn ?? config.sn ?? EMPTY_STRING,
      "odid": content.odid ?? config.odid ?? EMPTY_STRING,
      "uid": content.uid ?? config.uid ?? EMPTY_STRING,
      "uuid": content.uuid ?? config.uuid ?? EMPTY_STRING
    }
    return contentValue;
  }

  /**
   * get report content from the database resultSet.
   *
   * @params { relationalStore.ResultSet } data - the resultSet of query database.
   * @params { ReportDeviceInfo } deviceInfo - the deviceInfo.
   * @return { Content } the report content.
   */
  private getReportContent(data: relationalStore.ResultSet, deviceInfo: ReportDeviceInfo): Content {
    let content: Content = {
      appName: data.getString(data.getColumnIndex(Field.APP_NAME)),
      trigger: data.getString(data.getColumnIndex(Field.TRIGGER)),
      dataType: data.getString(data.getColumnIndex(Field.DATA_TYPE)),
      clientTime: data.getLong(data.getColumnIndex(Field.CLIENT_TIME)),
      content: data.getString(data.getColumnIndex(Field.CONTENT)),
      appVersion: data.getString(data.getColumnIndex(Field.APP_VER)),
      sn: data.getString(data.getColumnIndex(Field.SN)),
      udid: data.getString(data.getColumnIndex(Field.ODID)),
      uid: data.getString(data.getColumnIndex(Field.UID)),
      uuid: data.getString(data.getColumnIndex(Field.UUID)),
      deviceType: deviceInfo.deviceCategory,
      model: deviceInfo.model,
      osVersion: deviceInfo.osVersion,
      brand: deviceInfo.brand,
      manufacturer: deviceInfo.manufacturer
    };
    return content;
  }

  /**
   * if the count and size recorded in KVStore is abnormal, recount and recover.
   */
  private async recover(): Promise<void> {
    let dataCount: number = NUM_ZERO;
    let dataSize: number = NUM_ONE;
    let predicates: relationalStore.RdbPredicates = new relationalStore.RdbPredicates(Field.TABLE);
    predicates.isNotNull("id");
    try {
      let res = await this.store?.query(predicates);
      while (res?.goToNextRow()) {
        dataCount++;
        dataSize += res.getLong(res.getColumnIndex(Field.SIZE));
      }
      res?.close();
      await this.setKV(this.COUNT, dataCount);
      await this.setKV(this.SIZE, dataSize);
      Log.info(this.TAG, `recovery success, now remains ${dataCount} pieces and ${dataSize} Bytes data.`)
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `recovery failed with code: ${err.code}, message: ${err.message}.`)
    }
    return;
  }

  /**
   * get kvStore value.
   *
   * @params { string } key - the key.
   * @returns { Promise<number|undefined> } the value.
   */
  private async getKV(key: string): Promise<number> {
    try {
      let num = await this.kvStore?.get(key) as number;
      Log.info(this.TAG, `get kv success, key: ${key}, value: ${num}`);
      // if the result is abnormal, recover and recount.
      if (num < NUM_ZERO) {
        await this.recover();
      }
      return num;
    } catch (e) {
      let err = e as BusinessError;
      Log.warn(this.TAG, `get kv failed with code: ${err.code}, message: ${err.message}.`)
      return Promise.resolve(NUM_ZERO)
    }
  }

  /**
   * set kvStore value.
   *
   * @params { string } key - the key.
   * @params { number } value - the value.
   */
  private async setKV(key: string, value: number): Promise<void> {
    try {
      await this.kvStore?.put(key, value)
      Log.info(this.TAG, `set kv success, key: ${key}, value: ${value}.`);
      return Promise.resolve();
    } catch (e) {
      let err = e as BusinessError;
      Log.error(this.TAG, `set kv failed with code: ${err.code}, message: ${err.message}.`)
      return Promise.reject(err)
    }
  }

}