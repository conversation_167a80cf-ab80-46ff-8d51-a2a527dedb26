/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import connection from '@ohos.net.connection';
import taskpool from '@ohos.taskpool';
import { BusinessError } from '@kit.BasicServicesKit';
import Log from './utils/Log';
import { reportThroughHttp } from './taskpoolFunc/httpRequestFunc';
import { HeaderInfo, Content, ReportConfig, ReportDeviceInfo, REPORT_SUCCESS_CODE } from './utils/Constants';
import { RequestStringBody } from './utils/RequestStringBody'
import {
  HttpClient,
  Request,
  RequestOption,
  Header,
  HttpClientOption,
  RequestBody,
  IWriteTo,
  HttpClientRequestCallback,
  RequestFinishedInfo,
  Response,
  NetworkUrlCode
} from '@hms-network/url';
import { util } from '@kit.ArkTS';


// the http response indicates that whether the http request is success
interface ResponseInfo extends Object {
  code: number;
  desc: string;
}


/**
 * check the network and post http request.
 *
 * <AUTHOR>
 * @since 2024-5-28
 */
export default class NetworkManager {
  // TAG used to print the log.
  private static TAG: string = "aiTuneBase_network";
  // the singleton instance of NetWorkManager.
  private static instance: NetworkManager;
  // the netId that indicates there's no default netWork.
  private static NO_NET_ID: number = 0;
  // the netId that indicates network is available.
  private static HAS_NET_ID: number = 100;

  private static httpClient: HttpClient | undefined = undefined


  /**
   * get httpClient.
   *
   * @returns singleton instance of httpClient
   */
  private static getClient(): HttpClient {
    if (NetworkManager.httpClient === undefined) {
      let httpClientOption: HttpClientOption = new HttpClientOption();
      NetworkManager.httpClient = new HttpClient(httpClientOption);
      NetworkManager.httpClient.init();
    }
    return NetworkManager.httpClient
  }


  /**
   * check whether network is available.
   *
   * @return { boolean } whether network is available or not.
   */
  public static checkNetwork(): boolean {
    let netStatus = false;
    try {
      let netHandle = connection.getDefaultNetSync();
      if (netHandle.netId === NetworkManager.NO_NET_ID) {
        Log.warn(NetworkManager.TAG, `default network is unavailable, the netId: ${netHandle.netId}.`);
      } else {
        Log.info(NetworkManager.TAG, `default network is available, the netId: ${netHandle.netId}.`);
        netStatus = true;
      }
    } catch (e) {
      let err = e as BusinessError;
      Log.error(NetworkManager.TAG, `get default network failed with code: ${err.code}, message: ${err.message}.`);
    }
    return netStatus;
  }

  /**
   * build the http request header.
   *
   * @params { ReportConfig } config - the report config.
   * @params { ReportDeviceInfo } deviceInfo -the deviceInfo
   * @return { Header } the Header.
   */
  public static buildHeader(config: ReportConfig, deviceInfo: ReportDeviceInfo): HeaderInfo {
    let header: HeaderInfo = {
      contentType: "application/json",
      deviceId: config.deviceId,
      appName: config.appName,
      appVersion: config.appVersion,
      deviceCategory: deviceInfo.deviceCategory,
      model: deviceInfo.model,
      requestId: config.instanceTag + "_" +new Date().getTime()
    }
    return header;
  }


  /**
   * send httpRequest through network/url
   *
   * @params { url } post url
   * @params { header } post header
   * @params {content} post content
   * @returns weather report successfully or not
   */
  public static async sendHttpRequest(url: string, header: HeaderInfo, content: Content[]): Promise<boolean> {
    let httpClient = NetworkManager.getClient();
    let requestOptions: RequestOption = NetworkManager.getRequestOption(url, header, content);
    let httpRequest = new Request(httpClient, requestOptions);
    httpRequest.init();
    try {
      Log.info(NetworkManager.TAG, `begin to send http request.`)
      let res: Response = await httpRequest.syncEnqueue()
      Log.info(NetworkManager.TAG, `send http request finished, response code: ${JSON.stringify(res.code)}.`)
      let responseContent: string = new util.TextDecoder("utf-8", {ignoreBOM : true}).decode(new Uint8Array(res.content));
      Log.info(NetworkManager.TAG, `send http request finished, response content: ${JSON.stringify(responseContent)}.`)
      let resultInfo = JSON.parse(responseContent) as ResponseInfo
      return resultInfo.code === REPORT_SUCCESS_CODE
    } catch (e) {
      Log.error(NetworkManager.TAG, `send http request failed: ${JSON.stringify(e)}`);
      return false
    } finally {
      if (NetworkManager.httpClient?.getRequestSize() === 0) {
        NetworkManager.httpClient.releaseHttpClient();
        NetworkManager.httpClient = undefined
      }
    }
  }


  private static getRequestOption(url: string, headerInfo: HeaderInfo, contentList: Content[]): RequestOption {
    let requestOption = new RequestOption();
    requestOption.setUrl(url);
    requestOption.setMethod("POST");
    // build head
    let headerKeys = Object.getOwnPropertyNames(headerInfo);
    headerKeys.forEach((key: string) => {
      let value = headerInfo[key] as string
      requestOption.addHeader(new Header(key, value))
    });
    // build body
    requestOption.setRequestBody(new RequestStringBody(JSON.stringify(contentList), 'application/json'));

    return requestOption;
  }

  /**
   * execute report task in taskPool.
   *
   * @params { string } url - the report url.
   * @params { Header } header - the http request header.
   * @params { Content[] } content - the http request content array.
   * @returns { Promise<boolean> } return the http request result.
   */
  public reportInTaskPool(url: string, header: HeaderInfo, content: Content[]): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      Log.info(NetworkManager.TAG, `begin to execute http request task.`);
      taskpool.execute(reportThroughHttp, url, header, content).then((data: Object) => {
        Log.info(NetworkManager.TAG, `report in taskpool success. the result is ${data as boolean}.`);
        resolve(data as boolean);
      }).catch((err: BusinessError) => {
        Log.error(NetworkManager.TAG, `report in taskpool failed with code: ${err.code}, message: ${err.message}.`);
        resolve(false);
      })
    })
  }
}