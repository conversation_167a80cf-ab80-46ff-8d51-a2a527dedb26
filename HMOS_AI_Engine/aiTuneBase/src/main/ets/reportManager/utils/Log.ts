/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import hilog from '@ohos.hilog';

/**
 * Encapsulation of hilog.
 *
 * <AUTHOR>
 * @since 2024-4-15
 */
export default class Log {
  // the domain of aiTuneBase sdk.
  private static readonly DOMAIN: number = 0xFFFD;

  /**
   * print the log in debug level.
   *
   * @params { string } tag - tag of log.
   * @params { string } message - message of log.
   */
  public static debug(tag: string, message: string): void {
    hilog.debug(Log.DOMAIN, tag, message);
  }

  /**
   * print the log in info level.
   *
   * @params { string } tag - tag of log.
   * @params { string } message - message of log.
   */
  public static info(tag: string, message: string): void {
    hilog.info(Log.DOMAIN, tag, message);
  }

  /**
   * print the log in warn level.
   *
   * @params { string } tag - tag of log.
   * @params { string } message - message of log.
   */
  public static warn(tag: string, message: string): void {
    hilog.warn(Log.DOMAIN, tag, message);
  }

  /**
   * print the log in error level.
   *
   * @params { string } tag - tag of log.
   * @params { string } message - message of log.
   */
  public static error(tag: string, message: string): void {
    hilog.error(Log.DOMAIN, tag, message);
  }
}
