/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */


/**
 * ErrorManager to build error with code and message.
 *
 * <AUTHOR>
 * @since 2024-4-15
 */
export class ErrMaker extends Error {
  // errCode of invalid input parameter
  public static readonly INPUT_CODE: number = 10001000;
  // errMsg of invalid input parameter
  public static readonly INPUT_MSG: string = "Invalid input parameter.";
  // errCode of no config
  public static readonly CONFIG_CODE: number = 10001001;
  // errMsg of no config
  public static readonly CONFIG_MSG: string = "No config found, please init first.";
  // errCode of cache failed
  public static readonly CACHE_CODE: number = 10001002;
  // errMsg of cache failed
  public static readonly CACHE_MSG: string = "cache data failed.";
  // errCode of report failed
  public static readonly REPORT_CODE: number = 10001003;
  // errMsg of report failed
  public static readonly REPORT_MSG: string = "report to Hua<PERSON>han dataHub failed.";
  // errCode of inner error.
  public static readonly INNER_CODE: number = 10001004;
  // errMsg of inner error.
  public static readonly INNER_MSG: string = "Inner error."

  // error code.
  public code: number;
  // error message.
  public message: string;

  /**
   * constructor
   *
   * @param code - error code
   * @param message - error message
   */
  constructor(code: number, message: string) {
    super();
    this.code = code;
    this.message = message;
    this.name = "aiTuneBaseError"
  }

  /**
   * convert the error instance to a string.
   *
   * @returns an error string.
   */
  public toString(): string {
    let errString: string = `${this.name} -> errorCode: ${this.code}, errMessage: ${this.message}.`;
    return errString;
  }
}