/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

// the max length of a string.
export const MAX_LENGTH: number = 128;
// the default duration of data.
export const DEFAULT_DURATION: number = 7;
// the max duration of data.
export const MAX_DURATION: number = 30;
// the min duration of data.
export const MIN_DURATION: number = 1;
// number one.
export const NUM_ONE: number = 1;
// number zero.
export const NUM_ZERO: number = 0;
// number three.
export const NUM_THREE: number = 3;
// the retry interval
export const INTERVAL: number = 30000;
// when the dataCount reach the amount, it can be submitted.
export const COUNT_REQUEST: number = 25;
// when the dataSize reach the amount, it can be submitted.
export const SIZE_REQUEST: number = 30 * 1024;
// the max count of data to be submitted.
export const MAX_COUNT: number = 30;
// the max size of data to be submitted.
export const MAX_SIZE: number = 50 * 1024;
// default empty string
export const EMPTY_STRING: string = "";
// report success code
export const REPORT_SUCCESS_CODE: number = 200;

// the deviceInfo that will be submitted and is collected by sdk automatically.
export interface ReportDeviceInfo {
  deviceCategory: string;
  model: string;
  osVersion: string;
  brand: string;
  manufacturer: string;
}

// the report config info that user need to input.
export interface ReportConfig {
  urlType: ReportUrlType;
  encrypt: boolean;
  deviceId: string;
  appName: string;
  appVersion: string;
  trigger: string;
  dataType: string;
  instanceTag: string;
  sn?: string;
  odid?: string;
  uid?: string;
  uuid?: string;
  dataDuration?: number;
}

// the report content that would be submitted.
export interface ReportContent {
  content: string;
  dataType?: string;
  trigger?: string;
  instanceTag?: string;
  deviceId?: string;
  appName?: string;
  appVersion?: string;
  sn?: string;
  odid?: string;
  uid?: string;
  uuid?: string;
}

export enum ReportUrlType {
  LIVE = 0,
  TEST = 1
}

// the content column name that stored in the database.
export enum Field {
  TABLE = "content",
  TAG = "instanceTag",
  CLIENT_TIME = "clientTime",
  REQUEST_ID = "requestId",
  SIZE = "size",
  CONTENT = "content",
  APP_NAME = "appName",
  APP_VER = "appVersion",
  TRIGGER = "trigger",
  DATA_TYPE = "dataType",
  SN = "sn",
  ODID = "odid",
  UID = "uid",
  UUID = "uuid"
}

// the http request header
export interface HeaderInfo {
  contentType: string
  deviceId: string;
  appName: string;
  appVersion: string;
  deviceCategory: string;
  model?: string;
  requestId?: string;
  contentEncoding?: string;
}

// the http request content
export interface Content {
  appName: string;
  trigger: string;
  dataType: string;
  clientTime: number;
  content: string;
  deviceType: string;
  model: string;
  osVersion: string;
  appVersion?: string;
  brand?: string;
  manufacturer?: string;
  sn?: string;
  udid?: string;
  uid?: string;
  uuid?: string;
}

// the content info
export interface ContentInfo {
  contentList: Content[];
  sizeList: number[];
}
