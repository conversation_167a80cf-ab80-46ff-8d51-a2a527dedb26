/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import deviceInfo from '@ohos.deviceInfo';
import relationalStore from '@ohos.data.relationalStore';
import { ValuesBucket } from '@ohos.data.ValuesBucket';
import { ReportConfig, MAX_LENGTH, ReportDeviceInfo, ReportContent, EMPTY_STRING, Content, Field, HeaderInfo } from './Constants';
import Log from './Log';

/**
 * assistant utils.
 *
 * <AUTHOR>
 * @since 2024-4-15
 */
export class Utils {
  // TAG used to print log.
  private static readonly TAG: string = 'aiTuneBase_utils'

  /**
   * get the deviceInfo.
   *
   * @return { ReportDeviceInfo } the deviceInfo.
   */
  public static getDeviceInfo(): ReportDeviceInfo {
    let reportDeviceInfo: ReportDeviceInfo = {
      deviceCategory: deviceInfo.deviceType,
      model: deviceInfo.productModel,
      osVersion: deviceInfo.osFullName,
      brand: deviceInfo.brand,
      manufacturer: deviceInfo.manufacture
    };
    return reportDeviceInfo;
  }


  /**
   * sleep and await given seconds.
   *
   * @params { number } sleep time.
   */
  public static sleep(time: number): Promise<void> {
    return new Promise(resolve => {
      let timer = setTimeout(()=>{
        Log.info(Utils.TAG, `sleep ${time} ms.`);
        clearTimeout(timer);
        resolve();
      }, time)
    })
  }
}

