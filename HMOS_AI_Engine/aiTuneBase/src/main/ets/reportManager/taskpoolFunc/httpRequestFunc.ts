/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import http from '@ohos.net.http';
import { HeaderInfo, Content, REPORT_SUCCESS_CODE } from '../utils/Constants';
import { BusinessError } from '@kit.BasicServicesKit';
import Log from '../utils/Log';


// the http response indicates that whether the http request is success
interface Response extends Object {
    code: number;
    desc: Object;
}

/**
 * http request task function.
 *
 * @params { string } url - the report url.
 * @params { Header } header - the http request header.
 * @params { Content[] } content - the http request content array.
 * @returns { Promise<boolean> } return the http request result.
 * <AUTHOR>
 * @since 2024-5-28
 */
const reportThroughHttp = async function (url: string, header: HeaderInfo, content: Content[]): Promise<boolean> {
    "use concurrent";
    let tag: string = "aiTuneBase_http";
    let options: http.HttpRequestOptions = {
        method: http.RequestMethod.POST,
        header: header,
        extraData: JSON.stringify(content)
    };
    let result: boolean = false;
    let tuneBaseRequest = http.createHttp();
    Log.info(tag, `begin to send http request.`)
    // try to send http request. If the response code is 200, report success.
    try {
        let response = await tuneBaseRequest.request(url, options);
        Log.info(tag, `send http request success, response: ${JSON.stringify(response)}.`)
        if (typeof(response.result) === 'string') {
            let httpResult = JSON.parse(response.result as string) as Response;
            result = httpResult.code === REPORT_SUCCESS_CODE ? true : false
        } else if(typeof(response.result) === 'object') {
            result = response.result['code'] === REPORT_SUCCESS_CODE ? true : false
        }
    } catch (e) {
        let err = e as BusinessError;
        Log.error(tag, `send http request failed: ${err.code}, ${err.message}.`)
    } finally {
        tuneBaseRequest.destroy();
    }
    return result;
}

export { reportThroughHttp }