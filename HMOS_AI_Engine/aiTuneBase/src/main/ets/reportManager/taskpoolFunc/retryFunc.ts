/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import connection from '@ohos.net.connection';
import { reportThroughHttp } from './httpRequestFunc';
import { Content, HeaderInfo, NUM_ZERO, NUM_ONE, NUM_THREE, INTERVAL } from '../utils/Constants';
import Log from '../utils/Log'
import { BusinessError } from '@kit.BasicServicesKit';
import { taskpool } from '@kit.ArkTS';

/**
 * retry task function.
 *
 * @params { string } url - the report url.
 * @params { Header } header - the http request header.
 * @params { Content[] } content - the http request content array.
 * @returns { Promise<boolean> } return the retry result.
 * <AUTHOR>
 * @since 2024-5-28
 */
const retry = async function(url: string, header: HeaderInfo, content: Content[]): Promise<boolean> {
  "use concurrent";
  let tag: string = "aiTuneBase_http";
  let promise = new Promise<boolean> (resolve => {
    let tryCount = NUM_ZERO;
    let task = new taskpool.Task(reportThroughHttp, url, header, content);
    // retry for at most 3 times.
    let timer = setInterval(async () => {
      try {
        // check the network before report. If network is unavailable, terminate and return false.
        let id = connection.getDefaultNetSync().netId;
        if (id === NUM_ZERO) {
          Log.error(tag, `network is unavailable, stop retry. the requestId: ${header.requestId}.`);
          clearInterval(timer);
          resolve(false);
        }
        // try to execute report task. If retry success, terminate and return true.
        let httpResult: boolean = await taskpool.execute(task) as boolean
        if (httpResult) {
          Log.info(tag, `report retry success, tryCount: ${tryCount + NUM_ONE}, the requestId: ${header.requestId}.`);
          clearInterval(timer);
          resolve(true);
        }
      } catch (e) {
        // catch the error that may occurred when getting net status or executing task.
        let err = e as BusinessError;
        Log.error(tag, `execute failed with code: ${err.code}, ${err.message}.`);
        clearInterval(timer);
        resolve(false);
      }

      tryCount++;
      // retry at most 3 times.
      if (tryCount >= NUM_THREE) {
        clearInterval(timer);
        Log.error(tag, `report failed after retry 3 times. the requestId: ${header.requestId}.`);
        resolve(false);
      }
    }, INTERVAL)
  })
  return await promise;
}

export { retry }