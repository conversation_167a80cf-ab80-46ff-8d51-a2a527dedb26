#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 532676608 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3825), pid=71288, tid=77984
#
# JRE version:  (17.0.12+1) (build )
# Java VM: OpenJDK 64-Bit Server VM (17.0.12+1-b1087.25, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\hm_sign\hap-sign-tool.jar sign-app -mode remoteSign -signServer http://cpki-kms.cbg.huawei.com:18888/sign -signerPlugin hapsign-online-plugin.jar -onlineAuthMode account -username w30071960 -userPwd !mzdxcfkwaj0801 -profileFile D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\hm_sign\119429release.p7b -compatibleVersion 8 -signAlg SHA256withECDSA -keyAlias HiAIEngineForHarmonyOS -inFile D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\support\build\default\outputs\default\support-entry-default-unsigned.hap -outFile D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\support\build\default\outputs\default\support-entry-default-signed.hap

Host: 13th Gen Intel(R) Core(TM) i7-13700, 24 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.2506)
Time: Tue Jun 24 16:07:38 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.2506) elapsed time: 0.017399 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001f3e5a82580):  JavaThread "Unknown thread" [_thread_in_vm, id=77984, stack(0x000000efad500000,0x000000efad600000)]

Stack: [0x000000efad500000,0x000000efad600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x688dd9]
V  [jvm.dll+0x842eaa]
V  [jvm.dll+0x844aee]
V  [jvm.dll+0x845153]
V  [jvm.dll+0x24ba3f]
V  [jvm.dll+0x685ba9]
V  [jvm.dll+0x67a45a]
V  [jvm.dll+0x30c60b]
V  [jvm.dll+0x313ab6]
V  [jvm.dll+0x363c6e]
V  [jvm.dll+0x363e9f]
V  [jvm.dll+0x2e2658]
V  [jvm.dll+0x2e3594]
V  [jvm.dll+0x813b31]
V  [jvm.dll+0x3719c1]
V  [jvm.dll+0x7f2626]
V  [jvm.dll+0x3f563f]
V  [jvm.dll+0x3f7191]
C  [jli.dll+0x528f]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1257d]
C  [ntdll.dll+0x5aa58]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffd9e4d0f58, length=0, elements={
}

Java Threads: ( => current thread )

Other Threads:
  0x000001f3fc090900 GCTaskThread "GC Thread#0" [stack: 0x000000efad600000,0x000000efad700000] [id=33956]
  0x000001f3fc0a1600 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000efad700000,0x000000efad800000] [id=35060]
  0x000001f3fc0a2020 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000efad800000,0x000000efad900000] [id=75452]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd9dc831c7]

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001f3e5a7a1e0] Heap_lock - owner thread: 0x000001f3e5a82580

Heap address: 0x0000000605c00000, size: 8100 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
<Empty>

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000605c00000, 0x0000000800000000)
  region size 4096K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)

Card table byte_map: [0x000001f3f9fc0000,0x000001f3fafa0000] _byte_map_base: 0x000001f3f6f92000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001f3fc090f20, (CMBitMap*) 0x000001f3fc090f60
 Prev Bits: [0x000001f380000000, 0x000001f387e90000)
 Next Bits: [0x000001f387e90000, 0x000001f38fd20000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.005 Loaded shared library D:\Huawei\DevEco Studio\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

VM Operations (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff699ba0000 - 0x00007ff699baa000 	D:\Huawei\DevEco Studio\jbr\bin\java.exe
0x00007ffe36910000 - 0x00007ffe36b27000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffe35b10000 - 0x00007ffe35bd4000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffe33fa0000 - 0x00007ffe34346000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffe16cd0000 - 0x00007ffe16cf4000 	C:\Windows\System32\ghijt64win10.dll
0x00007ffe35be0000 - 0x00007ffe35c91000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffe35f40000 - 0x00007ffe35fe7000 	C:\Windows\System32\msvcrt.dll
0x00007ffe34f30000 - 0x00007ffe34fd5000 	C:\Windows\System32\sechost.dll
0x00007ffe34dd0000 - 0x00007ffe34ee7000 	C:\Windows\System32\RPCRT4.dll
0x00007ffe33d40000 - 0x00007ffe33e51000 	C:\Windows\System32\ucrtbase.dll
0x00007ffe1b0e0000 - 0x00007ffe1b0f7000 	D:\Huawei\DevEco Studio\jbr\bin\jli.dll
0x00007ffdf21f0000 - 0x00007ffdf220b000 	D:\Huawei\DevEco Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffe34680000 - 0x00007ffe3482e000 	C:\Windows\System32\USER32.dll
0x00007ffe33e60000 - 0x00007ffe33e86000 	C:\Windows\System32\win32u.dll
0x00007ffe34830000 - 0x00007ffe34859000 	C:\Windows\System32\GDI32.dll
0x00007ffe343d0000 - 0x00007ffe344e8000 	C:\Windows\System32\gdi32full.dll
0x00007ffe17280000 - 0x00007ffe17513000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.2506_none_270c5ae97388e100\COMCTL32.dll
0x00007ffe33f00000 - 0x00007ffe33f9a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffe34ef0000 - 0x00007ffe34f21000 	C:\Windows\System32\IMM32.DLL
0x00007ffe050b0000 - 0x00007ffe050bc000 	D:\Huawei\DevEco Studio\jbr\bin\vcruntime140_1.dll
0x00007ffde8a70000 - 0x00007ffde8afd000 	D:\Huawei\DevEco Studio\jbr\bin\msvcp140.dll
0x00007ffd9d990000 - 0x00007ffd9e615000 	D:\Huawei\DevEco Studio\jbr\bin\server\jvm.dll
0x00007ffde48f0000 - 0x00007ffde48f9000 	C:\Windows\SYSTEM32\WSOCK32.dll
0x00007ffe36500000 - 0x00007ffe36571000 	C:\Windows\System32\WS2_32.dll
0x00007ffe19720000 - 0x00007ffe1972a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffe26270000 - 0x00007ffe262a4000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffe32a90000 - 0x00007ffe32add000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffe32a70000 - 0x00007ffe32a83000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffe32cd0000 - 0x00007ffe32ce8000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffe2f430000 - 0x00007ffe2f43a000 	D:\Huawei\DevEco Studio\jbr\bin\jimage.dll
0x00007ffe312f0000 - 0x00007ffe31523000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffe34860000 - 0x00007ffe34be9000 	C:\Windows\System32\combase.dll
0x00007ffe36580000 - 0x00007ffe36657000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffe1ed50000 - 0x00007ffe1ed82000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffe34350000 - 0x00007ffe343ca000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffe1e7e0000 - 0x00007ffe1e805000 	D:\Huawei\DevEco Studio\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Huawei\DevEco Studio\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.2506_none_270c5ae97388e100;D:\Huawei\DevEco Studio\jbr\bin\server

VM Arguments:
java_command: D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\hm_sign\hap-sign-tool.jar sign-app -mode remoteSign -signServer http://cpki-kms.cbg.huawei.com:18888/sign -signerPlugin hapsign-online-plugin.jar -onlineAuthMode account -username w30071960 -userPwd !mzdxcfkwaj0801 -profileFile D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\hm_sign\119429release.p7b -compatibleVersion 8 -signAlg SHA256withECDSA -keyAlias HiAIEngineForHarmonyOS -inFile D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\support\build\default\outputs\default\support-entry-default-unsigned.hap -outFile D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\support\build\default\outputs\default\support-entry-default-signed.hap
java_class_path (initial): D:\DevEcoStudioProjects\HMS_AI_Engine_Background_Support_Traffic\HMOS_AI_Engine\HMOS_AI_Engine\hm_sign\hap-sign-tool.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 12                                        {product} {ergonomic}
     uint ConcGCThreads                            = 5                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 18                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 532676608                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8493465600                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 7602480                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122027880                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122027880                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8493465600                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\Huawei\DevEco Studio\jbr
PATH=D:\Huawei\DevEco Studio\tools\node;D:\Huawei\DevEco Studio\jbr\bin;C:\Program Files\Huawei\jdk1.8.0_402\bin;d:\cursor\resources\app\bin;C:\Program Files (x86)\Microsoft\Edge\Application;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Windows;C:\Windows\system32;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows;C:\Windows\system32;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Git\cmd;D:\nodejs\;C:\Program Files\Java\jdk-17\bin;C:\Program Files\Java\jdk-17\jre\bin;D:\Huawei\shuaji\hdc;D:\Huawei\shuaji\platform-tools;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;c:\User;D:\Program Files\VSCode-huawei\bin;D:\Program Files\anaconda3;D:\Program Files\anaconda3\Library\mingw-w64\bin;D:\Program Files\anaconda3\Library\usr\bin;D:\Program Files\anaconda3\Library\bin;D:\Program Files\anaconda3\Scripts;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;%DevEco Studio%;D:\Program Files\PyCharm 2024.1.4\bin;;D:\cursor\resources\app\bin;D:\Program Files\CLion 2024.3.2\bin;;D:\Program Files\MinGW\mingw64\bin;
USERNAME=w30071960
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 183 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

OOME stack traces (most recent first):
Classloader memory used:


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.2506)
OS uptime: 8 days 5:18 hours
Hyper-V role detected

CPU: total 24 (initial active 24) (12 cores per cpu, 2 threads per core) family 6 model 183 stepping 1 microcode 0x112, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv
Processor Information for processor 0
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 1
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 2
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 3
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 4
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 5
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 6
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 7
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 8
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 9
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 10
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 11
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 12
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 13
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 14
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 15
  Max Mhz: 2100, Current Mhz: 2100, Mhz Limit: 2100
Processor Information for processor 16
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 17
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 18
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 19
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 20
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 21
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 22
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491
Processor Information for processor 23
  Max Mhz: 2100, Current Mhz: 1500, Mhz Limit: 1491

Memory: 4k page, system-wide physical 32388M (3319M free)
TotalPageFile size 57988M (AvailPageFile size 492M)
current process WorkingSet (physical memory assigned to process): 11M, peak: 11M
current process commit charge ("private bytes"): 81M, peak: 589M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+1-b1087.25) for windows-amd64 JRE (17.0.12+1-b1087.25), built on 2024-08-30 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
