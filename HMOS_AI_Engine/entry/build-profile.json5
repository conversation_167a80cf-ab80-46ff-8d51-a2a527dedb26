{
  "apiType": 'stageMode',
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": [
              "./obfuscation-rules.txt"
            ]
          }
        }
      }
    },
  ],
  "buildOption": {
//    "compileMode": "esmodule",
    "napiLibFilterOption": {
      "pickFirsts": [
        "**/libcurl.so",
      ]
    }
  },
  "entryModules": [
    "entry"
  ],
  "targets": [
    {
      "name": "default"
    },
    {
      "name": "ohosTest",
    }
  ]
}