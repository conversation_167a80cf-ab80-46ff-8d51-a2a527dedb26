version: "1.0"
type: WiseCloud::HMOS::SDK
fileId: com.huawei.hiaiengineforharmonyos_sdk
microServiceCode: com.huawei.hiaiengineforharmonyos
properties:
- key: PLUGIN_PARAMS
  value: []
  isEncode: false
- key: BacPbiVersion
  value: HiAIEngineForHarmonyOS 1.2.11.212
  isEncode: false
- key: publishOHPM
  value: "false"
  isEncode: false
buildTasks:
- independentRelease: true
  moduleType: hap
  moduleName: entry
  sourcePath: ./entry
- independentRelease: true
  moduleType: hap
  moduleName: support
  sourcePath: ./support
- independentRelease: true
  moduleType: har
  moduleName: pdkfull
  sourcePath: ./pdkfull
- independentRelease: true
  moduleType: har
  moduleName: wakeup
  sourcePath: ./wakeup
- independentRelease: true
  moduleType: har
  moduleName: aiAppBaseNetwork
  sourcePath: ./aiAppBaseNetwork
- independentRelease: true
  moduleType: har
  moduleName: aiTuneBase
  sourcePath: ./aiTuneBase
buildActions:
- buildActionType: MR<PERSON><PERSON>
  triggers:
  - triggerType: MR
- buildActionType: DailyBuild
  triggers:
  - triggerType: Daily
    timePolicy: 0 0 4 * * ?
