/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import distributedKVStore from '@ohos.data.distributedKVStore';
import common from '@ohos.app.ability.common';
import { BusinessError } from '@ohos.base';
import { AiLog } from '../logmanager/AiLog';
import { NetworkConstants } from '../networkmanager/NetworkConstants';
import { NetworkManager, DOMAIN } from '../networkmanager/NetworkManager';


/**
 * 提供安全的Kv存储工具
 * 优化：加密后存储
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
export namespace SafeKv {

    const  TAG: string = "SafeKv";
    /**
     * 将kv所在kvStore对象缓存以提高访问速度
     */
    let kvStore: distributedKVStore.SingleKVStore | null = null;

    /*
     * 获取一个数据库实例
     * */
    async function getStore(): Promise<distributedKVStore.SingleKVStore | null> {
        if (kvStore) {
            return kvStore;
        }
        let context: common.Context | null = NetworkManager.context;
        if(!context) {
            return null;
        }
        let kvManagerConfig: distributedKVStore.KVManagerConfig = {
            context: context,
            bundleName: context.applicationInfo.name,
        }
        try {
            let kvManager: distributedKVStore.KVManager = distributedKVStore.createKVManager(kvManagerConfig);
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, TAG, 'SafeKv getStore, creating KVManager success');
            let options: distributedKVStore.Options = {
                createIfMissing: true,
                encrypt: true,
                backup: false,
                autoSync: false,
                kvStoreType: distributedKVStore.KVStoreType.SINGLE_VERSION,
                securityLevel: distributedKVStore.SecurityLevel.S4,
            };
            kvStore = await kvManager.getKVStore('CommonSafeKv', options);
            if(!kvStore) {
                return null;
            }
            return kvStore;
        } catch (e) {
            let error = e as BusinessError
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, TAG, `SafeKv getStore, create KVManager.code failed: ${error.code}, message: ${error.message}`);
            return null;
        }
    }

    /**
     * 将数据持久化保存到KV
     *
     * @param key key
     * @param value value
     */
    export async function set(key: string, value: string): Promise<void> {
        let store = await getStore();
        if(store) {
            store.put(key, value);
        }
    }

    /**
     *  从持久化保存的KV中获取数据
     *
     * @param key key
     * @returns 数据
     */
    export async function get(key: string): Promise<string | undefined> {
        let store = await getStore();
        try {
            if(store) {
                return "" + await store.get(key);
            } else {
                return undefined
            }
        } catch (e) {
            return undefined;
        }
    }
}