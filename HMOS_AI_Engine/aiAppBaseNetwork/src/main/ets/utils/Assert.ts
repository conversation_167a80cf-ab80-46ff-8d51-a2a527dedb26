/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * 用于对参数或条件进行校验的工具，若校验失败则抛出异常
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
export class Assert {
    /**
     * 条件不满足则抛出错误
     * @param cond 判断条件
     * @param error 错误信息
     */
    public static requiresTrue(cond: boolean, error: ErrorEnum): void {
        if (!cond) {
            throw error;
        }
    }
}
/*
 * 错误码管理
 * */
export class ErrorEnum extends Error {

    // 初始化GRS 失败
    public static readonly FAILED_TO_INIT_GRS: number = 100000001;

    // 从GRS获取地址失败
    public static readonly FAILED_TO_GET_URL_BY_GRS: number = 100000002;

    // HTTP请求时，无法通过GRS，请求参数等途径获得URL
    public static readonly CANNOT_DETERMINE_URL: number = 100000003;

    // UCS请求凭据失败
    public static readonly UCS_GET_CREDENTIAL_FAIL: number = 100000004;

    // UCS请求签名数据失败
    public static readonly UCS_SIGN_DATA_FAIL: number = 100000005;

    // 请求ak2sk2失败
    public static readonly FAILED_TO_REQUEST_AK2SK2: number = 100000006;

    // HTTP请求失败
    public static readonly HTTP_REQUEST_FAIL: number = 100000007;

    /**
     网络模块初始化失败，找不到serverName
     */
    public static readonly NO_SERVER_NAME: number = 100000008;

    // 错误码
    public  readonly errorCode: number

    // 详细错误信息
    public  readonly errorMessage: string

    // 详细错误信息
    public  readonly cause?: object

    /**
     * 构造函数
     *
     * @param errorCode 错误码
     * @param errorMessage 错误描述
     * @param cause 引起错误的原因，可能是catch一个异常并抛出另一个异常会存在cause
     */
    constructor(errorCode: number, errorMessage: string, cause?: object) {
        super();
        this.errorCode = errorCode
        this.errorMessage = errorMessage
        this.cause = cause;
    }

    toString(): string {
        return `errorCode:${this.errorCode} errorMessage:${this.errorMessage} cause:{${this.cause}}`;
    }
}