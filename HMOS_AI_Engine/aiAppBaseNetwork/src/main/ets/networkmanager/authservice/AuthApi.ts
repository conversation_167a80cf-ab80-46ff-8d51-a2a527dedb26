/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

// import http from '@ohos.net.http';
import { ASK_MSG_RESPONSE_TIMEOUT } from '../MsgBusConfig';
import { UcsResult } from '../ucs/UcsResult';
import util from '@ohos.util';
import { ServerConfig } from '../ServerConfigManager';
import { AiLog } from '../../logmanager/AiLog';
import common from '@ohos.app.ability.common';
import { UcsApi } from '../ucs/UcsApi';
import bundleManager from '@ohos.bundle.bundleManager';
import deviceInfo from '@ohos.deviceInfo';
import { ErrorEnum } from '../../utils/Assert';
import { NetworkConstants } from '../NetworkConstants';
import { DOMAIN } from '../NetworkManager';
import {
    HttpClient,
    Request,
    RequestOption,
    Header,
    HttpClientOption,
    Response,
} from '@hms-network/url';

const TAG: string = 'AuthApi';

const NUM_ZERO: number = 0;

/**
 * 云侧返回的错误码定义，仅0000000000为成功，其它则失败
 */
const ERROR_CODE_SUCCESS: string = "0000000000";

/*
 * 获取ak2SK2接口返回报文
 * */
export interface GetAuthInfoResponse {
    /**
     * 云侧返回的接口错误码，详细见yaml接口说明
     * https://codehub-g.huawei.com/hag_system_api/HAGApi/files?ref=master&filePath=APIForApp%2Fuser-ai-suggestion-v1.yaml&isFile=true
     *
     * 详细错误码以上面文档为准
     * 0:成功
     * 其它：失败
     */
    code: string;

    /**
     * 如果接口调用失败，返回错误描述
     */
    desc?: string;

    /**
     * 端请求业务前，统一先向接入认证云获取AK2SK2
     * 接入认证云通过包名配置端侧需要访问哪些云，及每个云的AK2SK2内容
     */
    akSkList?: AuthInfo[]
}

/**
 * 一个云对应一套AK2SK2
 */
export interface AuthInfo {
    serverName: string;

    // 后期用于长期端云认证的ak
    ak: string;

    // 后期用于长期端云认证的sk
    sk: string;
}

/**
 * 从业务云请求AK2 SK2的HTTP请求任务
 * 1.使用UCS获取凭据
 * 2.使用UCS凭据对数据进行签名
 * 3.使用签名的数据到认证云获取鉴权信息(ak2sk2)
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
export class AuthApi {
    /**
     * 单例对象
     */
    private static instance: AuthApi
    /**
     * http客户端
     */
    private httpClient: HttpClient
    /**
     * 获取单例对象
     */
    public static getInstance(): AuthApi {
        if (!AuthApi.instance) {
            AuthApi.instance = new AuthApi()
        }
        return AuthApi.instance
    }

    /**
     * 向业务云请求AK2SK2
     *
     * @param context 应用上下文
     * @param url HTTP全路径,指向接入认证云
     * @param serverConfig 启动框架传入的服务参数
     * @returns 返回GetAuthInfoResponse, 包含AK2 SK2
     */
    public async getAuthInfo(context: common.Context, url: string, serverConfig: ServerConfig): Promise<GetAuthInfoResponse> {
        let option: RequestOption = await this.createRequestOption(context, serverConfig);
        option.setUrl(url);
        let client: HttpClient = this.getClient();
        let httpRequest: Request = new Request(client, option);
        httpRequest.init()
        try {
            let httpResponse: Response = await httpRequest.syncEnqueue();
            AiLog.info(DOMAIN, NetworkConstants.MODULE_ID, TAG, `getAuthInfo httpResponse success`, );
            return this.parseResponse(httpResponse);
        } catch (err) {
            AiLog.error(DOMAIN, NetworkConstants.MODULE_ID, TAG, `getAuthInfo httpResponse fail %{private}s `, JSON.stringify(err));
            throw new ErrorEnum(ErrorEnum.FAILED_TO_REQUEST_AK2SK2, `request server fail`, err);
        } finally {
            if (this.httpClient.getRequestSize() === NUM_ZERO) {
                this.httpClient.releaseHttpClient();
                this.httpClient = undefined;
            }
        }
    }

    /**
     * 从http返回结构中解析出ak2sk2
     *
     * @param httpResponse 待解析的http返回的结果
     * @returns 返回GetAuthInfoResponse, 包含AK2 SK2
     */
    private parseResponse(httpResponse: Response): GetAuthInfoResponse {
        try {
            let decoder = util.TextDecoder.create("utf-8", {ignoreBOM: true});
            let responseBodyString = decoder.decodeWithStream(httpResponse.content);
            let response: GetAuthInfoResponse = JSON.parse(responseBodyString) as GetAuthInfoResponse;
            if (response?.code !== ERROR_CODE_SUCCESS) {
              throw new ErrorEnum(ErrorEnum.FAILED_TO_REQUEST_AK2SK2, `parseResponse code=${response?.code}`)
            }
            return response;
        } catch (err) {
            throw new ErrorEnum(ErrorEnum.FAILED_TO_REQUEST_AK2SK2, `invalid response`, err)
        }
    }

    /**
     * 根据接口构建云侧请求
     *
     * @param context app 上下文
     * @param serverConfig 该云的配置，主要是鉴权相关配置
     * @returns 已构建好的header,其中包含使用UCS SDK签名的数据
     */
    private async createRequestOption(context: common.Context, serverConfig: ServerConfig): Promise<RequestOption> {
        let bundleFlags: bundleManager.BundleFlag = bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION;
        let myBundleInfo: bundleManager.BundleInfo = await bundleManager.getBundleInfoForSelf(bundleFlags);

        let traceId: string = util.generateRandomUUID();
        let contentToSign: string = `${new Date().getTime()}&${traceId}`
        let ucsResult: UcsResult;
        try {
            ucsResult = await new UcsApi().signData(context, serverConfig.authConfig.ucsAppName, contentToSign);
        } catch (err) {
            throw new ErrorEnum(ErrorEnum.FAILED_TO_REQUEST_AK2SK2, `signData`, err);
        }
        let requestOption: RequestOption = new RequestOption();
        requestOption.setMethod('POST')
        requestOption.addHeader(new Header("x-trace-id", traceId))
        requestOption.addHeader(new Header("x-device-id", deviceInfo.udid))
        requestOption.addHeader(new Header("x-pkg-name", myBundleInfo.appInfo?.name))
        requestOption.addHeader(new Header("x-app-version", myBundleInfo.versionName))
        requestOption.addHeader(new Header("x-access-key", ucsResult.ak))
        requestOption.addHeader(new Header("x-content2sign", contentToSign))
        requestOption.addHeader(new Header("x-signature", ucsResult.signatureBase64))

        return requestOption;
    }

    private getClient(): HttpClient {
        if (!this.httpClient) {
            let httpClientOption: HttpClientOption = new HttpClientOption();
            httpClientOption.setConnectTimeout(ASK_MSG_RESPONSE_TIMEOUT);
            httpClientOption.setReadTimeout(ASK_MSG_RESPONSE_TIMEOUT)
            this.httpClient = new HttpClient(httpClientOption)
            this.httpClient.init()
        }
        return this.httpClient
    }
}
