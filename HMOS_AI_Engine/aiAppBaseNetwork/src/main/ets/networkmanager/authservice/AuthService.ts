/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import common from '@ohos.app.ability.common';
import { SafeKv } from '../../utils/SafeKv';
import { DOMAIN } from '../NetworkManager';
import { AiLog } from '../../logmanager/AiLog';
import { ServerConfig } from '../ServerConfigManager';
import { UrlTool } from '../UrlTool';
import { AuthApi, AuthInfo, GetAuthInfoResponse } from './AuthApi';
import { NetworkConstants } from '../NetworkConstants';


/**
 * 获取UCS 鉴权信息接口
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
export class AuthService {
    private static TAG: string = "AuthService";
    private static AUTH_INFO_KV_PREFIX: string = "AuthInfo";
    private static authInfoCache: Map<string, AuthInfo> = new Map();

    /**
     * AuthService获取AkSk接口
     * 会先尝试从缓存获取，或成功，则直接返回（当前云侧返回的ak2sk2没有有效期
     * 若本地不存在，则尝试从接入认证云获取
     * @param context 上下文
     * @param serverConfig 上云请求的参数集
     */
    static async getAuthInfo(context: common.Context, serverConfig: ServerConfig): Promise<AuthInfo | null | undefined> {
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, 'start AuthService getAuthInfo');
        // 1.尝试从缓存获取
        let authInfo: AuthInfo | null | undefined = await AuthService.getAk2sk2FromCache(serverConfig.serverName);
        if ((authInfo?.ak?.length ?? 0) > 0) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, 'getAuthInfo from cache success');
            return authInfo;
        }
        // 2.缓存获取失败，从接入认证云侧获取并缓存在本地
        let getAuthInfoResponse: GetAuthInfoResponse | null = await AuthService.getAuthInfoForServer(context, serverConfig);
        AuthService.saveAuthInfo(getAuthInfoResponse);
        return AuthService.getAk2sk2FromCache(serverConfig.serverName);
    }

    /**
     * 从云侧获取AK2SK2
     * @param context App上下文
     * @param serverConfig 该云的配置
     * @returns 云侧返回的ak2sk2，会一次返回多条数据
     */
    private static async getAuthInfoForServer(context: common.Context, serverConfig: ServerConfig): Promise<GetAuthInfoResponse | null> {
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, "start getAuthInfoForServer");
        // UCS配置无效，无法获取AKSK2
        if (!serverConfig.authConfig?.ucsAppName) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG,
                'initByServerConfig failed, no auth config for serverName');
            return null;
        }

        // 先拼接请求接入认证云的完整接口地址
        let urlToGetAk2Sk2: string = await UrlTool.getUrl(context, serverConfig.authConfig?.authUrl,
            serverConfig.authConfig?.authGrsConfig ?? serverConfig.serviceGrsConfig);
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, "getAk2Sk2FromAuthServer get urlToGetAk2Sk2 success");
        // 请求接入认证云，获取鉴权信息，根据包名一次返回多条
        let getAuthInfoResponse: GetAuthInfoResponse = await AuthApi.getInstance().getAuthInfo(context, urlToGetAk2Sk2, serverConfig);
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, "getAk2Sk2FromAuthServer getAuthInfoResponse success");
        return getAuthInfoResponse;
    }

    /**
     * 获取某个微服务的鉴权AK2SK2
     *
     * @param serverName 微服务名
     * 某个微服务的鉴权AK2SK2
     */
    private static async getAk2sk2FromCache(serverName: string): Promise<AuthInfo | null | undefined> {
        try {
            if (AuthService.authInfoCache.has(serverName)) {
                AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, `getFromCache success`);
                return AuthService.authInfoCache.get(serverName);
            }
            let valueFromKv: string | undefined = await SafeKv.get(`${AuthService.AUTH_INFO_KV_PREFIX}-${serverName}`);

            if ((valueFromKv?.length ?? 0) === 0) {
                AiLog.warn(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, `getFromCache from kv fail`);
                return null;
            }
            let authInfo: AuthInfo | null = null;
            if (valueFromKv) {
                authInfo = JSON.parse(valueFromKv) as AuthInfo;
                AuthService.authInfoCache.set(serverName, authInfo);
                AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, `getFromCache from SafeKv success`);
            }
            return authInfo;
        } catch (err) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG, `getFromCache exception %{private}s`,
                JSON.stringify(err));
            return null;
        }
    }

    /**
     * 将云侧返回的鉴权信息保存到KV
     * Key：微服务标识，serviceName
     * value:访问该云的ak2,sk2,为加密存储
     *
     * @param getAuthInfoResponse
     */
    private static async saveAuthInfo(getAuthInfoResponse: GetAuthInfoResponse | null): Promise<void> {
        for (let authInfo of (getAuthInfoResponse?.akSkList ?? [])) {
            AuthService.authInfoCache.set(authInfo.serverName, authInfo);
            SafeKv.set(`${AuthService.AUTH_INFO_KV_PREFIX}-${authInfo.serverName}`, JSON.stringify(authInfo))
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, AuthService.TAG,
                `saveAuthInfo success`);
        }
    }
}