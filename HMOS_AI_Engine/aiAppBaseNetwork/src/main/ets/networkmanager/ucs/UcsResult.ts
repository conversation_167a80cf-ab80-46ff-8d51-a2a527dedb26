/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
/**
 * 通过UCSSDK请求端侧UCS返回 ak1 SK1
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
export class UcsResult {
    // 用于云侧去往UCS做验签的ak
    ak: string = '';
    // 原始数据（系统util能力随机生成的uuid）
    content2Sign: string = '';
    // 签名后数据，云侧验签用
    signatureBase64: string = '';
    // 认证结果, 默认未完成为-1
    resultCode: number = -1;
    // 结果信息
    resultMessage: string = '';
}