/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import common from '@ohos.app.ability.common';
import util from '@ohos.util';
import { UcsResult } from './UcsResult';
import { AiLog } from '../../logmanager/AiLog';
import ucsAppauth from '@hms-security/ucs-appauth';
import { ErrorEnum } from '../../utils/Assert';
import { NetworkConstants } from '../NetworkConstants';
import { DOMAIN } from '../NetworkManager';
import { GrsUtils } from '../grs/GrsUtils'
import { osAccount } from '@kit.BasicServicesKit';
import ucsAppauthSoft from '@hms-security/ucs-appauth-soft';

const TAG = 'UcsApi';

export class OuterGrsCapability implements ucsAppauth.GrsCapability {
    context: common.Context

    constructor(context: common.Context) {
        this.context = context
    }

    async getGrsUrl(serviceName: string, key: string): Promise<string> {
        await GrsUtils.init(this.context)
        return await GrsUtils.getGrsUrl(this.context, serviceName, key)
    }
}

/**
 * 对端侧UCS SDK API的封装
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
export class UcsApi {
    constructor() {
        ucsAppauthSoft.SoftClient.init();
    }

    /**
     * 访问UCS做数据签名
     *
     * @param context 上下文
     * @param appName 包名
     * @param content2Sign 待签名的数据
     * @returns 返回 UCS做验签的临时ak，签名前数据 以及 签名后数据
     */
    public async signData(context: common.Context, appName: string, content2Sign: string): Promise<UcsResult> {
        let credential: string = await this.getCredential(context, appName);
        try {
            let baseInfo: ucsAppauth.BaseInfo = new ucsAppauth.BaseInfo();
            baseInfo.serviceName = appName;
            baseInfo.context = context;
            baseInfo.grsCapability = new OuterGrsCapability(context)

            let client: ucsAppauth.AppauthClient = new ucsAppauth.AppauthClient(baseInfo);
            let encoder: util.TextEncoder = new util.TextEncoder;

            let req: ucsAppauth.SignReq = new ucsAppauth.SignReq();
            req.credential = credential;
            req.srcData = encoder.encodeInto(content2Sign); // content2Sign;
            req.signAlg = ucsAppauth.SignAlg.HMAC_SHA256;
            req.transId = util.generateRandomUUID();

            let result: ucsAppauth.SignResp = await client.signWithCredential(req);
            let base64: util.Base64Helper = new util.Base64Helper();
            let signResult: UcsResult = new UcsResult();
            signResult.ak = result.ak;
            signResult.resultCode = 0;
            signResult.resultMessage = "ucs result get success";
            signResult.content2Sign = content2Sign;
            signResult.signatureBase64 = base64.encodeToStringSync(result.signature);
            AiLog.info(DOMAIN, NetworkConstants.MODULE_ID, TAG, 'signData content2Sign signatureBase64 success');
            return signResult;
        } catch (err) {
            AiLog.error(DOMAIN, NetworkConstants.MODULE_ID, TAG, `signData error code: ${err} ${err}`);
            throw new ErrorEnum(ErrorEnum.UCS_SIGN_DATA_FAIL, `signData`, err);
        }
    }

    /**
     * 访问UCS获取后续签名所需的credential(凭证)
     *
     * @param context 上下文
     * @param appName 包名
     * @returns 返回获取到的credential，类型为string
     */
    private async getCredential(context: common.Context, appName: string): Promise<string> {
        try {
            let baseInfo: ucsAppauth.BaseInfo = new ucsAppauth.BaseInfo();
            baseInfo.serviceName = appName;
            baseInfo.context = context;
            baseInfo.serCountry = "CN";
            baseInfo.grsCapability = new OuterGrsCapability(context)
            let client: ucsAppauth.AppauthClient = new ucsAppauth.AppauthClient(baseInfo);
            let req: ucsAppauth.ApplyCredentialReq = new ucsAppauth.ApplyCredentialReq();
            req.transId = util.generateRandomUUID();
            let applyCredentialResp: ucsAppauth.ApplyCredentialResp = await client.applyCredential(req);
            AiLog.info(DOMAIN, NetworkConstants.MODULE_ID, TAG, '[UCS] getCredential success appName: %{public}s', appName);
            return applyCredentialResp.credential;
        } catch (err) {
            AiLog.error(DOMAIN, NetworkConstants.MODULE_ID, TAG, "[UCS]getCredential appName: %{public}s error %{public}s ",
              appName, `${err}`);
            throw new ErrorEnum(ErrorEnum.UCS_GET_CREDENTIAL_FAIL, `getCredential`, err);
        }
    }
}

