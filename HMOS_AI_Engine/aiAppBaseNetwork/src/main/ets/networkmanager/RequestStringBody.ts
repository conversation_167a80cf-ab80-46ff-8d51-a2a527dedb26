import { RequestBody, IWriteTo } from '@hms-network/url'
import util from '@ohos.util';

export class RequestStringBody extends RequestBody {
  constructor(content: string, contentType: string) {
    super();
    let strWriteTo: StrWriteTo = new StrWriteTo(content);
    super.writeTo = strWriteTo;
    super.contentLength = strWriteTo.getContentLength();
    super.contentType = contentType;
  }
}

class StrWriteTo extends IWriteTo {
  private buffer: Uint8Array;

  constructor(content: string) {
    super();
    const enc = new util.TextEncoder();
    this.buffer = enc.encodeInto(content);
    if (!this.buffer) {
      this.buffer = new Uint8Array()
    }
  }

  getBytesToWrite(): Uint8Array {
    return this.buffer;
  }

  isFinished(): boolean {
    return true;
  }

  getContentLength(): number {
    if (!this.buffer) {
      return 0;
    }
    return this.buffer.length;
  }
}