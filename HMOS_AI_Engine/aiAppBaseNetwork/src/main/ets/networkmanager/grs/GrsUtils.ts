import { BaseInfoType, GrsError, GrsFileUtil, GRSService } from '@hms-network/grs';
import common from '@ohos.app.ability.common';
import bundleManager from '@ohos.bundle.bundleManager';
import { ErrorEnum } from '../../utils/Assert';
import { AiLog } from '../../logmanager/AiLog';
import { NetworkConstants } from '../NetworkConstants';
import { DOMAIN } from '../NetworkManager';

const TAG: string = '[GrsUtils]';

/**
 * GRS接口的二次封装，便获于通过GRS获取地址
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
export namespace GrsUtils {
    let hasInit: boolean = false;
    /**
     * 初始化GRS配置，从应用包中将GRS配置文件拷贝到本地文件夹中
     * @param context 应用上下文
     */
    export async function init(context: common.Context): Promise<void> {
        if (hasInit) {
            return
        }
        hasInit = true;
        await GrsFileUtil.copyGrsConfigFile(context)
    }

    /**
     *  根据GRS配置获取云侧地址
     *
     * @param context 上下文
     * @param serviceName grs serviceName
     * @param key grs service key
     * @returns 服务器地址
     */
    export async function getGrsUrl(context: common.Context, serviceName: string, key: string): Promise<string> {
        let bundleFlags: bundleManager.BundleFlag = bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION;
        let myBundleInfo: bundleManager.BundleInfo = await bundleManager.getBundleInfoForSelf(bundleFlags);
        let grsDir: string = context.filesDir + "/grs_config";
        AiLog.info(DOMAIN, NetworkConstants.MODULE_ID, TAG, "getGrsUrl grsDir: %{public}s, myBundleInfo.appInfo name:%{public}s, label:%{public}s version:%{public}s",
            grsDir, myBundleInfo.appInfo.name, myBundleInfo.appInfo.label, myBundleInfo.versionName);
        let info: BaseInfoType = {
            "packageName": serviceName,
            "packageVersion": myBundleInfo.versionName,
            "productName": myBundleInfo.appInfo.name,
            "productLine": myBundleInfo.appInfo.name,
            "appName": "",
            "appVersion": myBundleInfo.versionName,
            "configureFilePath": grsDir,
            "regCountry": "",
            "serCountry": "",
            "issueCountry": "",
            "systemType": "",
            "systemVersion": "",
            "romVersion": "",
            "deviceModel": "",
            "countryCodeSource": "",
            "proxyUrl": "",
            "proxyUser": "",
            "passWord": ""
        };

        let grsInstance: GRSService = new GRSService();
        let initResult = await grsInstance.Init(info, context);
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, TAG, 'getGrsUrl grsInstance init code:%{public}d', initResult.code);
        if (initResult.code === GrsError.ERR_OK) {
            let grsResult = await grsInstance.SyncGetGrsUrl(serviceName, key)
            AiLog.info(DOMAIN, NetworkConstants.MODULE_ID, TAG, 'getGrsUrl grsInstance SyncGetGrsUrl code:%{public}d', initResult.code);
            if (grsResult.code === GrsError.ERR_OK) {
                return grsResult.message
            } else {
                return Promise.reject(new ErrorEnum(ErrorEnum.FAILED_TO_GET_URL_BY_GRS,
                  `{"code":"${initResult.code}", "message":"${initResult.message}"`));
            }
        } else {
            return Promise.reject(new ErrorEnum(ErrorEnum.FAILED_TO_INIT_GRS,
              `{"code":"${initResult.code}", "message":"${initResult.message}"`));
        }
    }
}