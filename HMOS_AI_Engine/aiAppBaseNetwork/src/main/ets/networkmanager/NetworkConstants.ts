/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/**
 * 网络管理相关常量定义
 */
export namespace NetworkConstants {

    // 网络管理相关代码统一使用的日志模块标识
    export const MODULE_ID: string = "NetworkManager";

    /**
     * 网络请求默认超时时间，超时时间确定逻辑
     * 1.优先使用HttpRequest.timeout
     * 2.若请求没有单独设置超时，使用ServerConfig.timeout
     * 3.若都没有配置，则默认10秒（DEFAULT_TIMEOUT）
     */
    export const DEFAULT_TIMEOUT: number = 10000;
}