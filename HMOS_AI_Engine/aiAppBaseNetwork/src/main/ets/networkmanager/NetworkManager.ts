/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import common from '@ohos.app.ability.common';
import { AiLog } from '../logmanager/AiLog';
import { ErrorEnum } from '../utils/Assert';
import { AiHttp, HttpMethod, HttpRequest, HttpResponse } from './AiHttp';
import { AuthInfo } from './authservice/AuthApi';
import { AuthService } from './authservice/AuthService';
import { GrsUtils } from './grs/GrsUtils';
import { NetworkConstants } from './NetworkConstants';
import { ServerConfig, ServerConfigManager } from './ServerConfigManager';
import { UrlTool } from './UrlTool';

export const DOMAIN: number = 0;


/**
 * 网络管理模块入口，规划提供初始化HTTP请求接口
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
export class NetworkManager {
    private static TAG: string = "NetworkManager";
    public static  context: common.Context | null = null;

    /**
     * 初始化网络管理模块
     *
     * 初始化网络管理模块，一般在进程启动时调用，主要包括：
     * 1.  通过业务GRS配置获取到业务请求的服务器地址
     * 2.  通过授权GRS配置获取接入认证云的服务器地址
     * 3.  根据授权配置请求接入认证云，获取AKSK2并加密保存到本地
     *
     * 额外说明：
     * 1.获取AKSK2时会自动获取应用包名、版本号、设备id(udid)等信息
     * 2.一次请求接入认证云会返回多个云的AK2SK2,本地按云服务标识缓存鉴权信息，存储到KV，加密级别S4
     * @param context App上下文
     * @param serverName 服务名，与ServerConfig中的serverName相对应，根据serverName查找对应的GRS和UCS配置进行初始化
     */
    public static async init(context: common.Context, domain: number, serverConfig: ServerConfig): Promise<void> {
        this.context = context;
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, `init ${serverConfig.serverName} start`);
        // 读取配置文件，且会将文件按微服务分别写入KV和内存，使于多进程访问和提高查询速度
        ServerConfigManager.cacheConfig(serverConfig);
        // 初始化GRS
        await GrsUtils.init(this.context);
        let server = await ServerConfigManager.getServerConfig(serverConfig.serverName);
        if (!server) {
            throw new ErrorEnum(ErrorEnum.NO_SERVER_NAME, `${server.serverName}`);
        }
        await NetworkManager.initByServerConfig(this.context, server);
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, `init ${server.serverName} finish`);
    }

    /**
     * 获取用于端云鉴权的AKSK，如果不用默认的aksk鉴权方式，比如参与签名的字段不一样或者为兼容老接口，可以使用该函数获取aksk后自行对数据进行签名
     *
     * @param serverName 服务名，参考 ServerConfigFile 结构的serviceName
     * @returns HTTP请求返回的报文信息
     */
    public static async getAkSk(serverName: string): Promise<AuthInfo | null | undefined> {
        let serverConfig: ServerConfig | null = await ServerConfigManager.getServerConfig(serverName);
        if (!serverConfig) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, 'NetworkManager getAkSk, no config for serverName');
            return null;
        }
        try {
            // 获取该云的鉴权AK2SK2
            let authInfo: AuthInfo | null | undefined = await AuthService.getAuthInfo(this.context, serverConfig);
            return authInfo;
        } catch (err) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, `NetworkManager getAkSk, getAuthInfo fail`,
                serverConfig.serverName, `${err}`);
            return null;
        }
    }

    /**
     * 发起默认的HTTP GET 请求，使用ServerConfig配置的鉴权方式超时时间等配置
     *
     * @param serverName 服务名，参考 ServerConfigFile 结构的serviceName
     * @param path http请求相对路径
     * @param headers HTTP头
     * @returns HTTP请求返回的报文信息
     */
    public static async httpGet(serverName: string, path: string, headers?: object): Promise<HttpResponse> {
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, 'start NetworkManager httpGet');
        let request: HttpRequest = {
            method: HttpMethod.GET,
            path: path,
            headers: headers,
        };
        return NetworkManager.makeHttpRequest(serverName, request);
    }

    /**
     * 发起默认的HTTP POST 请求，使用ServerConfig配置的鉴权方式超时时间等配置
     *
     * @param serverName 服务名，参考 ServerConfigFile 结构的serverName
     * @param path http请求相对路径
     * @param header HTTP头
     * @param body body
     * @returns HTTP请求返回的报文信息
     */
    public static async httpPost(serverName: string, path: string, headers?: object,
                                 body?: string | object | ArrayBuffer): Promise<HttpResponse> {
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, 'start NetworkManager httpPost');
        let request: HttpRequest = {
            method: HttpMethod.POST,
            path: path,
            body: body,
            headers: headers
        };
        return NetworkManager.makeHttpRequest(serverName, request);
    }

    /**
     * 发起自定义的HTTP请求，支持自定义鉴权类型，超时时间等
     * @param serverName 服务名，参考 ServerConfigFile 结构的serviceName
     * @param request 请求实体内容，包含请求参数和配置
     * @returns HTTP请求返回的报文信息
     */
    public static async makeHttpRequest(serverName: string, request: HttpRequest): Promise<HttpResponse> {
        return AiHttp.makeHttpRequest(serverName, request);
    }

    /**
     * 根据某个云配置初始
     * 1.根据GRS配置获取访问该云的HOST
     * 2.尝试获取AKSK2并缓存在KV
     *
     * @param context App上下文
     * @param serverConfig 云侧的配置信息
     */
    private static async initByServerConfig(context: common.Context, serverConfig: ServerConfig): Promise<void> {
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, `start NetworkManager initByServerConfig`);
        try {
            // 尝试通过GRS配置获取到HOST
            await UrlTool.initForServer(context, serverConfig);
        } catch (err) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, `NetworkManager initByServerConfig,
                try get host fail err:%{public}s`, `${err}`);
        }
        try {
            // 获取该云的鉴权AK2SK2
            let authInfo: AuthInfo | null | undefined = await AuthService.getAuthInfo(context, serverConfig);
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, `NetworkManager initByServerConfig getAuthInfo success`);
        } catch (err) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, NetworkManager.TAG, `NetworkManager initByServerConfig getAuthInfo fail`);
        }
    }
}
