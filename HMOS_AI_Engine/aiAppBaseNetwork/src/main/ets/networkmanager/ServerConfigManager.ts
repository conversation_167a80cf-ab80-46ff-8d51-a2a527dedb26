/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { AiLog } from '../logmanager/AiLog';
import { SafeKv } from '../utils/SafeKv';
import { DOMAIN } from '../networkmanager/NetworkManager'


/**
 * 云的GRS配置
 */
export interface GrsConfig {
    // grs服务名称
    readonly grsServiceName: string

    // grs密钥
    readonly grsKey?: string

    // 云侧接口地址,host存在则不会使用grsServiceName grsKey 通过GRS获取业务服务器地址
    readonly host?: string
}

/*
 * 鉴权类型
 * */
export enum AuthType {

    // 不需要做特殊的签名验证
    NO_AUTH = "noAuth",

    // 需使用UCS鉴权方式对参数做签名
    UCS = "ucs",

    // 需使用ak2sk2鉴权方式对参数做签名
    AK2SK2 = "ak2sk2"
}

/**
 * Ucs配置
 */
export interface AuthConfig {

    // 注册到UCS的应用名
    ucsAppName: string

    // 从业务云请求AK2SK2 的接口地址，仅支持相对路径
    authUrl: string

    // 若获取鉴权信息接口与业务接口不在同一个云，需要配置获取ak2sk2的接口所在云地址
    authGrsConfig: GrsConfig

    // 默认端云授权方式
    defaultAuthType?: AuthType;
}

/**
 * 具体某个云配置，服务标识，请求地址等
 */
export interface ServerConfig {

    // 给云分配的标识，供本地分析日志及缓存数据用，本地不重复即可
    serverName: string

    // 业务请求的Grs 配置，一般一个云对应一个GRS配置
    serviceGrsConfig?: GrsConfig

    // 端云鉴权相关配置
    authConfig: AuthConfig

    // 默认请求超时时间，单位秒
    timeout?: number;
}

/**
 * 云侧接口的配置管理工具
 * 主要职责是从配置文件中加载配置
 * 主线程传入配置文件路径后，需要保存下来供其它线程读取使用
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
export class ServerConfigManager {

    // KV中会存储其它数据，在key前面增加前缀以免重复
    private static CONFIG_KV_PREFIX: string = "ServerConfig";
    /**
     * 服务的配置缓存
     * key:server_config.json 中配置的serverName，标识某个云
     * value:该云的GRS及鉴权信息配置
     */
    private static configCache: Map<string, ServerConfig> = new Map();

    private static TAG: string = "ServerConfigManager";

    /**
     * 将配置缓存在KV及内存中，便于其它线程查询，提供当前线程的获取速度
     * @param serverConfigFile 配置内容
     */
    static cacheConfig(serverConfig: ServerConfig): void {
        if(serverConfig) {
            ServerConfigManager.configCache.set(serverConfig.serverName, serverConfig);
            SafeKv.set(`${ServerConfigManager.CONFIG_KV_PREFIX}-${serverConfig.serverName}`, JSON.stringify(serverConfig));
            AiLog.debug(DOMAIN, ServerConfigManager.TAG, ServerConfigManager.TAG, `cacheConfig success`);
        } else {
            AiLog.debug(DOMAIN, ServerConfigManager.TAG, ServerConfigManager.TAG, `cacheConfig failed`);
        }
    }

    /**
     * 根据微服务名获取该服务器的配置信息
     * 在本线程内存中缓存配置内容以供快速查询
     *
     * @param context 鸿蒙应用上下文
     * @param configFilePath 配置文件路径
     */
    public static async getServerConfig(serviceName: string): Promise<ServerConfig | null> {
        if (ServerConfigManager.configCache.has(serviceName)) {
            let server: ServerConfig = ServerConfigManager.configCache.get(serviceName) ?? null;
            AiLog.debug(DOMAIN, ServerConfigManager.TAG, ServerConfigManager.TAG, `getServerConfig from cache success`);
            return server;
        }
        AiLog.debug(DOMAIN, ServerConfigManager.TAG, ServerConfigManager.TAG, `getServerConfig from cache failed, cache no this serverName`);
        let configStrFromKv: string | undefined = await SafeKv.get(`${ServerConfigManager.CONFIG_KV_PREFIX}-${serviceName}`);
        if ((configStrFromKv?.length ?? 0) === 0 || !configStrFromKv) {
            AiLog.debug(DOMAIN, ServerConfigManager.TAG, ServerConfigManager.TAG, `getServerConfig from kv failed`);
            return null;
        }
        let serverConfig: ServerConfig = JSON.parse(configStrFromKv) as ServerConfig;
        if (!serverConfig) {
            AiLog.debug(DOMAIN, ServerConfigManager.TAG, ServerConfigManager.TAG, `getServerConfig kv parse failed`);
            return null;
        }
        AiLog.debug(DOMAIN, ServerConfigManager.TAG, ServerConfigManager.TAG, `getServerConfig from kv success`);
        ServerConfigManager.configCache.set(serviceName, serverConfig);
        return serverConfig;
    }
}