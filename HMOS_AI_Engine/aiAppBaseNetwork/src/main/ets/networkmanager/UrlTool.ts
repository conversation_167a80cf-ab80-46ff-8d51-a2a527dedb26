/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import common from '@ohos.app.ability.common';
import { AiLog } from '../logmanager/AiLog';
import { ErrorEnum } from '../utils/Assert';
import { GrsUtils } from './grs/GrsUtils';
import { NetworkConstants } from './NetworkConstants';
import { DOMAIN } from './NetworkManager';
import { GrsConfig, ServerConfig } from './ServerConfigManager';


/**
 * 用于拼接http接口请求地址的工具
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
export class UrlTool {
    private static TAG: string = "UrlTool";

    /**
     * 根据GRS配置，初始化地址获取
     *
     * @param context 应用上下文
     * @param serverConfig 服务器配置
     */
    public static async initForServer(context: common.Context, serverConfig: ServerConfig): Promise<void> {
        // 1.提前请求GRS地址,GRS自身带有缓存，提前获取可以加速后续业务请求时获取速度
        if ((serverConfig?.serviceGrsConfig?.grsServiceName?.length ?? 0) === 0) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, UrlTool.TAG,
              'UrlTool initForServer, no grs config for serverName');
            return;
        }
        let grsServiceName: string | null = serverConfig?.serviceGrsConfig?.grsServiceName ?? null;
        let grsKey: string | null = serverConfig?.serviceGrsConfig?.grsKey ?? null;
        if(!grsServiceName || !grsKey) {
            return;
        }
        let host: string = await GrsUtils.getGrsUrl(context, grsServiceName, grsKey);
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, UrlTool.TAG, 'UrlTool initForServer getGrsUrl %{public}s', host ? 'success' : 'failed');
        if ((host?.length ?? 0) === 0) {
            throw new ErrorEnum(ErrorEnum.FAILED_TO_INIT_GRS, `UrlTool initForServer host is empty`);
        }
    }

    /**
     * 根据GRS配置，url相对路径拼接完整接口请求地址
     *
     * @param context 应用上下文
     * @param path 接口相对路径，不包含host
     * @param grsConfig grs配置
     * @return 网络请求全路径
     */
    public static async getUrl(context: common.Context, path: string, grsConfig: GrsConfig): Promise<string> {
        // 2.使用serverConfig 中的host
        if (grsConfig.host && (grsConfig.host?.length ?? 0) > 0) {
            return UrlTool.combineUrl(grsConfig.host, path);
        }

        // 3.通过GRS获取host
        if (grsConfig?.grsServiceName && grsConfig?.grsKey) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, UrlTool.TAG, "UrlTool getUrl, try join with grs host");
            try {
                let host: string = await GrsUtils.getGrsUrl(context, grsConfig.grsServiceName,
                    grsConfig?.grsKey)
                AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, UrlTool.TAG, "UrlTool getUrl, grs host %{public}s", host ? "success" : "failed");
                return UrlTool.combineUrl(host, path);
            } catch (err) {
                return Promise.reject(new ErrorEnum(ErrorEnum.FAILED_TO_GET_URL_BY_GRS, `UrlTool getUrl`, err));
            }
        }
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, UrlTool.TAG, "UrlTool getRealUrl reject " + ErrorEnum.CANNOT_DETERMINE_URL);
        return Promise.reject(new ErrorEnum(ErrorEnum.CANNOT_DETERMINE_URL,
          `{"url":${path},"host":"${grsConfig.host}"}`));
    }

    /**
     * Creates a new URL by combining the specified URLs
     *
     * @param host The base URL
     * @param relativePath The relative URL
     * @return The combined URL
     */
    private static combineUrl(host: string, relativePath: string): string {
        let betterHost: string = host?.startsWith("http") ? host : `https://${host}`;
        // 将baseURL最后的斜杠和relativeURL最前面的斜杠去掉
        return `${betterHost.replace(new RegExp("\/+$"), '')}/${relativePath.replace(new RegExp("^\/+"), '')}`;
    }
}
