/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { AiLog } from '../logmanager/AiLog';
import { ErrorEnum } from '../utils/Assert';
import { ServerConfig, ServerConfigManager } from './ServerConfigManager';
import { UrlTool } from './UrlTool';
import { BusinessError } from '@ohos.base';
import { NetworkConstants } from './NetworkConstants';
import { NetworkManager, DOMAIN } from './NetworkManager';
import {
    HttpClient,
    Request,
    RequestOption,
    Header,
    HttpClientOption,
    RequestBody,
    IWriteTo,
    HttpClientRequestCallback,
    RequestFinishedInfo,
    Response,
    NetworkUrlCode
} from '@hms-network/url';
import { RequestStringBody } from './RequestStringBody'
import { util } from '@kit.ArkTS';


/**
 * 调用HTTP返回的内容
 */
export interface HttpResponse {

    // HTTP响应码
    responseCode: number
    // 返回内容
    result: string | object | ArrayBuffer
}

/*
 * http请求类型
 * */
export enum HttpMethod {
    // get 请求
    GET = 1,
    // post 请求
    POST = 2
}

export enum AuthType {
    // 不需要做特殊的签名验证
    NO_AUTH = "noAuth",

    // 需使用UCS鉴权方式对参数做签名
    UCS = "ucs"
}

/**
 * HTTP请求参数
 */
export interface HttpRequest {

    // 请求类型
    method: HttpMethod;

    // HTTP请求路径，仅支持传入相对路径，模块自动拼接HOST
    path: string;

    // HTTP请求头
    headers?: object;

    // HTTP请求消息体
    body?: string | object | ArrayBuffer;

    // 不传则默认使用ServerConfig中的默认超时时间，若ServerConfig也没有配置timeout,则超时时间为10秒
    timeout?: number;

    // 鉴权方式，如果为空会默认为AuthType.DEFAULT
    authType?: AuthType;
}

const TAG: string = "AiHttp";

/*
 * 请求主体
 *
 * <AUTHOR>
 * @since 2023-08-25
 * */
export class AiHttp {

    private static httpClient: HttpClient

    /**
     * 发起HTTP请求
     *
     * @param serviceName 标识云侧的一个服务
     * @param request 请求内容
     * @returns http 请求返回的报文
     */
    public static async makeHttpRequest(serverName: string, request: HttpRequest): Promise<HttpResponse> {
        AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, TAG, 'start AiHttp makeHttpRequest');
        // 获取该server的配置，其中包括GRS，鉴权信息等
        let serverConfig: ServerConfig | null = await ServerConfigManager.getServerConfig(serverName);

        if (!serverConfig) {
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, TAG, 'AiHttp makeHttpRequest getServerConfig, no config for serverName');
            throw new ErrorEnum(ErrorEnum.HTTP_REQUEST_FAIL, `no config for serverName`);
        }
        // 拼接完整请求URL，依赖GRS
        let url: string = "";
        try {
            url = await UrlTool.getUrl(NetworkManager.context, request.path, serverConfig.serviceGrsConfig);
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, TAG, 'AiHttp makeHttpRequest getUrl success');
        } catch (e) {
            throw new ErrorEnum(ErrorEnum.HTTP_REQUEST_FAIL, 'AiHttp makeHttpRequest getUrl', e);
        }
        // 当前暂不支持UCS鉴权
        if (request.authType === AuthType.UCS) {
            throw new ErrorEnum(ErrorEnum.HTTP_REQUEST_FAIL, 'AiHttp makeHttpRequest getUrl, not support authType ucs');
        }
        return AiHttp.sendRequest(url, serverConfig, request);
    }

    /**
     * 发送请求
     *
     * @param url 请求地址
     * @param serverConfig 服务器配置
     * @param request 请求实体
     * @returns HTTP返回报文
     */
    private static async sendRequest(url: string, serverConfig: ServerConfig, request: HttpRequest):
        Promise<HttpResponse> {
        let httpClient = AiHttp.getClient(serverConfig, request);
        let requestOptions: RequestOption = AiHttp.createRequestOption(request);
        requestOptions.setUrl(url);
        let httpRequest = new Request(httpClient, requestOptions)
        httpRequest.init();
        try {
            let oriResponse: Response = await httpRequest.syncEnqueue();
            AiLog.debug(DOMAIN, NetworkConstants.MODULE_ID, TAG, "AiHttp sendRequest, responseCode: %{public}d ",oriResponse.code);
            let httpResponse: HttpResponse = {
                responseCode: 0,
                result: ''
            };
            httpResponse.responseCode = oriResponse.code;
            let decoder = util.TextDecoder.create();
            let resultString = decoder.decodeWithStream(oriResponse.content)
            httpResponse.result = resultString
            return httpResponse
        } catch (e) {
            if (typeof (e as BusinessError).code === 'number') {
                throw new ErrorEnum(ErrorEnum.HTTP_REQUEST_FAIL,
                  `HTTP request BusinessError:${(e as BusinessError).code},${(e as BusinessError).data}`);
            }
            throw new ErrorEnum(ErrorEnum.HTTP_REQUEST_FAIL, `HTTP request error:${JSON.stringify(e)}`);
        }
    }

    /**
     * 根据请求参数构造鸿蒙http请求参数
     *
     * @param serverConfig 该云的参数配置
     * @param request 网络管理模块的http请求参数
     * @returns 底层http请求参数
     */
    private static createRequestOption(request: HttpRequest): RequestOption {
        let requestOptions = new RequestOption();
        requestOptions.setMethod(request.method === HttpMethod.GET ? 'GET' : 'POST')
        if (request.headers !== undefined && request.headers !== null) {
            let headerKeys = Object.getOwnPropertyNames(request.headers);
            headerKeys.forEach((key: string) => {
                let value = request.headers[key] as Object;
                requestOptions.addHeader(new Header(key, value.toString()))
            })
        }
        if (request.body !== undefined) {
            requestOptions.setRequestBody(new RequestStringBody(AiHttp.getBodyString(request.body), 'application/json'))
        }
        return requestOptions
    }

    /**
     * 获取http客户端
     *
     * @param serverConfig 该云的参数配置
     * @param request 网络管理模块的http请求参数
     * @returns HttpClient客户端
     */
    private static getClient(serverConfig: ServerConfig, request: HttpRequest): HttpClient {
        if (!AiHttp.httpClient) {
            let httpClientOption: HttpClientOption = new HttpClientOption();
            httpClientOption.setReadTimeout(request.timeout ?? serverConfig.timeout ?? NetworkConstants.DEFAULT_TIMEOUT)
            AiHttp.httpClient = new HttpClient(httpClientOption)
            AiHttp.httpClient.init()
        }
        return AiHttp.httpClient
    }

    /**
     * 获取http请求体字符串
     *
     * @param body 请求体
     * @returns string格式的请求体
     */
    private static getBodyString(body: string | object | ArrayBuffer): string {
        if (typeof body === "string") {
            return body;
        } else if(body instanceof ArrayBuffer) {
            let decoder = util.TextDecoder.create("utf-8");
            let bodyString = decoder.decodeWithStream(new Uint8Array(body))
            return bodyString
        } else {
            return JSON.stringify(body)
        }
    }
}