/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import hilog from '@ohos.hilog';

/**
 * 日志打印工具统一封装，不可直接调用底层的日志打印接口
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
export class AiLog {
    /**
     * 打印debug级别日志，该级别日志仅打印hilog
     *
     * @param domain 日志对应的领域标识，范围是0x0~0xFFFF，在应用内根据需要自定义划分。
     *   使用命令 "hilog -D domain1,domain2,domain3" 只显示指定domain的日志
     *   使用命令 "hilog -D ^domain1,domain2,domain3" 排除指定domain的日志
     * @param moduleName 业务模块名，与BaseLogicModule相对应
     * @param tag 指定日志标识，可以为任意字符串，建议用于标识调用所在的类或者业务行为。
     *   使用命令 "hilog -T tag1,tag2,tag3" 只显示指定tag的日志
     *   使用命令 "hilog -T ^tag1,tag2,tag3" 排除指定tag的日志
     * @param format 格式字符串，用于日志的格式化输出。格式字符串中可以设置多个参数，参数需要包含参数类型、隐私标识。
     *   隐私标识分为{public}和{private}，缺省为{private}。标识{public}的内容明文输出，标识{private}的内容以<private>过滤回显。
     *   使用命令  "hilog -p on/off" 命令控制private参数是否打印
     * @param args 与格式字符串format对应的可变长度参数列表。参数数目、参数类型必须与格式字符串中的标识一一对应。
     */
    public static debug(domain: number, moduleName: string, tag: string, format: string, ...args: (string|number|object)[]): void {
        hilog.debug(domain, tag, `${moduleName}-${format}`, ...args);
    }

    /**
     * 打印debug级别日志，该级别日志仅打印hilog
     *
     * @param domain 日志对应的领域标识，范围是0x0~0xFFFF，在应用内根据需要自定义划分。
     *   使用命令 "hilog -D domain1,domain2,domain3" 只显示指定domain的日志
     *   使用命令 "hilog -D ^domain1,domain2,domain3" 排除指定domain的日志
     * @param moduleName 业务模块名，与BaseLogicModule相对应
     * @param tag 指定日志标识，可以为任意字符串，建议用于标识调用所在的类或者业务行为。
     *   使用命令 "hilog -T tag1,tag2,tag3" 只显示指定tag的日志
     *   使用命令 "hilog -T ^tag1,tag2,tag3" 排除指定tag的日志
     * @param format 格式字符串，用于日志的格式化输出。格式字符串中可以设置多个参数，参数需要包含参数类型、隐私标识。
     *   隐私标识分为{public}和{private}，缺省为{private}。标识{public}的内容明文输出，标识{private}的内容以<private>过滤回显。
     *   使用命令  "hilog -p on/off" 命令控制private参数是否打印
     * @param args 与格式字符串format对应的可变长度参数列表。参数数目、参数类型必须与格式字符串中的标识一一对应。
     */
    public static info(domain: number, moduleName: string, tag: string, format: string, ...args: (string|number|object)[]): void {
        hilog.info(domain, tag, `${moduleName}-${format}`, ...args);
    }

    /**
     * 打印warn级别日志,该级别的日志会打印hilog并自动写入日志文件
     *
     * @param domain 日志对应的领域标识，范围是0x0~0xFFFF，在应用内根据需要自定义划分。
     *   使用命令 "hilog -D domain1,domain2,domain3" 只显示指定domain的日志
     *   使用命令 "hilog -D ^domain1,domain2,domain3" 排除指定domain的日志
     * @param moduleName 业务模块名，与BaseLogicModule相对应
     * @param tag 指定日志标识，可以为任意字符串，建议用于标识调用所在的类或者业务行为。
     *   使用命令 "hilog -T tag1,tag2,tag3" 只显示指定tag的日志
     *   使用命令 "hilog -T ^tag1,tag2,tag3" 排除指定tag的日志
     * @param format 格式字符串，用于日志的格式化输出。格式字符串中可以设置多个参数，参数需要包含参数类型、隐私标识。
     *   隐私标识分为{public}和{private}，缺省为{private}。标识{public}的内容明文输出，标识{private}的内容以<private>过滤回显。
     *   使用命令  "hilog -p on/off" 命令控制private参数是否打印
     * @param args 与格式字符串format对应的可变长度参数列表。参数数目、参数类型必须与格式字符串中的标识一一对应。
     */
    public static warn(domain: number, moduleName: string, tag: string, format: string, ...args: (string|number|object)[]): void {
        hilog.warn(domain, tag, `${moduleName}-${format}`, ...args);
        // 一期先实现hilog日志打印，第二期增加文件日志及日志上报功能
    }

    /**
     * 打印error级别日志,该级别的日志会打印hilog并自动写入日志文件
     *
     * @param domain 日志对应的领域标识，范围是0x0~0xFFFF，在应用内根据需要自定义划分。
     *   使用命令 "hilog -D domain1,domain2,domain3" 只显示指定domain的日志
     *   使用命令 "hilog -D ^domain1,domain2,domain3" 排除指定domain的日志
     * @param moduleName 业务模块名，与BaseLogicModule相对应
     * @param tag 指定日志标识，可以为任意字符串，建议用于标识调用所在的类或者业务行为。
     *   使用命令 "hilog -T tag1,tag2,tag3" 只显示指定tag的日志
     *   使用命令 "hilog -T ^tag1,tag2,tag3" 排除指定tag的日志
     * @param format 格式字符串，用于日志的格式化输出。格式字符串中可以设置多个参数，参数需要包含参数类型、隐私标识。
     *   隐私标识分为{public}和{private}，缺省为{private}。标识{public}的内容明文输出，标识{private}的内容以<private>过滤回显。
     *   使用命令  "hilog -p on/off" 命令控制private参数是否打印
     * @param args 与格式字符串format对应的可变长度参数列表。参数数目、参数类型必须与格式字符串中的标识一一对应。
     */
    public static error(domain: number, moduleName: string, tag: string, format: string, ...args: (string|number|object)[]): void {
        hilog.error(domain, tag, `${moduleName}-${format}`, ...args);
        // 一期先实现hilog日志打印，后续增加日志文件打印
    }

    /**
     * 打印fatal级别日志,该级别的日志会打印hilog并自动写入日志文件
     *
     * @param domain 日志对应的领域标识，范围是0x0~0xFFFF，在应用内根据需要自定义划分。
     *   使用命令 "hilog -D domain1,domain2,domain3" 只显示指定domain的日志
     *   使用命令 "hilog -D ^domain1,domain2,domain3" 排除指定domain的日志
     * @param moduleName 业务模块名，与BaseLogicModule相对应
     * @param tag 指定日志标识，可以为任意字符串，建议用于标识调用所在的类或者业务行为。
     *   使用命令 "hilog -T tag1,tag2,tag3" 只显示指定tag的日志
     *   使用命令 "hilog -T ^tag1,tag2,tag3" 排除指定tag的日志
     * @param format 格式字符串，用于日志的格式化输出。格式字符串中可以设置多个参数，参数需要包含参数类型、隐私标识。
     *   隐私标识分为{public}和{private}，缺省为{private}。标识{public}的内容明文输出，标识{private}的内容以<private>过滤回显。
     *   使用命令  "hilog -p on/off" 命令控制private参数是否打印
     * @param args 与格式字符串format对应的可变长度参数列表。参数数目、参数类型必须与格式字符串中的标识一一对应。
     */
    public static fatal(domain: number, moduleName: string, tag: string, format: string, ...args: (string|number|object)[]): void {
        hilog.fatal(domain, tag, `${moduleName}-${format}`, ...args);
        // 一期先实现hilog日志打印，后续增加日志文件打印
    }
}