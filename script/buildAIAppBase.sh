#!/bin/bash
 
set -ex
set -o pipefail
 
export JAVA_HOME=/opt/buildtools/hwjdk-11
export PATH=${JAVA_HOME}/bin:${PATH}
export OHPM=/opt/buildtools/ohpm-1.4.4
export PATH=${OHPM}/bin:${PATH}
 
SCRIPT_DIR=$(cd $(dirname $0);pwd)
 
openharmony_sdk_path=/opt/buildtools/openharmony-********/sdk-full
nodejs_path=/opt/buildtools/node.js/bin
native_path=/opt/buildtools/openharmony-*******/sdk-full/native
hwsdk_dir=/opt/buildtools/harmonyos-NEXTSDK5.0.0.36/hwsdk-full

 
project_root_path=HMOS_AI_Engine
COMMON_UTILS_DIR=HMOS_AI_Engine
MODULE_DIR=HMOS_AI_Engine/aiAppBase
PACKAGE_PRE=HMOS_AI_Engine
 
ls
 
basefunctionBuild(){
    if [[  ${WORKSPACE} == "" ]]; then
        echo "this is in local!"
    else
        echo "this is in remote!"
        cd ${SCRIPT_DIR}/../${COMMON_UTILS_DIR}
		
		echo "print Path"
		pwd
				
        chmod +x ../script/appscope_replace.sh
        source ../script/appscope_replace.sh
		appscope_replace ${WORKSPACE}
        
		echo "print Path ok"
		ls
		
		#手动配置流水线上local.properties里面环境变量路径，需要自己修改。  
	    rm -rf local.properties
	    touch local.properties
	    chmod 777 local.properties
		
		echo "sdk.dir=$openharmony_sdk_path" > ./local.properties
        echo "nodejs.dir=$nodejs_path" >> ./local.properties
        echo "native.dir=$native_path" >> ./local.properties
		echo "hwsdk.dir=$hwsdk_dir" >> ./local.properties
	
	    #需要先删除主工程的依赖，在执行下载最新的
		if [ -d "oh_modules" ]; then
        echo "delete old oh_modules dir"
        rm -rf oh_modules
        fi
        ohpm install
		echo "main project oh_modules install complete"
    fi
}
	
functionBuild(){
    if [[  ${WORKSPACE} == "" ]]; then
        echo "this is in local!"
    else
        echo "this is in remote!"
        cd ${SCRIPT_DIR}/../${MODULE_DIR}
 
		chmod 777 oh-package.json5
		
		if [[  $sdkVersion == "" ]]; then
			export SDK_VERSION='1.0.0-245'
		else
			export SDK_VERSION=${sdkVersion}
		fi
		
		sed -i  "0,/\"version\":.*$/s//\"version\":\"${SDK_VERSION}\",/"  oh-package.json5 #替换打包版本
		ohpm install #安装Module所需依赖
		
        #生成pdk har包
		cd ../
        ls
		chmod +x hvigorw
		./hvigorw --mode module -p product -p module=aiAppBase -p debuggable=false assembleHar
		
		#解析项目的依赖树，然后供构建元数据使用，在完成编译后执行该命令
		generate build --target $WORKSPACE/$codeRootDir
        echo "the pdklite compilation is complete"
 
        cd $SCRIPT_DIR/../${MODULE_DIR}/build/default/cache/default/default@PackageHar
		sed -i  "0,/\"version\":.*$/s//\"version\":\"${SDK_VERSION}\",/"  oh-package.json5 #动态赋值出包版本
		
	if [[  $releaseVersion == "" ]]; then
        echo "this is snapshot version!"
        pushd $SCRIPT_DIR/../${MODULE_DIR}/build/default/cache/default/default@PackageHar
		# MR发布时需要修改两个'1.0.0-200'版本号，取消下面的注释（npm仓发布）
		# ohpm仓发布
		# cd $SCRIPT_DIR/../${MODULE_DIR}/build/default/outputs/default/
		# ohpm publish aiAppBase.har
        popd
    else
		echo "this is release version!"
		cd $SCRIPT_DIR/../${MODULE_DIR}/build/default/outputs/default/
		ohpm publish aiAppBase.har
		echo "push sdk is complete"	
		fi	
    fi
}
 
 
basefunctionBuild
functionBuild
 
if [[ $releaseVersion == "" ]]; then
 
   echo buildVersion=${ENV_PIPELINE_STARTTIME}> ${WORKSPACE}/buildInfo.properties
 
else
 
  echo buildVersion=${releaseVersion}"-"${ENV_PIPELINE_STARTTIME}> ${WORKSPACE}/buildInfo.properties
 
fi
 
 
customPATH="package,${project_root_path}/HMOS_AI_Engine/aiAppBase/build/default/outputs/default/aiAppBase.har"
echo customPATH=${customPATH}>> ${WORKSPACE}/buildInfo.properties