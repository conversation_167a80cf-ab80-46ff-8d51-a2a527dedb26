#!/bin/bash

set -ex
set -o pipefail

appscope_replace()
{
    echo "appscope replace"
    if [ -d "${1}/mybuidtemp" ]; then
       rm -rf ${1}/mybuidtemp 
    fi 
    mkdir -p ${1}/mybuidtemp
    if [ -d "${1}/HMOS_AI_Engine/HMOS_AI_Engine/AppScope" ]; then
        echo "delete origin AppScope"
        rm -rf ${1}/HMOS_AI_Engine/HMOS_AI_Engine/AppScope
        ls ${1}/HMOS_AI_Engine/HMOS_AI_Engine
    fi    
    git clone https://codehub-dg-g.huawei.com/CBG_CloudService_Assemble/AI_and_All_Scenario/HiAIEngine-Multihap-Forharmony.git -b master --depth=1 ${1}/mybuidtemp
    cp -r ${1}/mybuidtemp/AppScope ${1}/HMOS_AI_Engine/HMOS_AI_Engine/
}