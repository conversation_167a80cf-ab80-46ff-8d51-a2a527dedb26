#!/bin/bash

set -ex
set -o pipefail

export JAVA_HOME=/opt/buildtools/hwjdk-11
export PATH=${JAVA_HOME}/bin:${PATH}
export OHPM=/opt/buildtools/ohpm-1.4.4
export PATH=${OHPM}/bin:${PATH}

SCRIPT_DIR=$(cd $(dirname $0);pwd)


openharmony_sdk_path=/opt/buildtools/openharmony-*********/sdk-full
nodejs_path=/opt/buildtools/node.js/bin
native_path=/opt/buildtools/openharmony-*******/sdk-full/native
hwsdk_dir=/opt/buildtools/harmonyos-NEXTSDK5.0.0.36/hwsdk-full
sign_jar=/opt/buildtools/harmonyos-OH4.1.3.5-HMSCORE4.1.0.22/hwsdk-full/openharmony/11/toolchains/lib/hap-sign-tool.jar

project_root_path=HMOS_AI_Engine
COMMON_UTILS_DIR=HMOS_AI_Engine
MODULE_DIR=HMOS_AI_Engine/support
PACKAGE_PRE=HMOS_AI_Engine

ls

basefunctionBuild(){
    if [[  ${WORKSPACE} == "" ]]; then
        echo "this is in local!"
    else
        echo "this is in remote!"
        cd ${SCRIPT_DIR}/../${COMMON_UTILS_DIR}
		
		echo "print Path"
		pwd
		
        chmod +x ../script/appscope_replace.sh
        source ../script/appscope_replace.sh
		appscope_replace ${WORKSPACE}
        
		echo "print Path ok"
		ls
		
		#手动配置流水线上local.properties里面环境变量路径，需要自己修改。  
	    rm -rf local.properties
	    touch local.properties
	    chmod 777 local.properties
		
		echo "sdk.dir=$openharmony_sdk_path" > ./local.properties
        echo "nodejs.dir=$nodejs_path" >> ./local.properties
        echo "native.dir=$native_path" >> ./local.properties
        echo "hwsdk.dir=$hwsdk_dir" >> ./local.properties

	
	    #需要先删除主工程的依赖，在执行下载最新的
		if [ -d "node_modules" ]; then
        echo "delete old node_modules dir"
        rm -rf node_modules
        fi
        ohpm install
		echo "main project node_modules install complete"
    fi
}
	
functionBuild(){
    if [[  ${WORKSPACE} == "" ]]; then
        echo "this is in local!"
    else
        echo "this is in remote!"
        cd ${SCRIPT_DIR}/../${MODULE_DIR}

		chmod 777 oh-package.json5
		
		if [[  $sdkVersion == "" ]]; then
			export SDK_VERSION='1.0.0'
		else
			export SDK_VERSION=${sdkVersion}
		fi
		
		sed -i  "0,/\"version\":.*$/s//\"version\":\"${SDK_VERSION}\",/"  oh-package.json5 #替换打包版本
		
        #生成pdk har包
        cd ../support
        ls
		
        ohpm install #安装entry Module所需依赖
        cd ../ #进入仓库目录
        mkdir output
        ls
		which node
		echo "compiling the hap"
        
        #cd ./${COMMON_UTILS_DIR}
        ls 
        chmod +x hvigorw
		./hvigorw --mode module -p module=support assembleHap -p debuggable=false -p buildMode=release
		# node ./node_modules/@ohos/hvigor/bin/hvigor.js --mode module -p module=support assembleHap
		#解析项目的依赖树，然后供构建元数据使用，在完成编译后执行该命令
		generate build --target $WORKSPACE/$codeRootDir

        echo "the hap compilation is complete"

        #在线签名
        hap-sign-tool $sign_jar "HiAIEngineForHarmonyOS" ./hm_sign/106149release.p7b ./support/build/default/outputs/default/support-entry-default-unsigned.hap ./support/build/default/outputs/default/support-entry-default-signed.hap ${HW_USERNAME} ${HW_PASSWORD}

        cd ./support/build/default/outputs/default/
        ls
        if [ -f "support-entry-default-signed.hap" ]; then

	        echo "build success"

        else

	        echo "build failed"

	        exit 1
        fi
        cp ./support-entry-default-signed.hap ../../../../../output
        cd ../../../../../output
        ls

    fi
}


basefunctionBuild
functionBuild

if [[ $releaseVersion == "" ]]; then

   echo buildVersion=${ENV_PIPELINE_STARTTIME}> ${WORKSPACE}/buildInfo.properties

else

  echo buildVersion=${releaseVersion}"-"${ENV_PIPELINE_STARTTIME}> ${WORKSPACE}/buildInfo.properties

fi


customPATH="package,${project_root_path}/HMOS_AI_Engine/support/build/default/outputs/default/support-entry-default-signed.hap"

echo customPATH=${customPATH}>> ${WORKSPACE}/buildInfo.properties