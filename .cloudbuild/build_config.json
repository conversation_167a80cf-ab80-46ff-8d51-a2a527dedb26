{"buildEnvPrepare": {"JDK_VERSION": "17", "OHPM_VERSION": "5.0.2", "SDK_VERSION": "NEXTSDK5.1.0.48"}, "modifyPackName": {"archiveType": "6", "modifyName": [{"entry": "entry-default-signed"}, {"support": "support-entry-default-signed"}]}, "modules": [{"path": "./entry", "name": "entry", "type": "hap"}, {"path": "./pdkfull", "name": "pdkfull", "type": "har"}, {"path": "./support", "name": "support", "type": "hap"}, {"path": "./aiAppBase", "name": "aiAppBase", "type": "har"}, {"path": "./aiAppBaseConfig", "name": "aiAppBaseConfig", "type": "har"}, {"path": "./aiBaseSdk", "name": "aiBaseSdk", "type": "har"}, {"path": "./aiAppBaseNetwork", "name": "aiAppBaseNetwork", "type": "har"}, {"path": "./aiTuneBase", "name": "aiTuneBase", "type": "har"}, {"path": "./wakeup", "name": "wakeup", "type": "har"}], "hvigorVersion": "5.1.0.230"}