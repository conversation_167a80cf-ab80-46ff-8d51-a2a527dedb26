version: '2.0'
env:
  label: ${buildLabel}
params:
  - name: pipelineJobName
    value: ${ENV_PIPELINE_JOB_NAME}
  - name: codeRootDir 
    value: ${serviceName}
  - name: scriptPath  
    value: script/build.sh
  - name: projectPath
    value: 
  - name: excludeCfgFile
    value: 
  - name: includeCfgFile
    value:
steps:
  PRE_BUILD:
    - checkout:
        path: ${codeRootDir}
    - template: 
        url: https://codehub-dg-g.huawei.com/cbg_cloud_devops_tools/CSP_Script.git
        path: clouddragon/build2.0/build_template/service/pre_build_template.yml
        branch: Common
  BUILD:
    - template: 
        url: https://codehub-dg-g.huawei.com/cbg_cloud_devops_tools/CSP_Script.git
        path: clouddragon/build2.0/build_template/service/build_template_ohpm.yml
        branch: Common
  POST_BUILD:
    - template: 
        url: https://codehub-dg-g.huawei.com/cbg_cloud_devops_tools/CSP_Script.git
        path: clouddragon/build2.0/build_template/service/post_build_template_ohpm.yml
        branch: Common
